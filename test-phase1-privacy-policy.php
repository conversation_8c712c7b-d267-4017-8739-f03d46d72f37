<?php
/**
 * Test Phase 1: Privacy Policy Implementation
 * 
 * Verifies the Privacy Policy page creation and functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>ChatGABI Phase 1 - Privacy Policy Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".test-container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; font-weight: bold; }";
echo ".error { color: #e74c3c; font-weight: bold; }";
echo ".warning { color: #f39c12; font-weight: bold; }";
echo ".info { color: #3498db; font-weight: bold; }";
echo ".test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; background: #f8f9fa; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "</style>";
echo "</head><body>";

echo "<div class='test-container'>";
echo "<h1>🔒 ChatGABI Phase 1: Privacy Policy Test</h1>";
echo "<p><strong>Test Time:</strong> " . current_time('F j, Y g:i A') . "</p>";

$tests_passed = 0;
$tests_total = 0;

// Test 1: Check if Privacy Policy page exists
echo "<div class='test-section'>";
echo "<h2>Test 1: Privacy Policy Page Existence</h2>";
$tests_total++;

$privacy_page = get_page_by_path('privacy-policy');
if ($privacy_page) {
    echo "<p class='success'>✅ Privacy Policy page exists (ID: {$privacy_page->ID})</p>";
    echo "<p class='info'>📄 Title: " . $privacy_page->post_title . "</p>";
    echo "<p class='info'>🔗 URL: <a href='" . get_permalink($privacy_page->ID) . "' target='_blank'>" . get_permalink($privacy_page->ID) . "</a></p>";
    $tests_passed++;
} else {
    echo "<p class='error'>❌ Privacy Policy page does not exist</p>";
    echo "<p class='info'>🔧 Attempting to create Privacy Policy page...</p>";
    
    // Try to create the page
    if (function_exists('chatgabi_create_phase1_essential_pages')) {
        chatgabi_create_phase1_essential_pages();
        
        // Check again
        $privacy_page = get_page_by_path('privacy-policy');
        if ($privacy_page) {
            echo "<p class='success'>✅ Privacy Policy page created successfully!</p>";
            echo "<p class='info'>🔗 URL: <a href='" . get_permalink($privacy_page->ID) . "' target='_blank'>" . get_permalink($privacy_page->ID) . "</a></p>";
            $tests_passed++;
        } else {
            echo "<p class='error'>❌ Failed to create Privacy Policy page</p>";
        }
    } else {
        echo "<p class='error'>❌ chatgabi_create_phase1_essential_pages function not found</p>";
    }
}
echo "</div>";

// Test 2: Check template file existence
echo "<div class='test-section'>";
echo "<h2>Test 2: Template File Verification</h2>";
$tests_total++;

$template_file = get_template_directory() . '/page-privacy-policy.php';
if (file_exists($template_file)) {
    echo "<p class='success'>✅ Template file exists: page-privacy-policy.php</p>";
    echo "<p class='info'>📁 Path: " . $template_file . "</p>";
    echo "<p class='info'>📊 File size: " . number_format(filesize($template_file)) . " bytes</p>";
    $tests_passed++;
} else {
    echo "<p class='error'>❌ Template file missing: page-privacy-policy.php</p>";
}
echo "</div>";

// Test 3: Check page template assignment
echo "<div class='test-section'>";
echo "<h2>Test 3: Page Template Assignment</h2>";
$tests_total++;

if ($privacy_page) {
    $page_template = get_page_template_slug($privacy_page->ID);
    if ($page_template === 'page-privacy-policy.php') {
        echo "<p class='success'>✅ Correct template assigned: {$page_template}</p>";
        $tests_passed++;
    } else {
        echo "<p class='warning'>⚠️ Template assignment: " . ($page_template ? $page_template : 'default') . "</p>";
        echo "<p class='info'>🔧 Attempting to fix template assignment...</p>";
        
        update_post_meta($privacy_page->ID, '_wp_page_template', 'page-privacy-policy.php');
        
        $updated_template = get_page_template_slug($privacy_page->ID);
        if ($updated_template === 'page-privacy-policy.php') {
            echo "<p class='success'>✅ Template assignment fixed!</p>";
            $tests_passed++;
        } else {
            echo "<p class='error'>❌ Failed to fix template assignment</p>";
        }
    }
} else {
    echo "<p class='error'>❌ Cannot test template assignment - page does not exist</p>";
}
echo "</div>";

// Test 4: Check essential functions
echo "<div class='test-section'>";
echo "<h2>Test 4: Essential Functions Check</h2>";
$tests_total++;

$required_functions = array(
    'chatgabi_create_phase1_essential_pages',
    'get_page_by_path',
    'wp_insert_post',
    'update_post_meta'
);

$missing_functions = array();
foreach ($required_functions as $function) {
    if (function_exists($function)) {
        echo "<p class='success'>✅ Function exists: {$function}()</p>";
    } else {
        echo "<p class='error'>❌ Function missing: {$function}()</p>";
        $missing_functions[] = $function;
    }
}

if (empty($missing_functions)) {
    echo "<p class='success'>✅ All required functions are available</p>";
    $tests_passed++;
} else {
    echo "<p class='error'>❌ Missing " . count($missing_functions) . " required functions</p>";
}
echo "</div>";

// Test 5: Check page content and structure
echo "<div class='test-section'>";
echo "<h2>Test 5: Page Content Structure</h2>";
$tests_total++;

if ($privacy_page && file_exists($template_file)) {
    $template_content = file_get_contents($template_file);
    
    $required_sections = array(
        'Introduction' => 'Introduction',
        'Information We Collect' => 'Information We Collect',
        'How We Use Your Information' => 'How We Use Your Information',
        'AI and Third-Party Services' => 'AI and Third-Party Services',
        'African Data Protection Compliance' => 'African Data Protection Compliance',
        'Your Rights' => 'Your Rights',
        'Contact Information' => 'Contact Information'
    );
    
    $missing_sections = array();
    foreach ($required_sections as $section => $search_text) {
        if (strpos($template_content, $search_text) !== false) {
            echo "<p class='success'>✅ Section found: {$section}</p>";
        } else {
            echo "<p class='error'>❌ Section missing: {$section}</p>";
            $missing_sections[] = $section;
        }
    }
    
    if (empty($missing_sections)) {
        echo "<p class='success'>✅ All required sections are present</p>";
        $tests_passed++;
    } else {
        echo "<p class='error'>❌ Missing " . count($missing_sections) . " required sections</p>";
    }
} else {
    echo "<p class='error'>❌ Cannot test content structure - page or template missing</p>";
}
echo "</div>";

// Test 6: Check mobile responsiveness and design
echo "<div class='test-section'>";
echo "<h2>Test 6: Design and Responsiveness</h2>";
$tests_total++;

if (file_exists($template_file)) {
    $template_content = file_get_contents($template_file);
    
    $design_elements = array(
        'glassmorphism-card' => 'Glassmorphism design elements',
        '@media (max-width: 768px)' => 'Mobile responsiveness',
        'backdrop-filter: blur' => 'Backdrop filter effects',
        'var(--color-' => 'CSS custom properties',
        'theme-dark' => 'Dark theme support'
    );
    
    $missing_design = array();
    foreach ($design_elements as $element => $description) {
        if (strpos($template_content, $element) !== false) {
            echo "<p class='success'>✅ {$description} implemented</p>";
        } else {
            echo "<p class='warning'>⚠️ {$description} not found</p>";
            $missing_design[] = $description;
        }
    }
    
    if (count($missing_design) <= 1) {
        echo "<p class='success'>✅ Design implementation is adequate</p>";
        $tests_passed++;
    } else {
        echo "<p class='error'>❌ Design implementation needs improvement</p>";
    }
} else {
    echo "<p class='error'>❌ Cannot test design - template file missing</p>";
}
echo "</div>";

// Test Summary
echo "<div class='test-section'>";
echo "<h2>📊 Test Summary</h2>";
echo "<p><strong>Tests Passed:</strong> {$tests_passed} / {$tests_total}</p>";

$success_rate = ($tests_passed / $tests_total) * 100;
if ($success_rate >= 90) {
    echo "<p class='success'>🎉 Excellent! Privacy Policy implementation is ready for production.</p>";
} elseif ($success_rate >= 75) {
    echo "<p class='warning'>⚠️ Good progress, but some issues need attention before production.</p>";
} else {
    echo "<p class='error'>❌ Significant issues found. Privacy Policy needs more work before production.</p>";
}

echo "<p><strong>Success Rate:</strong> " . number_format($success_rate, 1) . "%</p>";
echo "</div>";

// Next Steps
echo "<div class='test-section'>";
echo "<h2>🚀 Next Steps</h2>";
if ($tests_passed === $tests_total) {
    echo "<p class='success'>✅ Privacy Policy implementation complete!</p>";
    echo "<p class='info'>🔄 Ready to proceed with Terms of Service implementation.</p>";
} else {
    echo "<p class='info'>🔧 Address the issues identified above before proceeding.</p>";
    echo "<p class='info'>📝 Review the Privacy Policy page and template file.</p>";
}

if ($privacy_page) {
    echo "<p class='info'>🌐 <strong>Test the page:</strong> <a href='" . get_permalink($privacy_page->ID) . "' target='_blank'>View Privacy Policy Page</a></p>";
}
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
