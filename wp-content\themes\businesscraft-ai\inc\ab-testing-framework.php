<?php
/**
 * A/B Testing Framework
 * 
 * Advanced A/B testing system for conversion optimization
 * with statistical significance tracking and automated winner selection.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize A/B testing framework
 */
function chatgabi_init_ab_testing() {
    // Add AJAX handlers
    add_action('wp_ajax_chatgabi_track_ab_conversion', 'chatgabi_track_ab_conversion');
    add_action('wp_ajax_nopriv_chatgabi_track_ab_conversion', 'chatgabi_track_ab_conversion');
    
    add_action('wp_ajax_chatgabi_get_ab_results', 'chatgabi_get_ab_results');
    add_action('wp_ajax_chatgabi_end_ab_test', 'chatgabi_end_ab_test');
    
    // Create A/B testing tables
    add_action('init', 'chatgabi_create_ab_testing_tables');
    
    // Enqueue A/B testing scripts
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_ab_testing_scripts');
    
    // Add admin menu
    add_action('admin_menu', 'chatgabi_add_ab_testing_admin_menu');
}
add_action('init', 'chatgabi_init_ab_testing');

/**
 * A/B Testing Manager Class
 */
class ChatGABI_AB_Testing {
    
    private static $instance = null;
    private $active_tests = array();
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get user variant for a test
     */
    public function get_variant($test_name, $variants = array()) {
        if (empty($variants)) {
            return null;
        }
        
        $user_id = $this->get_user_identifier();
        $stored_variant = $this->get_stored_variant($user_id, $test_name);
        
        if ($stored_variant && isset($variants[$stored_variant])) {
            return $stored_variant;
        }
        
        // Assign new variant
        $variant_keys = array_keys($variants);
        $variant_index = $user_id % count($variant_keys);
        $assigned_variant = $variant_keys[$variant_index];
        
        // Store variant assignment
        $this->store_variant_assignment($user_id, $test_name, $assigned_variant);
        
        // Track exposure
        $this->track_exposure($test_name, $assigned_variant, $user_id);
        
        return $assigned_variant;
    }
    
    /**
     * Track conversion for A/B test
     */
    public function track_conversion($test_name, $conversion_type = 'default', $value = 0) {
        $user_id = $this->get_user_identifier();
        $variant = $this->get_stored_variant($user_id, $test_name);
        
        if (!$variant) {
            return false;
        }
        
        global $wpdb;
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'chatgabi_ab_conversions',
            array(
                'test_name' => $test_name,
                'variant' => $variant,
                'user_identifier' => $user_id,
                'conversion_type' => $conversion_type,
                'conversion_value' => $value,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%f', '%s', '%s', '%s')
        );
        
        return $result !== false;
    }
    
    /**
     * Get test results with statistical analysis
     */
    public function get_test_results($test_name) {
        global $wpdb;
        
        // Get exposures
        $exposures = $wpdb->get_results($wpdb->prepare(
            "SELECT variant, COUNT(*) as count 
             FROM {$wpdb->prefix}chatgabi_ab_exposures 
             WHERE test_name = %s 
             GROUP BY variant",
            $test_name
        ), ARRAY_A);
        
        // Get conversions
        $conversions = $wpdb->get_results($wpdb->prepare(
            "SELECT variant, conversion_type, COUNT(*) as count, SUM(conversion_value) as total_value
             FROM {$wpdb->prefix}chatgabi_ab_conversions 
             WHERE test_name = %s 
             GROUP BY variant, conversion_type",
            $test_name
        ), ARRAY_A);
        
        // Calculate statistics
        $results = array();
        foreach ($exposures as $exposure) {
            $variant = $exposure['variant'];
            $exposure_count = $exposure['count'];
            
            $variant_conversions = array_filter($conversions, function($conv) use ($variant) {
                return $conv['variant'] === $variant;
            });
            
            $conversion_count = array_sum(array_column($variant_conversions, 'count'));
            $conversion_rate = $exposure_count > 0 ? ($conversion_count / $exposure_count) * 100 : 0;
            $total_value = array_sum(array_column($variant_conversions, 'total_value'));
            
            $results[$variant] = array(
                'exposures' => $exposure_count,
                'conversions' => $conversion_count,
                'conversion_rate' => $conversion_rate,
                'total_value' => $total_value,
                'avg_value' => $conversion_count > 0 ? $total_value / $conversion_count : 0
            );
        }
        
        // Calculate statistical significance
        if (count($results) >= 2) {
            $results['statistical_analysis'] = $this->calculate_statistical_significance($results);
        }
        
        return $results;
    }
    
    /**
     * Calculate statistical significance between variants
     */
    private function calculate_statistical_significance($results) {
        $variants = array_keys($results);
        if (count($variants) < 2) {
            return array('significant' => false, 'confidence' => 0);
        }
        
        $control = $results[$variants[0]];
        $treatment = $results[$variants[1]];
        
        $p1 = $control['conversion_rate'] / 100;
        $p2 = $treatment['conversion_rate'] / 100;
        $n1 = $control['exposures'];
        $n2 = $treatment['exposures'];
        
        if ($n1 < 30 || $n2 < 30) {
            return array('significant' => false, 'confidence' => 0, 'message' => 'Insufficient sample size');
        }
        
        // Z-test for proportions
        $p_pool = (($control['conversions'] + $treatment['conversions']) / ($n1 + $n2));
        $se = sqrt($p_pool * (1 - $p_pool) * ((1 / $n1) + (1 / $n2)));
        
        if ($se == 0) {
            return array('significant' => false, 'confidence' => 0, 'message' => 'No variance detected');
        }
        
        $z = ($p2 - $p1) / $se;
        $p_value = 2 * (1 - $this->normal_cdf(abs($z)));
        
        $confidence = (1 - $p_value) * 100;
        $significant = $p_value < 0.05; // 95% confidence
        
        return array(
            'significant' => $significant,
            'confidence' => $confidence,
            'p_value' => $p_value,
            'z_score' => $z,
            'winner' => $p2 > $p1 ? $variants[1] : $variants[0],
            'lift' => $p1 > 0 ? (($p2 - $p1) / $p1) * 100 : 0
        );
    }
    
    /**
     * Normal cumulative distribution function approximation
     */
    private function normal_cdf($x) {
        return 0.5 * (1 + $this->erf($x / sqrt(2)));
    }
    
    /**
     * Error function approximation
     */
    private function erf($x) {
        $a1 =  0.254829592;
        $a2 = -0.284496736;
        $a3 =  1.421413741;
        $a4 = -1.453152027;
        $a5 =  1.061405429;
        $p  =  0.3275911;
        
        $sign = $x < 0 ? -1 : 1;
        $x = abs($x);
        
        $t = 1.0 / (1.0 + $p * $x);
        $y = 1.0 - ((((($a5 * $t + $a4) * $t) + $a3) * $t + $a2) * $t + $a1) * $t * exp(-$x * $x);
        
        return $sign * $y;
    }
    
    /**
     * Get user identifier for consistent variant assignment
     */
    private function get_user_identifier() {
        if (is_user_logged_in()) {
            return 'user_' . get_current_user_id();
        }
        
        // Use IP + User Agent hash for anonymous users
        $identifier = $_SERVER['REMOTE_ADDR'] . $_SERVER['HTTP_USER_AGENT'];
        return 'anon_' . substr(md5($identifier), 0, 10);
    }
    
    /**
     * Get stored variant for user
     */
    private function get_stored_variant($user_id, $test_name) {
        global $wpdb;
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT variant FROM {$wpdb->prefix}chatgabi_ab_assignments 
             WHERE user_identifier = %s AND test_name = %s",
            $user_id, $test_name
        ));
    }
    
    /**
     * Store variant assignment
     */
    private function store_variant_assignment($user_id, $test_name, $variant) {
        global $wpdb;
        
        $wpdb->replace(
            $wpdb->prefix . 'chatgabi_ab_assignments',
            array(
                'user_identifier' => $user_id,
                'test_name' => $test_name,
                'variant' => $variant,
                'assigned_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Track exposure
     */
    private function track_exposure($test_name, $variant, $user_id) {
        global $wpdb;
        
        $wpdb->insert(
            $wpdb->prefix . 'chatgabi_ab_exposures',
            array(
                'test_name' => $test_name,
                'variant' => $variant,
                'user_identifier' => $user_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );
    }
}

/**
 * Get A/B testing instance
 */
function chatgabi_ab_testing() {
    return ChatGABI_AB_Testing::getInstance();
}

/**
 * Helper function to get variant
 */
function chatgabi_get_ab_variant($test_name, $variants) {
    return chatgabi_ab_testing()->get_variant($test_name, $variants);
}

/**
 * Helper function to track conversion
 */
function chatgabi_track_ab_conversion($test_name, $conversion_type = 'default', $value = 0) {
    return chatgabi_ab_testing()->track_conversion($test_name, $conversion_type, $value);
}

/**
 * AJAX handler for tracking conversions
 */
function chatgabi_track_ab_conversion() {
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_ab_nonce')) {
        wp_send_json_error('Invalid nonce');
    }
    
    $test_name = sanitize_text_field($_POST['test_name']);
    $conversion_type = sanitize_text_field($_POST['conversion_type'] ?? 'default');
    $value = floatval($_POST['value'] ?? 0);
    
    $result = chatgabi_track_ab_conversion($test_name, $conversion_type, $value);
    
    if ($result) {
        wp_send_json_success('Conversion tracked');
    } else {
        wp_send_json_error('Failed to track conversion');
    }
}

/**
 * Create A/B testing database tables
 */
function chatgabi_create_ab_testing_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Assignments table
    $assignments_table = $wpdb->prefix . 'chatgabi_ab_assignments';
    $assignments_sql = "CREATE TABLE $assignments_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_identifier varchar(100) NOT NULL,
        test_name varchar(100) NOT NULL,
        variant varchar(50) NOT NULL,
        assigned_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_test (user_identifier, test_name),
        KEY test_name (test_name),
        KEY assigned_at (assigned_at)
    ) $charset_collate;";
    
    // Exposures table
    $exposures_table = $wpdb->prefix . 'chatgabi_ab_exposures';
    $exposures_sql = "CREATE TABLE $exposures_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        test_name varchar(100) NOT NULL,
        variant varchar(50) NOT NULL,
        user_identifier varchar(100) NOT NULL,
        ip_address varchar(45),
        user_agent text,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY test_variant (test_name, variant),
        KEY timestamp (timestamp)
    ) $charset_collate;";
    
    // Conversions table
    $conversions_table = $wpdb->prefix . 'chatgabi_ab_conversions';
    $conversions_sql = "CREATE TABLE $conversions_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        test_name varchar(100) NOT NULL,
        variant varchar(50) NOT NULL,
        user_identifier varchar(100) NOT NULL,
        conversion_type varchar(100) DEFAULT 'default',
        conversion_value decimal(10,2) DEFAULT 0,
        ip_address varchar(45),
        user_agent text,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY test_variant (test_name, variant),
        KEY conversion_type (conversion_type),
        KEY timestamp (timestamp)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($assignments_sql);
    dbDelta($exposures_sql);
    dbDelta($conversions_sql);
}

/**
 * Enqueue A/B testing scripts
 */
function chatgabi_enqueue_ab_testing_scripts() {
    if (is_front_page()) {
        wp_enqueue_script(
            'chatgabi-ab-testing',
            CHATGABI_THEME_URL . '/assets/js/ab-testing.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );
        
        wp_localize_script('chatgabi-ab-testing', 'chatgabiAB', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_ab_nonce')
        ));
    }
}

/**
 * Add A/B testing admin menu
 */
function chatgabi_add_ab_testing_admin_menu() {
    add_submenu_page(
        'chatgabi-admin',
        __('A/B Testing', 'chatgabi'),
        __('A/B Testing', 'chatgabi'),
        'manage_options',
        'chatgabi-ab-testing',
        'chatgabi_ab_testing_admin_page'
    );
}

/**
 * A/B testing admin page
 */
function chatgabi_ab_testing_admin_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('ChatGABI A/B Testing Dashboard', 'chatgabi'); ?></h1>
        <div id="ab-testing-dashboard">
            <!-- Dashboard content will be loaded via JavaScript -->
            <div class="ab-loading">Loading A/B testing data...</div>
        </div>
    </div>
    
    <script>
    // Load A/B testing dashboard
    jQuery(document).ready(function($) {
        // Dashboard implementation would go here
        $('#ab-testing-dashboard').html('<p>A/B Testing Dashboard - Implementation in progress</p>');
    });
    </script>
    <?php
}
