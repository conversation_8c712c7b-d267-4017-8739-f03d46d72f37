<?php
/**
 * The template for displaying 404 pages (not found)
 * 
 * Professional 404 error page for ChatGABI
 * African-inspired design with helpful navigation
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="chatgabi-404-page">
    <div class="container">
        <!-- 404 Error Content -->
        <div class="error-404-content">
            <!-- Main Error Section -->
            <div class="error-main glassmorphism-card">
                <div class="error-icon">
                    <span class="error-number">404</span>
                    <span class="error-emoji">🔍</span>
                </div>
                
                <h1 class="error-title">
                    <?php _e('Page Not Found', 'chatgabi'); ?>
                </h1>
                
                <p class="error-message">
                    <?php _e('Oops! The page you\'re looking for seems to have wandered off like a lost entrepreneur in the African savanna. Don\'t worry, we\'ll help you find your way back to building your business empire!', 'chatgabi'); ?>
                </p>
                
                <div class="error-actions">
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                        <span class="btn-icon">🏠</span>
                        <?php _e('Go Home', 'chatgabi'); ?>
                    </a>
                    
                    <?php if (is_user_logged_in()): ?>
                        <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="btn btn-secondary">
                            <span class="btn-icon">📊</span>
                            <?php _e('Dashboard', 'chatgabi'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Search Section -->
            <div class="error-search glassmorphism-card">
                <h2><?php _e('Search ChatGABI', 'chatgabi'); ?></h2>
                <p><?php _e('Looking for something specific? Try searching our AI-powered business intelligence platform:', 'chatgabi'); ?></p>
                
                <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                    <div class="search-input-group">
                        <input type="search" 
                               class="search-field" 
                               placeholder="<?php esc_attr_e('Search for business insights, templates, tools...', 'chatgabi'); ?>" 
                               value="<?php echo get_search_query(); ?>" 
                               name="s" 
                               required>
                        <button type="submit" class="search-submit">
                            <span class="search-icon">🔍</span>
                            <?php _e('Search', 'chatgabi'); ?>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Popular Pages Section -->
            <div class="error-popular glassmorphism-card">
                <h2><?php _e('Popular Destinations', 'chatgabi'); ?></h2>
                <p><?php _e('Here are some popular pages that might help you on your entrepreneurial journey:', 'chatgabi'); ?></p>
                
                <div class="popular-links">
                    <div class="link-category">
                        <h3><?php _e('🚀 AI Tools', 'chatgabi'); ?></h3>
                        <ul>
                            <?php if (is_user_logged_in()): ?>
                                <li><a href="<?php echo esc_url(home_url('/templates')); ?>"><?php _e('Business Templates', 'chatgabi'); ?></a></li>
                                <li><a href="<?php echo esc_url(home_url('/wizards')); ?>"><?php _e('Document Wizards', 'chatgabi'); ?></a></li>
                                <li><a href="<?php echo esc_url(home_url('/dashboard')); ?>"><?php _e('AI Dashboard', 'chatgabi'); ?></a></li>
                            <?php else: ?>
                                <li><a href="<?php echo wp_login_url(); ?>"><?php _e('Login to Access Tools', 'chatgabi'); ?></a></li>
                                <li><a href="<?php echo wp_registration_url(); ?>"><?php _e('Create Account', 'chatgabi'); ?></a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    
                    <div class="link-category">
                        <h3><?php _e('📚 Information', 'chatgabi'); ?></h3>
                        <ul>
                            <li><a href="<?php echo esc_url(home_url('/about')); ?>"><?php _e('About ChatGABI', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/contact')); ?>"><?php _e('Contact Us', 'chatgabi'); ?></a></li>
                            <li><a href="#pricing"><?php _e('Pricing', 'chatgabi'); ?></a></li>
                            <li><a href="#help-center"><?php _e('Help Center', 'chatgabi'); ?></a></li>
                        </ul>
                    </div>
                    
                    <div class="link-category">
                        <h3><?php _e('🌍 African Markets', 'chatgabi'); ?></h3>
                        <ul>
                            <li><a href="<?php echo esc_url(home_url('/ghana')); ?>"><?php _e('Ghana Business Hub', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/kenya')); ?>"><?php _e('Kenya Business Hub', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/nigeria')); ?>"><?php _e('Nigeria Business Hub', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/south-africa')); ?>"><?php _e('South Africa Business Hub', 'chatgabi'); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="error-help glassmorphism-card">
                <h2><?php _e('Need Help?', 'chatgabi'); ?></h2>
                <p><?php _e('If you believe this is an error or need assistance, our support team is here to help you succeed:', 'chatgabi'); ?></p>
                
                <div class="help-options">
                    <div class="help-option">
                        <span class="help-icon">📧</span>
                        <div class="help-content">
                            <h4><?php _e('Email Support', 'chatgabi'); ?></h4>
                            <p><?php _e('<EMAIL>', 'chatgabi'); ?></p>
                            <small><?php _e('Response within 24 hours', 'chatgabi'); ?></small>
                        </div>
                    </div>
                    
                    <div class="help-option">
                        <span class="help-icon">💬</span>
                        <div class="help-content">
                            <h4><?php _e('Live Chat', 'chatgabi'); ?></h4>
                            <p><?php _e('Chat with our AI assistant', 'chatgabi'); ?></p>
                            <small><?php _e('Available 24/7', 'chatgabi'); ?></small>
                        </div>
                    </div>
                    
                    <div class="help-option">
                        <span class="help-icon">📱</span>
                        <div class="help-content">
                            <h4><?php _e('WhatsApp Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Quick support via WhatsApp', 'chatgabi'); ?></p>
                            <small><?php _e('Business hours: 9 AM - 6 PM GMT', 'chatgabi'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Motivational Quote -->
            <div class="error-quote glassmorphism-card">
                <blockquote>
                    <p><?php _e('"Every successful entrepreneur has faced dead ends. The key is to turn around and find a new path to success."', 'chatgabi'); ?></p>
                    <cite><?php _e('- African Business Wisdom', 'chatgabi'); ?></cite>
                </blockquote>
            </div>
        </div>
    </div>
</div>

<style>
/* 404 Page Specific Styles */
.chatgabi-404-page {
    padding: 60px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.error-404-content {
    max-width: 1000px;
    margin: 0 auto;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 30px;
}

/* Main Error Section */
.error-main {
    text-align: center;
    padding: 60px 40px;
}

.error-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    color: var(--color-primary-accent);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-emoji {
    font-size: 4rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.error-title {
    font-size: 3rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    font-weight: 700;
}

.error-message {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Search Section */
.error-search h2 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.error-search p {
    color: var(--color-text-secondary);
    margin-bottom: 25px;
}

.search-input-group {
    display: flex;
    gap: 0;
    max-width: 500px;
    margin: 0 auto;
}

.search-field {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid var(--color-borders);
    border-radius: 12px 0 0 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
}

.search-field:focus {
    outline: none;
    border-color: var(--color-primary-accent);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.1);
}

.search-submit {
    padding: 15px 25px;
    background: var(--color-primary-accent);
    color: white;
    border: none;
    border-radius: 0 12px 12px 0;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s ease;
}

.search-submit:hover {
    background: var(--color-secondary-accent);
}

/* Popular Pages Section */
.error-popular h2 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.error-popular p {
    color: var(--color-text-secondary);
    margin-bottom: 30px;
}

.popular-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.link-category h3 {
    color: var(--color-tertiary-accent);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.link-category ul {
    list-style: none;
    padding: 0;
}

.link-category li {
    margin-bottom: 10px;
}

.link-category a {
    color: var(--color-text-primary);
    text-decoration: none;
    padding: 8px 0;
    display: block;
    transition: color 0.3s ease;
}

.link-category a:hover {
    color: var(--color-primary-accent);
    text-decoration: underline;
}

/* Help Section */
.error-help h2 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.error-help p {
    color: var(--color-text-secondary);
    margin-bottom: 30px;
}

.help-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.help-option {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.help-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.help-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.help-content p {
    color: var(--color-text-primary);
    margin-bottom: 5px;
    font-weight: 600;
}

.help-content small {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Quote Section */
.error-quote {
    text-align: center;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-left: 4px solid var(--color-secondary-accent);
}

.error-quote blockquote {
    margin: 0;
}

.error-quote p {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--color-text-primary);
    margin-bottom: 15px;
    line-height: 1.6;
}

.error-quote cite {
    color: var(--color-text-secondary);
    font-size: 1rem;
    font-weight: 600;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-404-page {
        padding: 40px 0;
    }
    
    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 20px;
    }
    
    .error-main {
        padding: 40px 20px;
    }
    
    .error-number {
        font-size: 5rem;
    }
    
    .error-emoji {
        font-size: 3rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-message {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .search-input-group {
        flex-direction: column;
        max-width: 100%;
    }
    
    .search-field,
    .search-submit {
        border-radius: 12px;
    }
    
    .search-field {
        border-bottom: none;
    }
    
    .popular-links {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .help-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .help-option {
        flex-direction: column;
        text-align: center;
    }
    
    .error-quote p {
        font-size: 1.1rem;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .error-title {
    color: var(--color-secondary-accent);
}

body.theme-dark .error-search h2,
body.theme-dark .error-popular h2,
body.theme-dark .error-help h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .search-field {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .help-option {
    background: rgba(39, 174, 96, 0.2);
}

body.theme-dark .error-quote {
    background: rgba(255, 215, 0, 0.1);
}
</style>

<?php
// Add SEO meta tags for 404 page
function chatgabi_404_meta() {
    echo '<meta name="robots" content="noindex, nofollow">';
    echo '<meta name="description" content="' . esc_attr(__('Page not found on ChatGABI. Find your way back to AI-powered business intelligence for African entrepreneurs.', 'chatgabi')) . '">';
}
add_action('wp_head', 'chatgabi_404_meta');

get_footer();
?>
