<?php
/**
 * Template Name: Login
 * 
 * User login page for ChatGABI
 * Custom login with security features and dashboard redirect
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url('/dashboard'));
    exit;
}

// Handle login form submission
$login_message = '';
$login_status = '';
$form_data = array();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['chatgabi_login_submit'])) {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['chatgabi_login_nonce'], 'chatgabi_login_form')) {
        $login_message = __('Security check failed. Please try again.', 'chatgabi');
        $login_status = 'error';
    } else {
        // Sanitize form data
        $form_data = array(
            'username' => sanitize_user($_POST['login_username']),
            'password' => $_POST['login_password'],
            'remember' => isset($_POST['login_remember'])
        );
        
        // Validation
        if (empty($form_data['username']) || empty($form_data['password'])) {
            $login_message = __('Please enter both username and password.', 'chatgabi');
            $login_status = 'error';
        } else {
            // Attempt login
            $credentials = array(
                'user_login' => $form_data['username'],
                'user_password' => $form_data['password'],
                'remember' => $form_data['remember']
            );
            
            $user = wp_signon($credentials, false);
            
            if (is_wp_error($user)) {
                $login_message = __('Invalid username or password. Please try again.', 'chatgabi');
                $login_status = 'error';
                
                // Log failed login attempt
                error_log('ChatGABI Login Failed: ' . $form_data['username'] . ' from ' . $_SERVER['REMOTE_ADDR']);
            } else {
                // Successful login
                $login_message = __('Login successful! Redirecting to dashboard...', 'chatgabi');
                $login_status = 'success';
                
                // Update last login time
                update_user_meta($user->ID, 'last_login', current_time('mysql'));
                
                // Clear form data
                $form_data = array();
                
                // Redirect to dashboard
                $redirect_url = isset($_GET['redirect_to']) ? esc_url_raw($_GET['redirect_to']) : home_url('/dashboard');
                echo '<script>setTimeout(function(){ window.location.href = "' . $redirect_url . '"; }, 2000);</script>';
            }
        }
    }
}

get_header();
?>

<div class="chatgabi-login-page">
    <div class="container">
        <!-- Login Hero Section -->
        <div class="login-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🔑</span>
                    <?php _e('Welcome Back', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Sign in to your ChatGABI account and continue building your business with AI-powered intelligence.', 'chatgabi'); ?>
                </p>
                
                <div class="login-stats">
                    <div class="stat-item">
                        <span class="stat-icon">👥</span>
                        <span class="stat-text"><?php _e('5,000+ Entrepreneurs', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🌍</span>
                        <span class="stat-text"><?php _e('4 African Countries', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🤖</span>
                        <span class="stat-text"><?php _e('AI-Powered Insights', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="login-content">
            <!-- Login Form -->
            <div class="login-form-section glassmorphism-card">
                <div class="form-header">
                    <h2><?php _e('Sign In to Your Account', 'chatgabi'); ?></h2>
                    <p><?php _e('Enter your credentials to access your ChatGABI dashboard', 'chatgabi'); ?></p>
                </div>
                
                <?php if ($login_message): ?>
                    <div class="login-message <?php echo esc_attr($login_status); ?>">
                        <p><?php echo esc_html($login_message); ?></p>
                        <?php if ($login_status === 'success'): ?>
                            <p><small><?php _e('Redirecting to dashboard in 2 seconds...', 'chatgabi'); ?></small></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <form method="post" class="login-form" novalidate>
                    <?php wp_nonce_field('chatgabi_login_form', 'chatgabi_login_nonce'); ?>
                    
                    <div class="form-group">
                        <label for="login_username"><?php _e('Username or Email', 'chatgabi'); ?> <span class="required">*</span></label>
                        <input type="text" 
                               id="login_username" 
                               name="login_username" 
                               value="<?php echo esc_attr($form_data['username'] ?? ''); ?>"
                               required 
                               placeholder="<?php esc_attr_e('Enter your username or email', 'chatgabi'); ?>"
                               autocomplete="username">
                        <span class="input-icon">👤</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="login_password"><?php _e('Password', 'chatgabi'); ?> <span class="required">*</span></label>
                        <input type="password" 
                               id="login_password" 
                               name="login_password" 
                               required 
                               placeholder="<?php esc_attr_e('Enter your password', 'chatgabi'); ?>"
                               autocomplete="current-password">
                        <span class="input-icon">🔒</span>
                        <button type="button" class="password-toggle" aria-label="<?php esc_attr_e('Toggle password visibility', 'chatgabi'); ?>">
                            <span class="toggle-icon">👁️</span>
                        </button>
                    </div>
                    
                    <div class="form-options">
                        <div class="remember-me">
                            <label class="checkbox-label">
                                <input type="checkbox" 
                                       id="login_remember" 
                                       name="login_remember"
                                       <?php checked($form_data['remember'] ?? false, true); ?>>
                                <span class="checkmark"></span>
                                <span class="checkbox-text"><?php _e('Remember me', 'chatgabi'); ?></span>
                            </label>
                        </div>
                        
                        <div class="forgot-password">
                            <a href="<?php echo esc_url(home_url('/password-reset')); ?>"><?php _e('Forgot password?', 'chatgabi'); ?></a>
                        </div>
                    </div>
                    
                    <div class="form-submit">
                        <button type="submit" name="chatgabi_login_submit" class="btn btn-primary btn-large">
                            <span class="btn-icon">🚀</span>
                            <?php _e('Sign In to Dashboard', 'chatgabi'); ?>
                        </button>
                    </div>
                </form>
                
                <div class="form-footer">
                    <p><?php _e('Don\'t have an account?', 'chatgabi'); ?> 
                       <a href="<?php echo esc_url(home_url('/register')); ?>"><?php _e('Create account & get 25 free credits', 'chatgabi'); ?></a>
                    </p>
                </div>
                
                <div class="security-notice">
                    <div class="security-icon">🔒</div>
                    <div class="security-text">
                        <strong><?php _e('Secure Login', 'chatgabi'); ?></strong>
                        <p><?php _e('Your login is protected with enterprise-grade security and encryption.', 'chatgabi'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Login Benefits Sidebar -->
            <div class="login-benefits-section glassmorphism-card">
                <div class="benefits-header">
                    <h3><?php _e('Your AI Business Assistant', 'chatgabi'); ?></h3>
                    <p><?php _e('Access powerful tools designed for African entrepreneurs', 'chatgabi'); ?></p>
                </div>
                
                <div class="quick-access-features">
                    <div class="feature-item">
                        <div class="feature-icon">🤖</div>
                        <div class="feature-content">
                            <h4><?php _e('AI Chat Intelligence', 'chatgabi'); ?></h4>
                            <p><?php _e('Get instant business advice and insights', 'chatgabi'); ?></p>
                            <span class="feature-cost">1 <?php _e('credit per query', 'chatgabi'); ?></span>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">📝</div>
                        <div class="feature-content">
                            <h4><?php _e('Business Templates', 'chatgabi'); ?></h4>
                            <p><?php _e('Generate professional documents instantly', 'chatgabi'); ?></p>
                            <span class="feature-cost">2 <?php _e('credits per template', 'chatgabi'); ?></span>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <div class="feature-content">
                            <h4><?php _e('Market Analysis', 'chatgabi'); ?></h4>
                            <p><?php _e('Discover opportunities in your sector', 'chatgabi'); ?></p>
                            <span class="feature-cost">3 <?php _e('credits per analysis', 'chatgabi'); ?></span>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🧙‍♂️</div>
                        <div class="feature-content">
                            <h4><?php _e('Document Wizards', 'chatgabi'); ?></h4>
                            <p><?php _e('Step-by-step business plan creation', 'chatgabi'); ?></p>
                            <span class="feature-cost">5 <?php _e('credits per wizard', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="recent-updates">
                    <h4><?php _e('Recent Updates', 'chatgabi'); ?></h4>
                    <div class="update-item">
                        <span class="update-date"><?php _e('Dec 2024', 'chatgabi'); ?></span>
                        <span class="update-text"><?php _e('Enhanced multi-language support for Zulu and Yoruba', 'chatgabi'); ?></span>
                    </div>
                    <div class="update-item">
                        <span class="update-date"><?php _e('Nov 2024', 'chatgabi'); ?></span>
                        <span class="update-text"><?php _e('New African Context Engine with cultural intelligence', 'chatgabi'); ?></span>
                    </div>
                    <div class="update-item">
                        <span class="update-date"><?php _e('Oct 2024', 'chatgabi'); ?></span>
                        <span class="update-text"><?php _e('Paystack integration for seamless African payments', 'chatgabi'); ?></span>
                    </div>
                </div>
                
                <div class="support-links">
                    <h4><?php _e('Need Help?', 'chatgabi'); ?></h4>
                    <div class="support-options">
                        <a href="<?php echo esc_url(home_url('/contact')); ?>" class="support-link">
                            <span class="support-icon">💬</span>
                            <span><?php _e('Contact Support', 'chatgabi'); ?></span>
                        </a>
                        <a href="<?php echo esc_url(home_url('/features')); ?>" class="support-link">
                            <span class="support-icon">📖</span>
                            <span><?php _e('Feature Guide', 'chatgabi'); ?></span>
                        </a>
                        <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="support-link">
                            <span class="support-icon">💰</span>
                            <span><?php _e('Pricing Plans', 'chatgabi'); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Login Page Specific Styles */
.chatgabi-login-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.login-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.login-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.stat-icon {
    font-size: 1.5rem;
}

/* Login Content Layout */
.login-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Form Styles */
.login-form-section {
    padding: 40px;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header h2 {
    font-size: 2.2rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.form-header p {
    font-size: 1.1rem;
    color: var(--color-text-secondary);
}

.login-message {
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    font-weight: 600;
}

.login-message.success {
    background: rgba(39, 174, 96, 0.1);
    border: 2px solid #27ae60;
    color: #27ae60;
}

.login-message.error {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid #e74c3c;
    color: #e74c3c;
}

.login-form {
    max-width: 100%;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-text-primary);
}

.required {
    color: #e74c3c;
}

.form-group input {
    width: 100%;
    padding: 15px 50px 15px 16px;
    border: 2px solid var(--color-borders);
    border-radius: 12px;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--color-primary-accent);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.1);
}

.input-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    pointer-events: none;
}

.password-toggle {
    position: absolute;
    right: 45px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: var(--color-text-secondary);
    padding: 5px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--color-primary-accent);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--color-borders);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    position: relative;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--color-primary-accent);
    border-color: var(--color-primary-accent);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark:after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 10px;
}

.checkbox-text {
    color: var(--color-text-primary);
    font-size: 0.95rem;
}

.forgot-password a {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.form-submit {
    margin-bottom: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
    width: 100%;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1.2rem;
}

.form-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(var(--color-borders), 0.3);
    margin-bottom: 30px;
}

.form-footer p {
    color: var(--color-text-secondary);
}

.form-footer a {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

.security-notice {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.security-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.security-text strong {
    color: var(--color-primary-accent);
    display: block;
    margin-bottom: 5px;
}

.security-text p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Benefits Sidebar */
.login-benefits-section {
    padding: 30px;
}

.benefits-header {
    text-align: center;
    margin-bottom: 30px;
}

.benefits-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.benefits-header p {
    color: var(--color-text-secondary);
    font-size: 1rem;
}

.quick-access-features {
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    margin-bottom: 15px;
    border-left: 4px solid var(--color-primary-accent);
}

.feature-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.feature-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.feature-content p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.feature-cost {
    background: var(--color-secondary-accent);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.recent-updates {
    margin-bottom: 30px;
}

.recent-updates h4 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.update-item {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    padding: 10px;
    background: rgba(var(--color-secondary-accent), 0.05);
    border-radius: 8px;
}

.update-date {
    color: var(--color-secondary-accent);
    font-weight: bold;
    font-size: 0.8rem;
    flex-shrink: 0;
    min-width: 60px;
}

.update-text {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.support-links h4 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.support-options {
    display: grid;
    gap: 10px;
}

.support-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(var(--color-tertiary-accent), 0.1);
    border-radius: 8px;
    text-decoration: none;
    color: var(--color-text-primary);
    transition: all 0.3s ease;
}

.support-link:hover {
    background: rgba(var(--color-tertiary-accent), 0.2);
    transform: translateX(5px);
}

.support-icon {
    font-size: 1.2rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-login-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .login-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .login-stats {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .login-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .login-form-section {
        padding: 25px 20px;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .feature-item {
        padding: 15px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .form-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .form-group input {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .checkmark {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
}

body.theme-dark .security-notice {
    background: rgba(39, 174, 96, 0.2);
}

body.theme-dark .feature-item {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .update-item {
    background: rgba(255, 215, 0, 0.1);
}

body.theme-dark .support-link {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .support-link:hover {
    background: rgba(110, 127, 243, 0.2);
}
</style>

<script>
// Login Page JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // Form elements
    const loginForm = document.querySelector('.login-form');
    const usernameField = document.getElementById('login_username');
    const passwordField = document.getElementById('login_password');
    const passwordToggle = document.querySelector('.password-toggle');
    const rememberCheckbox = document.getElementById('login_remember');
    const submitButton = document.querySelector('button[name="chatgabi_login_submit"]');

    // Password visibility toggle
    if (passwordToggle && passwordField) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            const toggleIcon = this.querySelector('.toggle-icon');
            toggleIcon.textContent = type === 'password' ? '👁️' : '🙈';
        });
    }

    // Form validation
    function validateForm() {
        let isValid = true;

        // Clear previous validation messages
        document.querySelectorAll('.validation-message').forEach(msg => msg.remove());

        // Validate username/email
        if (!usernameField.value.trim()) {
            showValidationMessage(usernameField, 'Username or email is required', 'error');
            isValid = false;
        }

        // Validate password
        if (!passwordField.value.trim()) {
            showValidationMessage(passwordField, 'Password is required', 'error');
            isValid = false;
        }

        return isValid;
    }

    function showValidationMessage(field, message, type) {
        const validationMessage = document.createElement('small');
        validationMessage.className = `validation-message ${type}`;
        validationMessage.textContent = message;
        field.parentNode.appendChild(validationMessage);
    }

    // Real-time validation
    if (usernameField) {
        usernameField.addEventListener('blur', function() {
            if (this.value.trim()) {
                // Remove any existing validation messages
                const existingMessage = this.parentNode.querySelector('.validation-message');
                if (existingMessage) {
                    existingMessage.remove();
                }
            }
        });
    }

    if (passwordField) {
        passwordField.addEventListener('blur', function() {
            if (this.value.trim()) {
                // Remove any existing validation messages
                const existingMessage = this.parentNode.querySelector('.validation-message');
                if (existingMessage) {
                    existingMessage.remove();
                }
            }
        });
    }

    // Form submission handling
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="btn-icon">⏳</span> Signing In...';
                submitButton.disabled = true;

                // Re-enable button after 10 seconds as fallback
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 10000);
            }
        });
    }

    // Remember me functionality
    if (rememberCheckbox) {
        // Load saved username if remember me was checked
        const savedUsername = localStorage.getItem('chatgabi_remember_username');
        const wasRemembered = localStorage.getItem('chatgabi_remember_me') === 'true';

        if (wasRemembered && savedUsername && usernameField) {
            usernameField.value = savedUsername;
            rememberCheckbox.checked = true;
        }

        // Save/clear username based on remember me checkbox
        rememberCheckbox.addEventListener('change', function() {
            if (this.checked && usernameField.value.trim()) {
                localStorage.setItem('chatgabi_remember_username', usernameField.value.trim());
                localStorage.setItem('chatgabi_remember_me', 'true');
            } else {
                localStorage.removeItem('chatgabi_remember_username');
                localStorage.removeItem('chatgabi_remember_me');
            }
        });

        // Update saved username when field changes
        if (usernameField) {
            usernameField.addEventListener('blur', function() {
                if (rememberCheckbox.checked && this.value.trim()) {
                    localStorage.setItem('chatgabi_remember_username', this.value.trim());
                }
            });
        }
    }

    // Auto-focus on username field
    if (usernameField && !usernameField.value) {
        usernameField.focus();
    } else if (passwordField && usernameField.value) {
        passwordField.focus();
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Enter key to submit form
        if (e.key === 'Enter' && (usernameField === document.activeElement || passwordField === document.activeElement)) {
            if (loginForm) {
                loginForm.dispatchEvent(new Event('submit'));
            }
        }

        // Escape key to clear form
        if (e.key === 'Escape') {
            if (usernameField) usernameField.value = '';
            if (passwordField) passwordField.value = '';
            if (usernameField) usernameField.focus();
        }
    });

    // Smooth animations for form elements
    const formGroups = document.querySelectorAll('.form-group');

    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        group.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        setTimeout(() => {
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Feature items animation
    const featureItems = document.querySelectorAll('.feature-item');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, observerOptions);

    featureItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        item.style.transitionDelay = `${index * 100}ms`;
        observer.observe(item);
    });

    // Security notice animation
    const securityNotice = document.querySelector('.security-notice');
    if (securityNotice) {
        securityNotice.style.opacity = '0';
        securityNotice.style.transform = 'translateY(20px)';
        securityNotice.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        setTimeout(() => {
            securityNotice.style.opacity = '1';
            securityNotice.style.transform = 'translateY(0)';
        }, 800);
    }

    // Input field focus effects
    const inputs = document.querySelectorAll('input');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentNode.classList.remove('focused');
        });
    });

    // Add custom CSS for validation messages and focus effects
    const style = document.createElement('style');
    style.textContent = `
        .validation-message {
            display: block;
            margin-top: 5px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .validation-message.error {
            color: #e74c3c;
        }

        .validation-message.success {
            color: #27ae60;
        }

        .form-group.focused .input-icon {
            color: var(--color-primary-accent);
        }

        .form-group.focused input {
            border-color: var(--color-primary-accent);
            box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.1);
        }

        .password-toggle:hover {
            background: rgba(var(--color-primary-accent), 0.1);
        }

        .feature-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .support-link:hover .support-icon {
            transform: scale(1.1);
        }

        .update-item:hover {
            background: rgba(var(--color-secondary-accent), 0.1);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn-primary:hover {
            animation: pulse 0.3s ease;
        }
    `;
    document.head.appendChild(style);

    // Check for URL parameters (e.g., registration success)
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('registered') === 'success') {
        const successMessage = document.createElement('div');
        successMessage.className = 'login-message success';
        successMessage.innerHTML = '<p>Registration successful! Please sign in with your new account.</p>';

        const formHeader = document.querySelector('.form-header');
        if (formHeader) {
            formHeader.after(successMessage);
        }
    }

    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate any responsive elements if needed
        }, 250);
    });
});
</script>

<?php
// Add SEO meta tags
function chatgabi_login_meta() {
    $title = __('Login to ChatGABI - Access Your AI Business Intelligence Dashboard', 'chatgabi');
    $description = __('Sign in to your ChatGABI account to access AI-powered business intelligence, market analysis, and document generation tools designed for African entrepreneurs.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
    echo '<meta name="robots" content="noindex, nofollow">';
}
add_action('wp_head', 'chatgabi_login_meta');

get_footer();
?>
