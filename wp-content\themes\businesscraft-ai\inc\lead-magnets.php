<?php
/**
 * Lead Magnets System
 * 
 * Manages free downloadable resources, email capture,
 * and lead nurturing for conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize lead magnets system
 */
function chatgabi_init_lead_magnets() {
    // Add AJAX handlers for lead magnet downloads
    add_action('wp_ajax_chatgabi_download_lead_magnet', 'chatgabi_handle_lead_magnet_download');
    add_action('wp_ajax_nopriv_chatgabi_download_lead_magnet', 'chatgabi_handle_lead_magnet_download');
    
    // Add email capture handler
    add_action('wp_ajax_chatgabi_capture_email', 'chatgabi_handle_email_capture');
    add_action('wp_ajax_nopriv_chatgabi_capture_email', 'chatgabi_handle_email_capture');
    
    // Enqueue lead magnets scripts
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_lead_magnets_scripts');
    
    // Create lead magnets table
    add_action('init', 'chatgabi_create_lead_magnets_table');
}
add_action('init', 'chatgabi_init_lead_magnets');

/**
 * Enqueue lead magnets scripts and styles
 */
function chatgabi_enqueue_lead_magnets_scripts() {
    if (is_front_page()) {
        wp_enqueue_script(
            'chatgabi-lead-magnets',
            CHATGABI_THEME_URL . '/assets/js/lead-magnets.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );
        
        wp_localize_script('chatgabi-lead-magnets', 'chatgabiLeadMagnets', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_lead_magnets_nonce'),
            'strings' => array(
                'downloading' => __('Preparing download...', 'chatgabi'),
                'email_required' => __('Please enter your email address', 'chatgabi'),
                'invalid_email' => __('Please enter a valid email address', 'chatgabi'),
                'download_success' => __('Download started! Check your email for the link.', 'chatgabi'),
                'download_error' => __('Download failed. Please try again.', 'chatgabi'),
                'already_subscribed' => __('You\'re already subscribed! Download link sent to your email.', 'chatgabi')
            )
        ));
    }
}

/**
 * Get available lead magnets
 */
function chatgabi_get_lead_magnets() {
    return array(
        array(
            'id' => 'business-plan-template',
            'title' => __('Free Business Plan Template', 'chatgabi'),
            'description' => __('Professional 20-page business plan template specifically designed for African entrepreneurs. Includes market analysis, financial projections, and growth strategies.', 'chatgabi'),
            'file_size' => '2.5 MB',
            'format' => 'PDF + Word',
            'downloads' => 15420,
            'rating' => 4.9,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/business-plan-template-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/chatgabi-business-plan-template.pdf',
            'category' => 'business-planning',
            'countries' => array('GH', 'KE', 'NG', 'ZA'),
            'languages' => array('en', 'tw', 'sw', 'yo', 'zu'),
            'features' => array(
                __('20-page comprehensive template', 'chatgabi'),
                __('African market examples', 'chatgabi'),
                __('Financial projection sheets', 'chatgabi'),
                __('Investor pitch deck template', 'chatgabi'),
                __('Available in 5 languages', 'chatgabi')
            )
        ),
        array(
            'id' => 'african-market-guide',
            'title' => __('African Market Entry Guide', 'chatgabi'),
            'description' => __('Complete guide to entering and succeeding in African markets. Covers regulations, cultural considerations, and growth opportunities across 12 countries.', 'chatgabi'),
            'file_size' => '4.2 MB',
            'format' => 'PDF',
            'downloads' => 8930,
            'rating' => 4.8,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/african-market-guide-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/african-market-entry-guide.pdf',
            'category' => 'market-research',
            'countries' => array('GH', 'KE', 'NG', 'ZA', 'CI', 'SN', 'UG', 'TZ', 'RW', 'ET', 'EG', 'MA'),
            'languages' => array('en', 'fr'),
            'features' => array(
                __('12 African countries covered', 'chatgabi'),
                __('Regulatory requirements', 'chatgabi'),
                __('Cultural business practices', 'chatgabi'),
                __('Market size data', 'chatgabi'),
                __('Success case studies', 'chatgabi')
            )
        ),
        array(
            'id' => 'financial-planning-toolkit',
            'title' => __('Financial Planning Toolkit', 'chatgabi'),
            'description' => __('Excel templates and calculators for cash flow, break-even analysis, and financial projections. Includes currency converters for African markets.', 'chatgabi'),
            'file_size' => '1.8 MB',
            'format' => 'Excel + PDF Guide',
            'downloads' => 12650,
            'rating' => 4.9,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/financial-toolkit-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/financial-planning-toolkit.zip',
            'category' => 'financial-planning',
            'countries' => array('GH', 'KE', 'NG', 'ZA'),
            'languages' => array('en'),
            'features' => array(
                __('Cash flow calculator', 'chatgabi'),
                __('Break-even analysis tool', 'chatgabi'),
                __('ROI calculator', 'chatgabi'),
                __('Currency converters', 'chatgabi'),
                __('5-year projection template', 'chatgabi')
            )
        ),
        array(
            'id' => 'marketing-strategy-playbook',
            'title' => __('African Marketing Strategy Playbook', 'chatgabi'),
            'description' => __('Proven marketing strategies that work in African markets. Includes social media templates, content calendars, and local advertising tips.', 'chatgabi'),
            'file_size' => '3.1 MB',
            'format' => 'PDF + Templates',
            'downloads' => 9840,
            'rating' => 4.7,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/marketing-playbook-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/african-marketing-playbook.pdf',
            'category' => 'marketing',
            'countries' => array('GH', 'KE', 'NG', 'ZA'),
            'languages' => array('en', 'tw', 'sw', 'yo'),
            'features' => array(
                __('Social media strategies', 'chatgabi'),
                __('Content calendar templates', 'chatgabi'),
                __('Local advertising tips', 'chatgabi'),
                __('Influencer outreach guide', 'chatgabi'),
                __('ROI tracking methods', 'chatgabi')
            )
        ),
        array(
            'id' => 'startup-legal-checklist',
            'title' => __('Startup Legal Checklist', 'chatgabi'),
            'description' => __('Essential legal requirements for starting a business in Ghana, Kenya, Nigeria, and South Africa. Includes registration forms and compliance guides.', 'chatgabi'),
            'file_size' => '2.9 MB',
            'format' => 'PDF + Forms',
            'downloads' => 7230,
            'rating' => 4.8,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/legal-checklist-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/startup-legal-checklist.pdf',
            'category' => 'legal-compliance',
            'countries' => array('GH', 'KE', 'NG', 'ZA'),
            'languages' => array('en'),
            'features' => array(
                __('Business registration guide', 'chatgabi'),
                __('Tax registration steps', 'chatgabi'),
                __('Licensing requirements', 'chatgabi'),
                __('Employment law basics', 'chatgabi'),
                __('Intellectual property guide', 'chatgabi')
            )
        ),
        array(
            'id' => 'funding-opportunities-database',
            'title' => __('African Funding Opportunities Database', 'chatgabi'),
            'description' => __('Comprehensive database of grants, loans, and investment opportunities for African startups. Updated quarterly with new opportunities.', 'chatgabi'),
            'file_size' => '5.4 MB',
            'format' => 'Excel + PDF',
            'downloads' => 11200,
            'rating' => 4.9,
            'preview_image' => CHATGABI_THEME_URL . '/assets/images/lead-magnets/funding-database-preview.jpg',
            'file_path' => CHATGABI_THEME_DIR . '/assets/downloads/african-funding-database.xlsx',
            'category' => 'funding',
            'countries' => array('GH', 'KE', 'NG', 'ZA', 'CI', 'SN', 'UG', 'TZ', 'RW', 'ET'),
            'languages' => array('en', 'fr'),
            'features' => array(
                __('200+ funding sources', 'chatgabi'),
                __('Government grants', 'chatgabi'),
                __('Private investors', 'chatgabi'),
                __('International programs', 'chatgabi'),
                __('Application templates', 'chatgabi')
            )
        )
    );
}

/**
 * Handle lead magnet download request
 */
function chatgabi_handle_lead_magnet_download() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_lead_magnets_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }
    
    $magnet_id = sanitize_text_field($_POST['magnet_id']);
    $email = sanitize_email($_POST['email']);
    $name = sanitize_text_field($_POST['name'] ?? '');
    $country = sanitize_text_field($_POST['country'] ?? '');
    
    // Validate email
    if (!is_email($email)) {
        wp_send_json_error(array('message' => __('Invalid email address', 'chatgabi')));
    }
    
    // Get lead magnet data
    $magnets = chatgabi_get_lead_magnets();
    $magnet = null;
    foreach ($magnets as $m) {
        if ($m['id'] === $magnet_id) {
            $magnet = $m;
            break;
        }
    }
    
    if (!$magnet) {
        wp_send_json_error(array('message' => __('Lead magnet not found', 'chatgabi')));
    }
    
    // Store lead information
    $lead_id = chatgabi_store_lead($email, $name, $country, $magnet_id);
    
    if ($lead_id) {
        // Send download email
        $email_sent = chatgabi_send_download_email($email, $name, $magnet);
        
        if ($email_sent) {
            // Track conversion
            chatgabi_track_lead_magnet_conversion($magnet_id, $email, $country);
            
            wp_send_json_success(array(
                'message' => __('Download link sent to your email!', 'chatgabi'),
                'lead_id' => $lead_id
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to send email. Please try again.', 'chatgabi')));
        }
    } else {
        wp_send_json_error(array('message' => __('Failed to process request. Please try again.', 'chatgabi')));
    }
}

/**
 * Store lead information in database
 */
function chatgabi_store_lead($email, $name, $country, $magnet_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_leads';
    
    // Check if email already exists
    $existing_lead = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM $table_name WHERE email = %s",
        $email
    ));
    
    if ($existing_lead) {
        // Update existing lead with new download
        $wpdb->update(
            $table_name,
            array(
                'downloads' => $wpdb->get_var($wpdb->prepare(
                    "SELECT downloads FROM $table_name WHERE email = %s",
                    $email
                )) . ',' . $magnet_id,
                'last_download' => current_time('mysql'),
                'download_count' => $wpdb->get_var($wpdb->prepare(
                    "SELECT download_count FROM $table_name WHERE email = %s",
                    $email
                )) + 1
            ),
            array('email' => $email),
            array('%s', '%s', '%d'),
            array('%s')
        );
        
        return $existing_lead->id;
    } else {
        // Insert new lead
        $result = $wpdb->insert(
            $table_name,
            array(
                'email' => $email,
                'name' => $name,
                'country' => $country,
                'magnet_id' => $magnet_id,
                'downloads' => $magnet_id,
                'first_download' => current_time('mysql'),
                'last_download' => current_time('mysql'),
                'download_count' => 1,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
        
        return $result ? $wpdb->insert_id : false;
    }
}

/**
 * Send download email to user
 */
function chatgabi_send_download_email($email, $name, $magnet) {
    $subject = sprintf(__('Your %s is ready for download!', 'chatgabi'), $magnet['title']);
    
    $download_url = add_query_arg(array(
        'action' => 'chatgabi_serve_download',
        'magnet' => $magnet['id'],
        'email' => base64_encode($email),
        'token' => wp_create_nonce('download_' . $magnet['id'] . '_' . $email)
    ), admin_url('admin-ajax.php'));
    
    $message = sprintf(
        __('Hi %s,

Thank you for your interest in ChatGABI! Your free %s is ready for download.

Download Link: %s

This link will expire in 7 days for security reasons.

What\'s included:
%s

Need help getting started? Reply to this email and our African business experts will assist you.

Best regards,
The ChatGABI Team
Swiftmind - Empowering African Entrepreneurs

P.S. Don\'t forget to check out ChatGABI for AI-powered business planning: %s', 'chatgabi'),
        $name ?: __('there', 'chatgabi'),
        $magnet['title'],
        $download_url,
        implode("\n• ", $magnet['features']),
        home_url()
    );
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ChatGABI <<EMAIL>>',
        'Reply-To: ChatGABI Support <<EMAIL>>'
    );
    
    return wp_mail($email, $subject, $message, $headers);
}

/**
 * Track lead magnet conversion
 */
function chatgabi_track_lead_magnet_conversion($magnet_id, $email, $country) {
    // Track in conversion system
    if (function_exists('chatgabi_handle_conversion_tracking')) {
        $conversion_data = array(
            'event_type' => 'lead_magnet_download',
            'event_data' => json_encode(array(
                'magnet_id' => $magnet_id,
                'email' => $email,
                'country' => $country,
                'timestamp' => current_time('mysql')
            )),
            'user_id' => 0,
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'page_url' => $_SERVER['HTTP_REFERER'] ?? '',
            'timestamp' => current_time('mysql')
        );
        
        global $wpdb;
        $wpdb->insert(
            $wpdb->prefix . 'chatgabi_conversions',
            $conversion_data,
            array('%s', '%s', '%d', '%s', '%s', '%s', '%s')
        );
    }
}

/**
 * Create leads table
 */
function chatgabi_create_lead_magnets_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_leads';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        email varchar(255) NOT NULL,
        name varchar(255),
        country varchar(10),
        magnet_id varchar(100),
        downloads text,
        first_download datetime,
        last_download datetime,
        download_count int DEFAULT 1,
        ip_address varchar(45),
        user_agent text,
        subscribed tinyint(1) DEFAULT 1,
        unsubscribed_at datetime NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY email (email),
        KEY country (country),
        KEY magnet_id (magnet_id),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Serve download file
 */
function chatgabi_serve_download() {
    if (isset($_GET['action']) && $_GET['action'] === 'chatgabi_serve_download') {
        $magnet_id = sanitize_text_field($_GET['magnet']);
        $email = base64_decode($_GET['email']);
        $token = sanitize_text_field($_GET['token']);
        
        // Verify token
        if (!wp_verify_nonce($token, 'download_' . $magnet_id . '_' . $email)) {
            wp_die(__('Invalid download link', 'chatgabi'));
        }
        
        // Get magnet data
        $magnets = chatgabi_get_lead_magnets();
        $magnet = null;
        foreach ($magnets as $m) {
            if ($m['id'] === $magnet_id) {
                $magnet = $m;
                break;
            }
        }
        
        if (!$magnet || !file_exists($magnet['file_path'])) {
            wp_die(__('File not found', 'chatgabi'));
        }
        
        // Serve file
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($magnet['file_path']) . '"');
        header('Content-Length: ' . filesize($magnet['file_path']));
        readfile($magnet['file_path']);
        exit;
    }
}
add_action('init', 'chatgabi_serve_download');

/**
 * Get lead magnet by ID
 */
function chatgabi_get_lead_magnet_by_id($magnet_id) {
    $magnets = chatgabi_get_lead_magnets();
    foreach ($magnets as $magnet) {
        if ($magnet['id'] === $magnet_id) {
            return $magnet;
        }
    }
    return null;
}
