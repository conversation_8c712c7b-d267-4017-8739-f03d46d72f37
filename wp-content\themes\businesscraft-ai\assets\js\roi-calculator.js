/**
 * Interactive ROI Calculator JavaScript
 * 
 * Handles real-time calculations, currency conversions,
 * industry insights, and lead generation integration.
 */

(function($) {
    'use strict';

    const ROICalculator = {
        
        // Configuration
        config: {
            chatgabiBaseCost: 50, // Base monthly cost in USD
            timeSavingsPercent: 90, // Percentage of time saved
            consultantReductionPercent: 80 // Percentage reduction in consultant costs
        },
        
        // Exchange rates (would be loaded from API in production)
        exchangeRates: {
            'USD': 1,
            'GHS': 12.5,  // Ghana Cedi
            'KES': 150,   // Kenyan Shilling
            'NGN': 800,   // Nigerian Naira
            'ZAR': 18.5,  // South African Rand
            'XOF': 600    // West African CFA Franc
        },
        
        // Currency symbols
        currencySymbols: {
            'USD': '$',
            'GHS': '₵',
            'KES': 'KSh',
            'NGN': '₦',
            'ZAR': 'R',
            'XOF': 'CFA'
        },
        
        // Industry multipliers for insights
        industryMultipliers: {
            'technology': { time: 0.85, cost: 0.75 },
            'agriculture': { time: 0.90, cost: 0.85 },
            'retail': { time: 0.88, cost: 0.80 },
            'manufacturing': { time: 0.92, cost: 0.88 },
            'services': { time: 0.85, cost: 0.75 },
            'healthcare': { time: 0.87, cost: 0.82 },
            'education': { time: 0.89, cost: 0.85 },
            'finance': { time: 0.83, cost: 0.70 },
            'tourism': { time: 0.90, cost: 0.85 },
            'other': { time: 0.88, cost: 0.80 }
        },
        
        // Country insights
        countryInsights: {
            'GH': {
                name: 'Ghana',
                insight: 'Ghanaian entrepreneurs report 40% faster business plan completion',
                avgSavings: 2200
            },
            'KE': {
                name: 'Kenya',
                insight: 'Kenyan startups save an average of 25 hours monthly on planning',
                avgSavings: 1800
            },
            'NG': {
                name: 'Nigeria',
                insight: 'Nigerian businesses see 60% reduction in consultant dependency',
                avgSavings: 2800
            },
            'ZA': {
                name: 'South Africa',
                insight: 'South African companies achieve 45% cost reduction in first year',
                avgSavings: 3200
            },
            'CI': {
                name: 'Côte d\'Ivoire',
                insight: 'Ivorian businesses report 50% improvement in planning efficiency',
                avgSavings: 1600
            },
            'OTHER': {
                name: 'Africa',
                insight: 'African entrepreneurs save an average of 20 hours monthly',
                avgSavings: 2000
            }
        },
        
        // Current state
        state: {
            businessType: 'startup',
            industry: 'technology',
            country: 'GH',
            consultantCost: 500,
            planningHours: 20,
            hourlyRate: 25,
            currency: 'GHS',
            currencySymbol: '₵'
        },
        
        // Initialize calculator
        init: function() {
            this.bindEvents();
            this.initializeValues();
            this.calculateROI();
            this.updateInsights();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Input changes
            $('#business-type').on('change', (e) => {
                this.state.businessType = e.target.value;
                this.calculateROI();
            });
            
            $('#industry').on('change', (e) => {
                this.state.industry = e.target.value;
                this.calculateROI();
                this.updateInsights();
            });
            
            $('#country').on('change', (e) => {
                this.state.country = e.target.value;
                this.updateCurrency();
                this.calculateROI();
                this.updateInsights();
            });
            
            // Slider inputs
            $('#consultant-cost').on('input', (e) => {
                this.state.consultantCost = parseInt(e.target.value);
                $('#consultant-cost-value').text(this.state.consultantCost);
                this.calculateROI();
            });
            
            $('#planning-hours').on('input', (e) => {
                this.state.planningHours = parseInt(e.target.value);
                $('#planning-hours-value').text(this.state.planningHours);
                this.calculateROI();
            });
            
            $('#hourly-rate').on('input', (e) => {
                this.state.hourlyRate = parseInt(e.target.value);
                $('#hourly-rate-value').text(this.state.hourlyRate);
                this.calculateROI();
            });
            
            // CTA buttons
            $('#roi-calculator-signup').on('click', () => {
                this.trackConversion('signup_from_calculator');
            });
            
            $('#email-roi-report').on('click', () => {
                this.showEmailModal();
            });
            
            // Modal events
            $('#roi-modal-close, .roi-email-modal-overlay').on('click', (e) => {
                if (e.target === e.currentTarget) {
                    this.hideEmailModal();
                }
            });
            
            $('#roi-email-form').on('submit', (e) => {
                this.handleEmailSubmission(e);
            });
            
            // Track calculator interactions
            $('.calculator-input, .calculator-slider').on('change input', () => {
                this.trackInteraction();
            });
        },
        
        // Initialize default values
        initializeValues: function() {
            // Set default country based on user location if available
            if (window.chatgabiAjax && window.chatgabiAjax.userCountry) {
                this.state.country = window.chatgabiAjax.userCountry;
                $('#country').val(this.state.country);
            }
            
            this.updateCurrency();
            
            // Set slider values
            $('#consultant-cost-value').text(this.state.consultantCost);
            $('#planning-hours-value').text(this.state.planningHours);
            $('#hourly-rate-value').text(this.state.hourlyRate);
        },
        
        // Update currency based on country
        updateCurrency: function() {
            const currencyMap = {
                'GH': 'GHS',
                'KE': 'KES',
                'NG': 'NGN',
                'ZA': 'ZAR',
                'CI': 'XOF',
                'SN': 'XOF',
                'OTHER': 'USD'
            };
            
            this.state.currency = currencyMap[this.state.country] || 'USD';
            this.state.currencySymbol = this.currencySymbols[this.state.currency];
            
            // Update currency symbols in UI
            $('.currency-symbol').text(this.state.currencySymbol);
            $('#currency-symbol').text(this.state.currencySymbol);
        },
        
        // Calculate ROI
        calculateROI: function() {
            const exchangeRate = this.exchangeRates[this.state.currency];
            const industryMultiplier = this.industryMultipliers[this.state.industry];
            
            // Current costs (in local currency)
            const currentConsultantCost = this.state.consultantCost;
            const currentTimeCost = this.state.planningHours * this.state.hourlyRate;
            const totalCurrentCost = currentConsultantCost + currentTimeCost;
            
            // ChatGABI costs (convert from USD to local currency)
            const chatgabiCostLocal = Math.round(this.config.chatgabiBaseCost * exchangeRate);
            
            // Reduced costs with ChatGABI
            const reducedConsultantCost = currentConsultantCost * (1 - this.config.consultantReductionPercent / 100);
            const reducedTimeCost = currentTimeCost * (1 - this.config.timeSavingsPercent / 100) * industryMultiplier.time;
            const totalChatgabiCost = chatgabiCostLocal + reducedConsultantCost + reducedTimeCost;
            
            // Savings calculations
            const monthlySavings = totalCurrentCost - totalChatgabiCost;
            const annualSavings = monthlySavings * 12;
            const roiPercentage = totalChatgabiCost > 0 ? ((annualSavings / (chatgabiCostLocal * 12)) * 100) : 0;
            
            // Time savings
            const hoursSaved = this.state.planningHours * (this.config.timeSavingsPercent / 100) * industryMultiplier.time;
            const daysSaved = hoursSaved / 8; // Assuming 8-hour work day
            
            // Update UI
            this.updateCostDisplay(currentConsultantCost, currentTimeCost, totalCurrentCost);
            this.updateChatgabiCostDisplay(chatgabiCostLocal, reducedTimeCost, totalChatgabiCost);
            this.updateSavingsDisplay(monthlySavings, annualSavings, roiPercentage);
            this.updateTimeDisplay(hoursSaved, daysSaved);
        },
        
        // Update current cost display
        updateCostDisplay: function(consultantCost, timeCost, totalCost) {
            $('#current-consultant-cost').text(this.formatCurrency(consultantCost));
            $('#current-time-cost').text(this.formatCurrency(timeCost));
            $('#total-current-cost').text(this.formatCurrency(totalCost));
        },
        
        // Update ChatGABI cost display
        updateChatgabiCostDisplay: function(chatgabiCost, reducedTimeCost, totalCost) {
            $('#chatgabi-cost').text(this.formatCurrency(chatgabiCost));
            $('#reduced-time-cost').text(this.formatCurrency(reducedTimeCost));
            $('#total-chatgabi-cost').text(this.formatCurrency(totalCost));
        },
        
        // Update savings display
        updateSavingsDisplay: function(monthly, annual, roi) {
            $('#monthly-savings').text(this.formatCurrency(monthly));
            $('#annual-savings').text(this.formatCurrency(annual));
            $('#roi-percentage').text(Math.round(roi) + '%');
        },
        
        // Update time display
        updateTimeDisplay: function(hours, days) {
            $('#hours-saved').text(Math.round(hours));
            $('#days-saved').text(days.toFixed(1));
        },
        
        // Update industry and country insights
        updateInsights: function() {
            const industryMultiplier = this.industryMultipliers[this.state.industry];
            const countryData = this.countryInsights[this.state.country];
            
            // Industry insight
            const industryText = this.getIndustryInsightText(this.state.industry, industryMultiplier);
            $('#industry-insight .insight-text').text(industryText);
            
            // Country insight
            $('#country-insight .insight-text').text(countryData.insight);
        },
        
        // Get industry-specific insight text
        getIndustryInsightText: function(industry, multiplier) {
            const timeSavings = Math.round((1 - multiplier.time) * 100);
            const industryNames = {
                'technology': 'Technology',
                'agriculture': 'Agriculture',
                'retail': 'Retail',
                'manufacturing': 'Manufacturing',
                'services': 'Professional Services',
                'healthcare': 'Healthcare',
                'education': 'Education',
                'finance': 'Finance',
                'tourism': 'Tourism',
                'other': 'Other'
            };
            
            return `${industryNames[industry]} businesses typically save ${timeSavings}% on planning time with ChatGABI`;
        },
        
        // Format currency
        formatCurrency: function(amount) {
            return this.state.currencySymbol + Math.round(amount).toLocaleString();
        },
        
        // Show email modal
        showEmailModal: function() {
            $('#roi-email-modal').addClass('show');
            this.trackInteraction('email_modal_opened');
        },
        
        // Hide email modal
        hideEmailModal: function() {
            $('#roi-email-modal').removeClass('show');
        },
        
        // Handle email form submission
        handleEmailSubmission: function(e) {
            e.preventDefault();
            
            const formData = {
                email: $('#roi-email').val(),
                name: $('#roi-name').val(),
                company: $('#roi-company').val(),
                calculatorData: this.state
            };
            
            // Disable submit button
            const $submitBtn = $('.submit-btn');
            const originalText = $submitBtn.find('.btn-text').text();
            $submitBtn.find('.btn-text').text('Sending...');
            $submitBtn.prop('disabled', true);
            
            // Send email request
            $.ajax({
                url: window.chatgabiROI.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_send_roi_report',
                    nonce: window.chatgabiROI.nonce,
                    ...formData
                },
                success: (response) => {
                    if (response.success) {
                        this.showSuccessMessage();
                        this.trackConversion('roi_report_requested');
                    } else {
                        this.showErrorMessage(response.data.message);
                    }
                },
                error: () => {
                    this.showErrorMessage('Failed to send report. Please try again.');
                },
                complete: () => {
                    $submitBtn.find('.btn-text').text(originalText);
                    $submitBtn.prop('disabled', false);
                }
            });
        },
        
        // Show success message
        showSuccessMessage: function() {
            const successHtml = `
                <div class="success-message">
                    <div class="success-icon">✅</div>
                    <h4>ROI Report Sent!</h4>
                    <p>Check your email for your detailed ROI analysis and implementation guide.</p>
                    <button class="success-close-btn" onclick="ROICalculator.hideEmailModal()">Close</button>
                </div>
            `;
            $('.modal-content').html(successHtml);
        },
        
        // Show error message
        showErrorMessage: function(message) {
            const errorHtml = `<div class="error-message" style="color: #dc3545; margin-bottom: 1rem;">${message}</div>`;
            $('.modal-content').prepend(errorHtml);
            
            setTimeout(() => {
                $('.error-message').fadeOut(() => $('.error-message').remove());
            }, 5000);
        },
        
        // Track calculator interactions
        trackInteraction: function(action = 'calculator_interaction') {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion(action, {
                    business_type: this.state.businessType,
                    industry: this.state.industry,
                    country: this.state.country,
                    section: 'roi_calculator'
                });
            }
        },
        
        // Track conversions
        trackConversion: function(conversionType) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion(conversionType, {
                    calculator_data: this.state,
                    section: 'roi_calculator'
                });
            }
            
            // Track A/B test conversion if available
            if (window.ChatGABI && window.ChatGABI.ABTesting) {
                window.ChatGABI.ABTesting.trackConversion('roi_calculator_test', conversionType);
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize ROI calculator
        if ($('.interactive-roi-calculator-section').length) {
            ROICalculator.init();
        }
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.ROICalculator = ROICalculator;
    window.ROICalculator = ROICalculator; // For inline onclick handlers

})(jQuery);
