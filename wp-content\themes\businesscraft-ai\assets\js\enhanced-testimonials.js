/**
 * Enhanced Testimonials JavaScript
 * 
 * Handles testimonial filtering, rotation, loading,
 * and interactive elements for conversion optimization.
 */

(function($) {
    'use strict';

    const EnhancedTestimonials = {
        
        // Configuration
        config: {
            rotationInterval: 8000, // 8 seconds
            loadMoreCount: 3,
            animationDuration: 500
        },
        
        // State
        state: {
            currentFilter: 'all',
            loadedCount: 3,
            totalCount: 12,
            isRotating: false
        },
        
        // Initialize enhanced testimonials
        init: function() {
            this.bindEvents();
            this.initFiltering();
            this.initLoadMore();
            this.initVideoTestimonials();
            this.trackTestimonialInteractions();
            this.startRotation();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Filter buttons
            $('.filter-btn').on('click', (e) => {
                this.handleFilterClick(e);
            });
            
            // Load more button
            $('#load-more-testimonials').on('click', () => {
                this.loadMoreTestimonials();
            });
            
            // Case study links
            $(document).on('click', '.case-study-btn', (e) => {
                this.handleCaseStudyClick(e);
            });
            
            // Business links
            $(document).on('click', '.business-link', (e) => {
                this.trackBusinessLinkClick(e);
            });
            
            // Video testimonials
            $(document).on('click', '.video-testimonial', (e) => {
                this.handleVideoClick(e);
            });
        },
        
        // Initialize filtering functionality
        initFiltering: function() {
            // Set initial filter based on user country
            const userCountry = this.getUserCountry();
            if (userCountry && userCountry !== 'unknown') {
                this.applyFilter(userCountry);
                $(`.filter-btn[data-filter="${userCountry}"]`).addClass('active');
                $('.filter-btn[data-filter="all"]').removeClass('active');
            }
        },
        
        // Handle filter button clicks
        handleFilterClick: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const filter = $button.data('filter');
            
            // Update active state
            $('.filter-btn').removeClass('active');
            $button.addClass('active');
            
            // Apply filter
            this.applyFilter(filter);
            this.state.currentFilter = filter;
            
            // Track filter usage
            this.trackFilterUsage(filter);
        },
        
        // Apply testimonial filter
        applyFilter: function(filter) {
            const $cards = $('.testimonial-card.enhanced');
            
            if (filter === 'all') {
                $cards.fadeIn(this.config.animationDuration);
            } else {
                $cards.each(function() {
                    const $card = $(this);
                    const cardCountry = $card.data('country');
                    
                    if (cardCountry === filter) {
                        $card.fadeIn(this.config.animationDuration);
                    } else {
                        $card.fadeOut(this.config.animationDuration);
                    }
                }.bind(this));
            }
        },
        
        // Initialize load more functionality
        initLoadMore: function() {
            this.updateLoadMoreButton();
        },
        
        // Load more testimonials
        loadMoreTestimonials: function() {
            const $button = $('#load-more-testimonials');
            const $buttonText = $button.find('.btn-text');
            const originalText = $buttonText.text();
            
            // Show loading state
            $buttonText.text(chatgabiTestimonials.strings.loading);
            $button.prop('disabled', true);
            
            // Make AJAX request
            $.ajax({
                url: chatgabiTestimonials.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_testimonials',
                    nonce: chatgabiTestimonials.nonce,
                    count: this.config.loadMoreCount,
                    offset: this.state.loadedCount
                },
                success: (response) => {
                    if (response.success && response.data.length > 0) {
                        this.addTestimonials(response.data);
                        this.state.loadedCount += response.data.length;
                        this.updateLoadMoreButton();
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Failed to load testimonials:', error);
                },
                complete: () => {
                    $buttonText.text(originalText);
                    $button.prop('disabled', false);
                }
            });
            
            // Track load more usage
            this.trackLoadMoreUsage();
        },
        
        // Add new testimonials to grid
        addTestimonials: function(testimonials) {
            const $grid = $('#testimonials-grid');
            
            testimonials.forEach((testimonial) => {
                const $card = this.createTestimonialCard(testimonial);
                $card.hide().appendTo($grid).fadeIn(this.config.animationDuration);
            });
            
            // Apply current filter to new cards
            if (this.state.currentFilter !== 'all') {
                this.applyFilter(this.state.currentFilter);
            }
        },
        
        // Create testimonial card HTML
        createTestimonialCard: function(testimonial) {
            const verifiedBadge = testimonial.verified ? 
                `<div class="verified-badge" title="${chatgabiTestimonials.strings.verified_business}">
                    <span class="verified-icon">✓</span>
                </div>` : '';
            
            const businessLogo = testimonial.business_logo ? 
                `<div class="business-logo">
                    <img src="${testimonial.business_logo}" alt="${testimonial.name} Business Logo" loading="lazy">
                </div>` : '';
            
            const stars = Array.from({length: 5}, (_, i) => 
                `<span class="star ${i < testimonial.rating ? 'filled' : ''}">★</span>`
            ).join('');
            
            const results = testimonial.results ? 
                `<div class="testimonial-results">
                    <h5>Results Achieved:</h5>
                    <div class="results-grid">
                        ${Object.entries(testimonial.results).map(([key, value]) => 
                            `<div class="result-item">
                                <span class="result-value">${value}</span>
                                <span class="result-label">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>` : '';
            
            const caseStudyBtn = testimonial.case_study_url ? 
                `<a href="${testimonial.case_study_url}" class="case-study-btn">
                    <span class="btn-icon">📊</span>
                    ${chatgabiTestimonials.strings.case_study}
                </a>` : '';
            
            const businessLink = testimonial.business_url ? 
                `<a href="${testimonial.business_url}" target="_blank" rel="noopener noreferrer" class="business-link">
                    <span class="btn-icon">🔗</span>
                    Visit Business
                </a>` : '';
            
            return $(`
                <div class="testimonial-card enhanced" data-country="${testimonial.country_code}" data-business="${testimonial.business_type}">
                    <div class="testimonial-header">
                        <div class="testimonial-avatar-section">
                            <img src="${testimonial.avatar}" alt="${testimonial.name}" class="testimonial-avatar" loading="lazy">
                            ${verifiedBadge}
                        </div>
                        <div class="testimonial-info">
                            <h4 class="testimonial-name">${testimonial.name}</h4>
                            <p class="testimonial-title">${testimonial.title}</p>
                            <p class="testimonial-location">
                                <span class="country-flag">${this.getCountryFlag(testimonial.country_code)}</span>
                                ${testimonial.country}
                            </p>
                            <div class="business-type-tag">${testimonial.business_type}</div>
                        </div>
                        ${businessLogo}
                    </div>
                    <div class="testimonial-rating">${stars}</div>
                    <blockquote class="testimonial-quote">"${testimonial.quote}"</blockquote>
                    ${results}
                    <div class="testimonial-actions">
                        ${caseStudyBtn}
                        ${businessLink}
                    </div>
                </div>
            `);
        },
        
        // Update load more button state
        updateLoadMoreButton: function() {
            const $button = $('#load-more-testimonials');
            const $note = $('.load-more-note');
            
            if (this.state.loadedCount >= this.state.totalCount) {
                $button.hide();
                $note.text('All testimonials loaded');
            } else {
                $note.text(`Showing ${this.state.loadedCount} of ${this.state.totalCount}+ verified testimonials`);
            }
        },
        
        // Initialize video testimonials
        initVideoTestimonials: function() {
            // Add click handlers for video thumbnails
            $('.video-testimonial').on('click', (e) => {
                this.handleVideoClick(e);
            });
        },
        
        // Handle video testimonial clicks
        handleVideoClick: function(e) {
            e.preventDefault();
            const $video = $(e.currentTarget);
            const videoTitle = $video.find('h4').text();
            
            // Track video interaction
            this.trackVideoInteraction(videoTitle);
            
            // For now, show a modal or redirect to video
            // In a real implementation, you'd integrate with a video player
            this.showVideoModal(videoTitle);
        },
        
        // Show video modal (placeholder)
        showVideoModal: function(title) {
            // Create a simple modal for video
            const modal = $(`
                <div class="video-modal-overlay">
                    <div class="video-modal">
                        <div class="video-modal-header">
                            <h3>${title}</h3>
                            <button class="video-modal-close">&times;</button>
                        </div>
                        <div class="video-modal-content">
                            <p>Video testimonial would play here.</p>
                            <p>Integration with video hosting service (YouTube, Vimeo, etc.) would be implemented.</p>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(modal);
            modal.fadeIn(300);
            
            // Close modal handlers
            modal.find('.video-modal-close, .video-modal-overlay').on('click', (e) => {
                if (e.target === e.currentTarget) {
                    modal.fadeOut(300, () => modal.remove());
                }
            });
        },
        
        // Start testimonial rotation
        startRotation: function() {
            if (this.state.isRotating) return;
            
            this.state.isRotating = true;
            setInterval(() => {
                this.rotateTestimonials();
            }, chatgabiTestimonials.rotationInterval);
        },
        
        // Rotate testimonials
        rotateTestimonials: function() {
            // Only rotate if showing all testimonials
            if (this.state.currentFilter !== 'all') return;
            
            // Get new set of testimonials
            $.ajax({
                url: chatgabiTestimonials.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_testimonials',
                    nonce: chatgabiTestimonials.nonce,
                    count: 3
                },
                success: (response) => {
                    if (response.success && response.data.length > 0) {
                        this.replaceTestimonials(response.data);
                    }
                }
            });
        },
        
        // Replace current testimonials with new ones
        replaceTestimonials: function(newTestimonials) {
            const $grid = $('#testimonials-grid');
            const $currentCards = $grid.find('.testimonial-card.enhanced');
            
            // Fade out current cards
            $currentCards.fadeOut(this.config.animationDuration, () => {
                $currentCards.remove();
                
                // Add new cards
                newTestimonials.forEach((testimonial) => {
                    const $card = this.createTestimonialCard(testimonial);
                    $card.hide().appendTo($grid).fadeIn(this.config.animationDuration);
                });
            });
        },
        
        // Handle case study clicks
        handleCaseStudyClick: function(e) {
            e.preventDefault();
            const $link = $(e.currentTarget);
            const href = $link.attr('href');
            
            // Track case study interaction
            this.trackCaseStudyInteraction(href);
            
            // For now, show placeholder modal
            this.showCaseStudyModal();
        },
        
        // Show case study modal (placeholder)
        showCaseStudyModal: function() {
            const modal = $(`
                <div class="case-study-modal-overlay">
                    <div class="case-study-modal">
                        <div class="case-study-modal-header">
                            <h3>Detailed Case Study</h3>
                            <button class="case-study-modal-close">&times;</button>
                        </div>
                        <div class="case-study-modal-content">
                            <p>Detailed case study with metrics, timeline, and results would be displayed here.</p>
                            <p>This would include charts, graphs, and detailed analysis of the business transformation.</p>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(modal);
            modal.fadeIn(300);
            
            // Close modal handlers
            modal.find('.case-study-modal-close, .case-study-modal-overlay').on('click', (e) => {
                if (e.target === e.currentTarget) {
                    modal.fadeOut(300, () => modal.remove());
                }
            });
        },
        
        // Track testimonial interactions
        trackTestimonialInteractions: function() {
            // Track testimonial card views
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const $card = $(entry.target);
                        const testimonialName = $card.find('.testimonial-name').text();
                        this.trackTestimonialView(testimonialName);
                    }
                });
            }, { threshold: 0.5 });
            
            $('.testimonial-card.enhanced').each(function() {
                observer.observe(this);
            });
        },
        
        // Tracking functions
        trackFilterUsage: function(filter) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('testimonial_filter_used', {
                    filter: filter,
                    section: 'testimonials'
                });
            }
        },
        
        trackLoadMoreUsage: function() {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('testimonials_load_more', {
                    loaded_count: this.state.loadedCount,
                    section: 'testimonials'
                });
            }
        },
        
        trackTestimonialView: function(name) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('testimonial_viewed', {
                    testimonial_name: name,
                    section: 'testimonials'
                });
            }
        },
        
        trackCaseStudyInteraction: function(href) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('case_study_clicked', {
                    case_study_url: href,
                    section: 'testimonials'
                });
            }
        },
        
        trackBusinessLinkClick: function(e) {
            const href = $(e.currentTarget).attr('href');
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('business_link_clicked', {
                    business_url: href,
                    section: 'testimonials'
                });
            }
        },
        
        trackVideoInteraction: function(title) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('video_testimonial_clicked', {
                    video_title: title,
                    section: 'testimonials'
                });
            }
        },
        
        // Helper functions
        getUserCountry: function() {
            // Try to get user country from various sources
            if (window.chatgabiAjax && window.chatgabiAjax.strings && window.chatgabiAjax.strings.detectedCountry) {
                return window.chatgabiAjax.strings.detectedCountry;
            }
            return 'unknown';
        },
        
        getCountryFlag: function(countryCode) {
            const flags = {
                'GH': '🇬🇭',
                'KE': '🇰🇪',
                'NG': '🇳🇬',
                'ZA': '🇿🇦',
                'CI': '🇨🇮'
            };
            return flags[countryCode] || '🌍';
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Add modal styles
        const modalStyles = `
            <style>
            .video-modal-overlay,
            .case-study-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
            }
            
            .video-modal,
            .case-study-modal {
                background: white;
                border-radius: 16px;
                max-width: 600px;
                width: 100%;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .video-modal-header,
            .case-study-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1.5rem;
                border-bottom: 1px solid #dee2e6;
            }
            
            .video-modal-close,
            .case-study-modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6c757d;
            }
            
            .video-modal-content,
            .case-study-modal-content {
                padding: 1.5rem;
            }
            </style>
        `;
        
        $('head').append(modalStyles);
        
        // Initialize enhanced testimonials
        if ($('.enhanced-testimonials-section').length) {
            EnhancedTestimonials.init();
        }
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.EnhancedTestimonials = EnhancedTestimonials;

})(jQuery);
