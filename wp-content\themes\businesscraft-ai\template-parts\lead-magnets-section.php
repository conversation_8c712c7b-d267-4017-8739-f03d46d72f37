<?php
/**
 * Lead Magnets Section Template Part
 *
 * Displays free downloadable resources with email capture
 * for lead generation and conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get lead magnets data
$lead_magnets = chatgabi_get_lead_magnets();
$featured_magnets = array_slice($lead_magnets, 0, 3); // Show first 3 initially
$user_country = chatgabi_get_user_country();
?>

<section class="lead-magnets-section">
    <div class="container">

        <!-- Section Header -->
        <div class="lead-magnets-header">
            <h2><?php _e('Free Resources for African Entrepreneurs', 'chatgabi'); ?></h2>
            <p class="lead-magnets-subtitle">
                <?php _e('Download proven templates, guides, and tools to accelerate your business growth. Trusted by 50,000+ entrepreneurs across Africa.', 'chatgabi'); ?>
            </p>
        </div>

        <!-- Featured Lead Magnets -->
        <div class="lead-magnets-grid">
            <?php foreach ($featured_magnets as $magnet): ?>
                <div class="lead-magnet-card" data-magnet-id="<?php echo esc_attr($magnet['id']); ?>">

                    <!-- Magnet Preview -->
                    <div class="magnet-preview">
                        <img src="<?php echo esc_url($magnet['preview_image']); ?>"
                             alt="<?php echo esc_attr($magnet['title']); ?> Preview"
                             class="magnet-preview-image"
                             loading="lazy">
                        <div class="magnet-overlay">
                            <div class="magnet-stats">
                                <div class="stat-item">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-value"><?php echo esc_html($magnet['rating']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">📥</span>
                                    <span class="stat-value"><?php echo number_format($magnet['downloads']); ?>+</span>
                                </div>
                            </div>
                            <button class="preview-btn" data-magnet-id="<?php echo esc_attr($magnet['id']); ?>">
                                <span class="btn-icon">👁️</span>
                                <?php _e('Preview', 'chatgabi'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Magnet Content -->
                    <div class="magnet-content">
                        <h3 class="magnet-title"><?php echo esc_html($magnet['title']); ?></h3>
                        <p class="magnet-description"><?php echo esc_html($magnet['description']); ?></p>

                        <!-- Magnet Features -->
                        <div class="magnet-features">
                            <h4><?php _e('What\'s Included:', 'chatgabi'); ?></h4>
                            <ul class="features-list">
                                <?php foreach (array_slice($magnet['features'], 0, 3) as $feature): ?>
                                    <li>
                                        <span class="feature-icon">✅</span>
                                        <?php echo esc_html($feature); ?>
                                    </li>
                                <?php endforeach; ?>
                                <?php if (count($magnet['features']) > 3): ?>
                                    <li class="features-more">
                                        <span class="feature-icon">➕</span>
                                        <?php printf(__('And %d more features...', 'chatgabi'), count($magnet['features']) - 3); ?>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <!-- Magnet Meta -->
                        <div class="magnet-meta">
                            <div class="meta-item">
                                <span class="meta-icon">📄</span>
                                <span class="meta-text"><?php echo esc_html($magnet['format']); ?></span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-icon">💾</span>
                                <span class="meta-text"><?php echo esc_html($magnet['file_size']); ?></span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-icon">🌍</span>
                                <span class="meta-text"><?php echo count($magnet['countries']); ?> <?php _e('countries', 'chatgabi'); ?></span>
                            </div>
                        </div>

                        <!-- Download Button -->
                        <button class="download-btn" data-magnet-id="<?php echo esc_attr($magnet['id']); ?>">
                            <span class="btn-icon">📥</span>
                            <span class="btn-text"><?php _e('Download Free', 'chatgabi'); ?></span>
                            <span class="btn-subtext"><?php _e('No credit card required', 'chatgabi'); ?></span>
                        </button>

                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- View All Resources Button -->
        <div class="view-all-resources">
            <button class="view-all-btn" id="view-all-resources">
                <?php _e('View All 6 Free Resources', 'chatgabi'); ?>
                <span class="btn-arrow">→</span>
            </button>
        </div>

        <!-- Social Proof Section -->
        <div class="lead-magnets-social-proof">
            <h3><?php _e('Join 50,000+ Entrepreneurs Who Downloaded Our Resources', 'chatgabi'); ?></h3>
            <div class="social-proof-stats">
                <div class="proof-stat">
                    <span class="proof-number">50,000+</span>
                    <span class="proof-label"><?php _e('Downloads', 'chatgabi'); ?></span>
                </div>
                <div class="proof-stat">
                    <span class="proof-number">4.9/5</span>
                    <span class="proof-label"><?php _e('Average Rating', 'chatgabi'); ?></span>
                </div>
                <div class="proof-stat">
                    <span class="proof-number">12</span>
                    <span class="proof-label"><?php _e('Countries', 'chatgabi'); ?></span>
                </div>
                <div class="proof-stat">
                    <span class="proof-number">5</span>
                    <span class="proof-label"><?php _e('Languages', 'chatgabi'); ?></span>
                </div>
            </div>

            <!-- Recent Downloads -->
            <div class="recent-downloads">
                <p class="recent-downloads-title"><?php _e('Recent Downloads:', 'chatgabi'); ?></p>
                <div class="recent-downloads-list" id="recent-downloads-list">
                    <div class="recent-download-item">
                        <span class="download-text"><?php _e('Kwame from Ghana downloaded Business Plan Template', 'chatgabi'); ?></span>
                        <span class="download-time"><?php _e('2 minutes ago', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<!-- Download Modal -->
<div class="download-modal-overlay" id="download-modal-overlay">
    <div class="download-modal">
        <div class="download-modal-header">
            <h3 id="download-modal-title"><?php _e('Download Free Resource', 'chatgabi'); ?></h3>
            <button class="download-modal-close" id="download-modal-close">&times;</button>
        </div>
        <div class="download-modal-content">
            <div class="download-modal-preview">
                <img id="download-modal-image" src="" alt="Resource Preview">
                <div class="download-modal-stats">
                    <div class="modal-stat">
                        <span class="modal-stat-icon">⭐</span>
                        <span class="modal-stat-value" id="download-modal-rating">4.9</span>
                    </div>
                    <div class="modal-stat">
                        <span class="modal-stat-icon">📥</span>
                        <span class="modal-stat-value" id="download-modal-downloads">15,420+</span>
                    </div>
                </div>
            </div>
            <div class="download-modal-form">
                <p class="download-modal-description" id="download-modal-description"></p>

                <form id="download-form" class="download-form">
                    <div class="form-group">
                        <label for="download-email"><?php _e('Email Address', 'chatgabi'); ?> *</label>
                        <input type="email"
                               id="download-email"
                               name="email"
                               required
                               placeholder="<?php _e('Enter your email address', 'chatgabi'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="download-name"><?php _e('First Name', 'chatgabi'); ?></label>
                        <input type="text"
                               id="download-name"
                               name="name"
                               placeholder="<?php _e('Enter your first name (optional)', 'chatgabi'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="download-country"><?php _e('Country', 'chatgabi'); ?></label>
                        <select id="download-country" name="country">
                            <option value=""><?php _e('Select your country', 'chatgabi'); ?></option>
                            <option value="GH" <?php selected($user_country, 'GH'); ?>>🇬🇭 <?php _e('Ghana', 'chatgabi'); ?></option>
                            <option value="KE" <?php selected($user_country, 'KE'); ?>>🇰🇪 <?php _e('Kenya', 'chatgabi'); ?></option>
                            <option value="NG" <?php selected($user_country, 'NG'); ?>>🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?></option>
                            <option value="ZA" <?php selected($user_country, 'ZA'); ?>>🇿🇦 <?php _e('South Africa', 'chatgabi'); ?></option>
                            <option value="CI">🇨🇮 <?php _e('Côte d\'Ivoire', 'chatgabi'); ?></option>
                            <option value="SN">🇸🇳 <?php _e('Senegal', 'chatgabi'); ?></option>
                            <option value="UG">🇺🇬 <?php _e('Uganda', 'chatgabi'); ?></option>
                            <option value="TZ">🇹🇿 <?php _e('Tanzania', 'chatgabi'); ?></option>
                            <option value="RW">🇷🇼 <?php _e('Rwanda', 'chatgabi'); ?></option>
                            <option value="ET">🇪🇹 <?php _e('Ethiopia', 'chatgabi'); ?></option>
                            <option value="OTHER"><?php _e('Other', 'chatgabi'); ?></option>
                        </select>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="download-newsletter" name="newsletter" checked>
                            <span class="checkbox-custom"></span>
                            <?php _e('Yes, send me weekly business tips and updates (you can unsubscribe anytime)', 'chatgabi'); ?>
                        </label>
                    </div>

                    <button type="submit" class="download-submit-btn" id="download-submit-btn">
                        <span class="btn-icon">📥</span>
                        <span class="btn-text"><?php _e('Send Download Link', 'chatgabi'); ?></span>
                    </button>

                    <p class="download-privacy-note">
                        <?php _e('We respect your privacy. Your email will only be used to send you the download link and occasional business tips. No spam, ever.', 'chatgabi'); ?>
                    </p>
                </form>

                <div class="download-success-message" id="download-success-message" style="display: none;">
                    <div class="success-icon">✅</div>
                    <h4><?php _e('Download Link Sent!', 'chatgabi'); ?></h4>
                    <p><?php _e('Check your email for the download link. It should arrive within a few minutes.', 'chatgabi'); ?></p>
                    <button class="success-close-btn" id="success-close-btn"><?php _e('Close', 'chatgabi'); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Lead Magnets Section Styles */
.lead-magnets-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.lead-magnets-header {
    text-align: center;
    margin-bottom: 3rem;
}

.lead-magnets-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.lead-magnets-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
}

/* Lead Magnets Grid */
.lead-magnets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.lead-magnet-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.lead-magnet-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

/* Magnet Preview */
.magnet-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.magnet-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.lead-magnet-card:hover .magnet-preview-image {
    transform: scale(1.05);
}

.magnet-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lead-magnet-card:hover .magnet-overlay {
    opacity: 1;
}

.magnet-stats {
    display: flex;
    gap: 1rem;
    align-self: flex-end;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.stat-icon {
    font-size: 1rem;
}

.preview-btn {
    align-self: center;
    background: rgba(255,255,255,0.9);
    color: var(--color-african-sky);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-btn:hover {
    background: white;
    transform: scale(1.05);
}

/* Magnet Content */
.magnet-content {
    padding: 2rem;
}

.magnet-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.magnet-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Magnet Features */
.magnet-features {
    margin-bottom: 1.5rem;
}

.magnet-features h4 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 0.75rem;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    font-size: 0.9rem;
    color: #2c3e50;
}

.feature-icon {
    font-size: 1rem;
    flex-shrink: 0;
}

.features-more {
    color: var(--color-african-gold);
    font-weight: 600;
}

/* Magnet Meta */
.magnet-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.meta-icon {
    font-size: 1rem;
}

/* Download Button */
.download-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    color: #2c3e50;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 1rem;
}

.btn-subtext {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* View All Resources */
.view-all-resources {
    text-align: center;
    margin-bottom: 3rem;
}

.view-all-btn {
    background: var(--color-african-sky);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.view-all-btn:hover {
    background: #2c3e50;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(61, 78, 129, 0.3);
}

.btn-arrow {
    transition: transform 0.3s ease;
}

.view-all-btn:hover .btn-arrow {
    transform: translateX(5px);
}

/* Social Proof Section */
.lead-magnets-social-proof {
    text-align: center;
    background: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.lead-magnets-social-proof h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 2rem;
}

.social-proof-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.proof-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.proof-number {
    font-size: 2rem;
    font-weight: 900;
    color: var(--color-african-gold);
    margin-bottom: 0.5rem;
}

.proof-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
}

/* Recent Downloads */
.recent-downloads {
    margin-top: 2rem;
}

.recent-downloads-title {
    font-weight: 600;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.recent-downloads-list {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
}

.recent-download-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
    animation: slideInUp 0.5s ease;
}

.recent-download-item:last-child {
    border-bottom: none;
}

.download-text {
    font-weight: 600;
    color: #2c3e50;
}

.download-time {
    font-size: 0.85rem;
    color: #6c757d;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .lead-magnets-grid {
        grid-template-columns: 1fr;
    }

    .magnet-content {
        padding: 1.5rem;
    }

    .magnet-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .social-proof-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .recent-download-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Download Modal Styles */
.download-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.download-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.download-modal {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.download-modal-overlay.show .download-modal {
    transform: scale(1);
}

.download-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #dee2e6;
}

.download-modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin: 0;
}

.download-modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.download-modal-close:hover {
    color: var(--color-african-sky);
}

.download-modal-content {
    padding: 2rem;
}

.download-modal-preview {
    position: relative;
    margin-bottom: 2rem;
}

.download-modal-preview img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
}

.download-modal-stats {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 1rem;
}

.modal-stat {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.download-modal-description {
    font-size: 1.1rem;
    color: #2c3e50;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Download Form */
.download-form {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--color-african-sky);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--color-african-sky);
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--color-african-sky);
    border-color: var(--color-african-sky);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.download-submit-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    color: #2c3e50;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.download-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.download-submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.download-privacy-note {
    font-size: 0.85rem;
    color: #6c757d;
    text-align: center;
    line-height: 1.4;
    margin: 0;
}

/* Success Message */
.download-success-message {
    text-align: center;
    padding: 2rem;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.download-success-message h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-nature);
    margin-bottom: 1rem;
}

.download-success-message p {
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.success-close-btn {
    background: var(--color-african-nature);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.success-close-btn:hover {
    background: #219a52;
    transform: translateY(-2px);
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
    .download-modal-overlay {
        padding: 1rem;
    }

    .download-modal {
        max-height: 95vh;
    }

    .download-modal-header,
    .download-modal-content {
        padding: 1.5rem;
    }

    .download-modal-stats {
        position: static;
        justify-content: center;
        margin-top: 1rem;
    }

    .checkbox-group {
        align-items: flex-start;
    }
}
</style>