<?php
/**
 * FAQ Section Template Part
 * 
 * Comprehensive FAQ section addressing common objections
 * and questions for conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user country for localized FAQs
$user_country = chatgabi_get_user_country();
$user_currency = chatgabi_get_user_currency();
?>

<section class="faq-section">
    <div class="container">
        
        <!-- Section Header -->
        <div class="faq-header">
            <h2><?php _e('Frequently Asked Questions', 'chatgabi'); ?></h2>
            <p class="faq-subtitle">
                <?php _e('Get answers to common questions about ChatGABI and how it can transform your business.', 'chatgabi'); ?>
            </p>
        </div>
        
        <!-- FAQ Categories -->
        <div class="faq-categories">
            <button class="faq-category-btn active" data-category="all">
                <?php _e('All Questions', 'chatgabi'); ?>
            </button>
            <button class="faq-category-btn" data-category="getting-started">
                <?php _e('Getting Started', 'chatgabi'); ?>
            </button>
            <button class="faq-category-btn" data-category="pricing">
                <?php _e('Pricing & Credits', 'chatgabi'); ?>
            </button>
            <button class="faq-category-btn" data-category="features">
                <?php _e('Features', 'chatgabi'); ?>
            </button>
            <button class="faq-category-btn" data-category="african-market">
                <?php _e('African Markets', 'chatgabi'); ?>
            </button>
            <button class="faq-category-btn" data-category="technical">
                <?php _e('Technical', 'chatgabi'); ?>
            </button>
        </div>
        
        <!-- FAQ Items -->
        <div class="faq-container">
            
            <!-- Getting Started FAQs -->
            <div class="faq-item" data-category="getting-started">
                <div class="faq-question">
                    <h3><?php _e('How do I get started with ChatGABI?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Getting started is simple! Sign up for a free account and receive 50 credits immediately. No credit card required. You can start creating business plans, marketing strategies, and financial forecasts right away.', 'chatgabi'); ?></p>
                    <div class="faq-cta">
                        <a href="<?php echo wp_registration_url(); ?>" class="faq-cta-btn">
                            <?php _e('Start Free Now', 'chatgabi'); ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" data-category="getting-started">
                <div class="faq-question">
                    <h3><?php _e('Do I need any technical knowledge to use ChatGABI?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Not at all! ChatGABI is designed for entrepreneurs, not tech experts. Simply type your business questions in plain language (English, Twi, Swahili, Yoruba, or Zulu) and get professional-quality responses instantly.', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <div class="faq-item" data-category="getting-started">
                <div class="faq-question">
                    <h3><?php _e('How long does it take to create a business plan?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Most business plans are completed in 5-10 minutes. ChatGABI asks you targeted questions about your business and generates a comprehensive 15-20 page plan with market analysis, financial projections, and growth strategies.', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <!-- Pricing FAQs -->
            <div class="faq-item" data-category="pricing">
                <div class="faq-question">
                    <h3><?php _e('How does the credit system work?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Credits are used for AI-powered responses. Simple questions use 1-2 credits, while comprehensive business plans use 10-15 credits. You get 50 free credits to start, and can purchase more as needed.', 'chatgabi'); ?></p>
                    <div class="credit-examples">
                        <div class="credit-example">
                            <span class="example-task"><?php _e('Quick business advice', 'chatgabi'); ?></span>
                            <span class="example-cost">1-2 <?php _e('credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="credit-example">
                            <span class="example-task"><?php _e('Marketing strategy', 'chatgabi'); ?></span>
                            <span class="example-cost">5-8 <?php _e('credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="credit-example">
                            <span class="example-task"><?php _e('Complete business plan', 'chatgabi'); ?></span>
                            <span class="example-cost">10-15 <?php _e('credits', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" data-category="pricing">
                <div class="faq-question">
                    <h3><?php printf(__('What payment methods do you accept in %s?', 'chatgabi'), chatgabi_get_country_name($user_country)); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('We accept all major payment methods including:', 'chatgabi'); ?></p>
                    <ul class="payment-methods-list">
                        <li>💳 <?php _e('Credit/Debit Cards (Visa, Mastercard)', 'chatgabi'); ?></li>
                        <li>📱 <?php _e('Mobile Money (MTN, Vodafone, Airtel)', 'chatgabi'); ?></li>
                        <li>🏦 <?php _e('Bank Transfers', 'chatgabi'); ?></li>
                        <li>💰 <?php _e('Paystack Secure Payments', 'chatgabi'); ?></li>
                    </ul>
                    <p><?php printf(__('All payments are processed securely in %s.', 'chatgabi'), $user_currency['code']); ?></p>
                </div>
            </div>
            
            <div class="faq-item" data-category="pricing">
                <div class="faq-question">
                    <h3><?php _e('Is there a money-back guarantee?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Yes! We offer a 30-day money-back guarantee. If you\'re not satisfied with ChatGABI, contact us within 30 days for a full refund. We\'re confident you\'ll love the results.', 'chatgabi'); ?></p>
                    <div class="guarantee-badge">
                        <span class="guarantee-icon">🛡️</span>
                        <span class="guarantee-text"><?php _e('30-Day Money-Back Guarantee', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Features FAQs -->
            <div class="faq-item" data-category="features">
                <div class="faq-question">
                    <h3><?php _e('What languages does ChatGABI support?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('ChatGABI supports 5 African languages:', 'chatgabi'); ?></p>
                    <div class="languages-grid">
                        <div class="language-item">
                            <span class="language-flag">🇬🇧</span>
                            <span class="language-name"><?php _e('English', 'chatgabi'); ?></span>
                        </div>
                        <div class="language-item">
                            <span class="language-flag">🇬🇭</span>
                            <span class="language-name"><?php _e('Twi (Ghana)', 'chatgabi'); ?></span>
                        </div>
                        <div class="language-item">
                            <span class="language-flag">🇰🇪</span>
                            <span class="language-name"><?php _e('Swahili (Kenya)', 'chatgabi'); ?></span>
                        </div>
                        <div class="language-item">
                            <span class="language-flag">🇳🇬</span>
                            <span class="language-name"><?php _e('Yoruba (Nigeria)', 'chatgabi'); ?></span>
                        </div>
                        <div class="language-item">
                            <span class="language-flag">🇿🇦</span>
                            <span class="language-name"><?php _e('Zulu (South Africa)', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" data-category="features">
                <div class="faq-question">
                    <h3><?php _e('Can I export my business plans and documents?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Absolutely! You can export all your documents in multiple formats:', 'chatgabi'); ?></p>
                    <ul class="export-formats">
                        <li>📄 <?php _e('PDF (for presentations)', 'chatgabi'); ?></li>
                        <li>📝 <?php _e('Word Document (for editing)', 'chatgabi'); ?></li>
                        <li>📊 <?php _e('Excel (for financial data)', 'chatgabi'); ?></li>
                        <li>📧 <?php _e('Email delivery', 'chatgabi'); ?></li>
                    </ul>
                </div>
            </div>
            
            <!-- African Market FAQs -->
            <div class="faq-item" data-category="african-market">
                <div class="faq-question">
                    <h3><?php _e('Does ChatGABI understand African business contexts?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Yes! ChatGABI is specifically trained on African market data, business practices, and economic conditions. It understands local challenges, opportunities, and cultural nuances across Ghana, Kenya, Nigeria, and South Africa.', 'chatgabi'); ?></p>
                    <div class="african-features">
                        <div class="feature-item">
                            <span class="feature-icon">🌍</span>
                            <span class="feature-text"><?php _e('Local market intelligence', 'chatgabi'); ?></span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💰</span>
                            <span class="feature-text"><?php _e('Currency-specific financial models', 'chatgabi'); ?></span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🏛️</span>
                            <span class="feature-text"><?php _e('Regulatory compliance guidance', 'chatgabi'); ?></span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🤝</span>
                            <span class="feature-text"><?php _e('Cultural business practices', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" data-category="african-market">
                <div class="faq-question">
                    <h3><?php _e('Can ChatGABI help with government requirements and regulations?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Yes! ChatGABI includes guidance on business registration, tax requirements, licensing, and compliance for each African country. It helps you understand what\'s needed to operate legally and successfully.', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <!-- Technical FAQs -->
            <div class="faq-item" data-category="technical">
                <div class="faq-question">
                    <h3><?php _e('Is my business data secure and private?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('Absolutely! We use bank-level encryption and security measures. Your business data is never shared, sold, or used for any purpose other than generating your requested content. All data is stored securely and you maintain full ownership.', 'chatgabi'); ?></p>
                    <div class="security-features">
                        <div class="security-item">
                            <span class="security-icon">🔒</span>
                            <span class="security-text"><?php _e('256-bit SSL encryption', 'chatgabi'); ?></span>
                        </div>
                        <div class="security-item">
                            <span class="security-icon">🛡️</span>
                            <span class="security-text"><?php _e('GDPR compliant', 'chatgabi'); ?></span>
                        </div>
                        <div class="security-item">
                            <span class="security-icon">🏦</span>
                            <span class="security-text"><?php _e('Bank-level security', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" data-category="technical">
                <div class="faq-question">
                    <h3><?php _e('What if I have poor internet connection?', 'chatgabi'); ?></h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p><?php _e('ChatGABI is optimized for African internet conditions. It works well on 3G connections and automatically saves your progress. If you lose connection, your work is preserved and you can continue where you left off.', 'chatgabi'); ?></p>
                </div>
            </div>
            
        </div>
        
        <!-- Still Have Questions Section -->
        <div class="faq-contact-section">
            <div class="faq-contact-content">
                <h3><?php _e('Still have questions?', 'chatgabi'); ?></h3>
                <p><?php _e('Our African business experts are here to help you succeed.', 'chatgabi'); ?></p>
                <div class="faq-contact-options">
                    <a href="mailto:<EMAIL>" class="contact-option">
                        <span class="contact-icon">📧</span>
                        <span class="contact-text"><?php _e('Email Support', 'chatgabi'); ?></span>
                    </a>
                    <a href="#" class="contact-option" id="live-chat-btn">
                        <span class="contact-icon">💬</span>
                        <span class="contact-text"><?php _e('Live Chat', 'chatgabi'); ?></span>
                    </a>
                    <a href="tel:+233123456789" class="contact-option">
                        <span class="contact-icon">📞</span>
                        <span class="contact-text"><?php _e('Phone Support', 'chatgabi'); ?></span>
                    </a>
                </div>
            </div>
        </div>
        
    </div>
</section>

<style>
/* FAQ Section Styles */
.faq-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.faq-header {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.faq-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* FAQ Categories */
.faq-categories {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.faq-category-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.faq-category-btn:hover,
.faq-category-btn.active {
    background: var(--color-african-sky);
    color: white;
    border-color: var(--color-african-sky);
}

/* FAQ Container */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin: 0;
    flex: 1;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--color-african-gold);
    transition: transform 0.3s ease;
    margin-left: 1rem;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.5rem 1.5rem 1.5rem;
    display: none;
    color: #2c3e50;
    line-height: 1.6;
}

.faq-item.active .faq-answer {
    display: block;
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* FAQ Content Elements */
.faq-cta {
    margin-top: 1rem;
}

.faq-cta-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--color-african-gold);
    color: #2c3e50;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.faq-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    color: #2c3e50;
}

.credit-examples {
    margin-top: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.credit-example {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.credit-example:last-child {
    border-bottom: none;
}

.example-task {
    font-weight: 600;
}

.example-cost {
    color: var(--color-african-nature);
    font-weight: 700;
}

.payment-methods-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.payment-methods-list li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.guarantee-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--color-african-nature);
    color: white;
    border-radius: 8px;
    margin-top: 1rem;
    font-weight: 600;
}

.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.language-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: 600;
}

.export-formats {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.export-formats li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.african-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-text {
    font-weight: 600;
    color: var(--color-african-sky);
}

.security-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.security-icon {
    font-size: 1.5rem;
}

.security-text {
    font-weight: 600;
    color: var(--color-african-nature);
}

/* Contact Section */
.faq-contact-section {
    margin-top: 4rem;
    padding: 3rem;
    background: white;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.faq-contact-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.faq-contact-content p {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.faq-contact-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.contact-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    text-decoration: none;
    color: var(--color-african-sky);
    transition: all 0.3s ease;
    min-width: 120px;
}

.contact-option:hover {
    background: var(--color-african-sky);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.contact-icon {
    font-size: 2rem;
}

.contact-text {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .faq-categories {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .faq-question {
        padding: 1rem;
    }
    
    .faq-answer {
        padding: 0 1rem 1rem 1rem;
    }
    
    .languages-grid,
    .african-features,
    .security-features {
        grid-template-columns: 1fr;
    }
    
    .faq-contact-options {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-option {
        width: 100%;
        max-width: 200px;
    }
}
</style>

<script>
// FAQ Section JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // FAQ item toggle functionality
    document.querySelectorAll('.faq-question').forEach(question => {
        question.addEventListener('click', function() {
            const faqItem = this.parentElement;
            const isActive = faqItem.classList.contains('active');
            
            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Toggle current item
            if (!isActive) {
                faqItem.classList.add('active');
            }
            
            // Track FAQ interaction
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                const questionText = this.querySelector('h3').textContent;
                window.ChatGABI.ConversionTracker.trackConversion('faq_question_clicked', {
                    question: questionText,
                    section: 'faq'
                });
            }
        });
    });
    
    // Category filtering
    document.querySelectorAll('.faq-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            document.querySelectorAll('.faq-category-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Track category filter usage
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('faq_category_filtered', {
                    category: category,
                    section: 'faq'
                });
            }
        });
    });
    
    // Live chat button
    document.getElementById('live-chat-btn')?.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Track live chat click
        if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
            window.ChatGABI.ConversionTracker.trackConversion('live_chat_clicked', {
                source: 'faq_section'
            });
        }
        
        // Implement live chat integration here
        alert('Live chat would be integrated here with a service like Intercom, Zendesk, or Tawk.to');
    });
});
</script>

<?php
/**
 * Helper function to get country name
 */
function chatgabi_get_country_name($country_code) {
    $countries = array(
        'GH' => __('Ghana', 'chatgabi'),
        'KE' => __('Kenya', 'chatgabi'),
        'NG' => __('Nigeria', 'chatgabi'),
        'ZA' => __('South Africa', 'chatgabi')
    );
    
    return $countries[$country_code] ?? __('your country', 'chatgabi');
}
?>
