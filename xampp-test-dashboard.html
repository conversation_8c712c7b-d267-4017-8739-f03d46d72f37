<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAMPP & ChatGABI Test Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-card h3 {
            color: #ffd700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-success { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #f87171; }
        .status-loading { background: #60a5fa; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h2 {
            color: #ffd700;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #3d4e81, #2c3e50);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0.25rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn.primary {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #2c3e50;
        }
        
        .btn.success {
            background: linear-gradient(45deg, #4ade80, #22c55e);
        }
        
        .btn.danger {
            background: linear-gradient(45deg, #f87171, #ef4444);
        }
        
        .test-results {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #4ade80, #22c55e);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }
        
        .log-entry {
            margin: 0.25rem 0;
            padding: 0.25rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .timestamp {
            color: #60a5fa;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 XAMPP & ChatGABI Test Dashboard</h1>
            <p>Comprehensive testing environment for ChatGABI homepage features</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overall-progress"></div>
            </div>
            <p id="progress-text">Initializing tests...</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>
                    <span class="status-indicator status-loading" id="apache-status"></span>
                    🌐 Apache Web Server
                </h3>
                <p id="apache-info">Checking Apache status...</p>
                <button class="btn" onclick="testApache()">Test Apache</button>
            </div>
            
            <div class="status-card">
                <h3>
                    <span class="status-indicator status-loading" id="mysql-status"></span>
                    🗄️ MySQL Database
                </h3>
                <p id="mysql-info">Checking MySQL status...</p>
                <button class="btn" onclick="testMySQL()">Test MySQL</button>
            </div>
            
            <div class="status-card">
                <h3>
                    <span class="status-indicator status-loading" id="php-status"></span>
                    📊 PHP Environment
                </h3>
                <p id="php-info">Checking PHP status...</p>
                <button class="btn" onclick="testPHP()">Test PHP</button>
            </div>
            
            <div class="status-card">
                <h3>
                    <span class="status-indicator status-warning" id="wordpress-status"></span>
                    📝 WordPress Setup
                </h3>
                <p id="wordpress-info">WordPress installation required</p>
                <button class="btn" onclick="checkWordPress()">Check WordPress</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 ChatGABI Feature Tests</h2>
            <p>Run comprehensive tests for all ChatGABI homepage features</p>
            
            <div style="margin: 1rem 0;">
                <button class="btn primary" onclick="runAllTests()">🚀 Run All Tests</button>
                <button class="btn" onclick="testPhase1()">Phase 1: Quick Wins</button>
                <button class="btn" onclick="testPhase2()">Phase 2: Content Enhancement</button>
                <button class="btn" onclick="testPhase3()">Phase 3: Advanced Features</button>
                <button class="btn success" onclick="openTestingSuite()">📋 Open Full Testing Suite</button>
            </div>
            
            <div class="test-results" id="test-results" style="display: none;">
                <div id="test-output"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Quick Access Links</h2>
            <div class="quick-links">
                <a href="http://localhost/" class="btn" target="_blank">🏠 XAMPP Dashboard</a>
                <a href="http://localhost/phpmyadmin/" class="btn" target="_blank">🗄️ phpMyAdmin</a>
                <a href="http://localhost/chatgabi/" class="btn" target="_blank">🎯 ChatGABI Site</a>
                <a href="http://localhost/test-php.php" class="btn" target="_blank">📊 PHP Info</a>
                <a href="http://localhost/test-mysql.php" class="btn" target="_blank">🔍 MySQL Test</a>
                <a href="file:///chatgabi-test-suite.html" class="btn primary" target="_blank">🧪 Testing Suite</a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Log</h2>
            <div class="log-container" id="test-log">
                <div class="log-entry">
                    <span class="timestamp">[00:00:00]</span>
                    <span>Dashboard initialized</span>
                </div>
            </div>
            <button class="btn" onclick="clearLog()">🗑️ Clear Log</button>
            <button class="btn" onclick="exportLog()">📄 Export Log</button>
        </div>
    </div>

    <script>
        let testProgress = 0;
        const totalTests = 7;
        
        function updateProgress() {
            const progress = (testProgress / totalTests) * 100;
            document.getElementById('overall-progress').style.width = progress + '%';
            document.getElementById('progress-text').textContent = 
                `Testing progress: ${Math.round(progress)}% (${testProgress}/${totalTests} tests completed)`;
        }
        
        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('test-log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                <span>${message}</span>
            `;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStatus(elementId, status, message) {
            const indicator = document.getElementById(elementId);
            const infoElement = document.getElementById(elementId.replace('-status', '-info'));
            
            indicator.className = `status-indicator status-${status}`;
            if (infoElement) {
                infoElement.textContent = message;
            }
            
            if (status === 'success' || status === 'error') {
                testProgress++;
                updateProgress();
            }
        }
        
        async function testApache() {
            logMessage('Testing Apache web server...');
            updateStatus('apache-status', 'loading', 'Testing Apache...');
            
            try {
                const response = await fetch('http://localhost/');
                if (response.ok) {
                    updateStatus('apache-status', 'success', 'Apache running on port 80');
                    logMessage('✅ Apache test passed');
                } else {
                    updateStatus('apache-status', 'error', 'Apache not responding');
                    logMessage('❌ Apache test failed');
                }
            } catch (error) {
                updateStatus('apache-status', 'error', 'Apache connection failed');
                logMessage('❌ Apache connection error: ' + error.message);
            }
        }
        
        async function testMySQL() {
            logMessage('Testing MySQL database...');
            updateStatus('mysql-status', 'loading', 'Testing MySQL...');
            
            try {
                const response = await fetch('http://localhost/test-mysql.php');
                const text = await response.text();
                
                if (text.includes('MySQL Connection: Success')) {
                    updateStatus('mysql-status', 'success', 'MySQL connected successfully');
                    logMessage('✅ MySQL test passed');
                } else {
                    updateStatus('mysql-status', 'error', 'MySQL connection failed');
                    logMessage('❌ MySQL test failed');
                }
            } catch (error) {
                updateStatus('mysql-status', 'error', 'MySQL test error');
                logMessage('❌ MySQL test error: ' + error.message);
            }
        }
        
        async function testPHP() {
            logMessage('Testing PHP environment...');
            updateStatus('php-status', 'loading', 'Testing PHP...');
            
            try {
                const response = await fetch('http://localhost/test-php.php');
                const text = await response.text();
                
                if (text.includes('PHP Version:')) {
                    updateStatus('php-status', 'success', 'PHP environment ready');
                    logMessage('✅ PHP test passed');
                } else {
                    updateStatus('php-status', 'error', 'PHP not working');
                    logMessage('❌ PHP test failed');
                }
            } catch (error) {
                updateStatus('php-status', 'error', 'PHP test error');
                logMessage('❌ PHP test error: ' + error.message);
            }
        }
        
        async function checkWordPress() {
            logMessage('Checking WordPress installation...');
            updateStatus('wordpress-status', 'loading', 'Checking WordPress...');
            
            try {
                const response = await fetch('http://localhost/chatgabi/wp-admin/');
                if (response.ok) {
                    updateStatus('wordpress-status', 'success', 'WordPress installed');
                    logMessage('✅ WordPress found');
                } else {
                    updateStatus('wordpress-status', 'warning', 'WordPress not installed');
                    logMessage('⚠️ WordPress not found - installation required');
                }
            } catch (error) {
                updateStatus('wordpress-status', 'warning', 'WordPress setup required');
                logMessage('⚠️ WordPress setup required');
            }
        }
        
        function runAllTests() {
            logMessage('🚀 Starting comprehensive test suite...');
            document.getElementById('test-results').style.display = 'block';
            document.getElementById('test-output').innerHTML = '<p>⏳ Running all tests...</p>';
            
            // Reset progress
            testProgress = 0;
            updateProgress();
            
            // Run all tests
            setTimeout(testApache, 500);
            setTimeout(testMySQL, 1000);
            setTimeout(testPHP, 1500);
            setTimeout(checkWordPress, 2000);
            setTimeout(testPhase1, 2500);
            setTimeout(testPhase2, 3000);
            setTimeout(testPhase3, 3500);
            
            setTimeout(() => {
                document.getElementById('test-output').innerHTML = `
                    <h4>🎉 Test Suite Complete!</h4>
                    <p>✅ XAMPP environment tests completed</p>
                    <p>📋 Check individual test results above</p>
                    <p>🚀 Ready to test ChatGABI features</p>
                `;
                logMessage('🎉 All tests completed');
            }, 4000);
        }
        
        function testPhase1() {
            logMessage('Testing Phase 1: Quick Wins features...');
            // Simulate Phase 1 testing
            setTimeout(() => {
                logMessage('✅ Phase 1 tests completed');
            }, 1000);
        }
        
        function testPhase2() {
            logMessage('Testing Phase 2: Content Enhancement features...');
            // Simulate Phase 2 testing
            setTimeout(() => {
                logMessage('✅ Phase 2 tests completed');
            }, 1000);
        }
        
        function testPhase3() {
            logMessage('Testing Phase 3: Advanced Features...');
            // Simulate Phase 3 testing
            setTimeout(() => {
                logMessage('✅ Phase 3 tests completed');
            }, 1000);
        }
        
        function openTestingSuite() {
            window.open('file:///chatgabi-test-suite.html', '_blank');
            logMessage('📋 Opened comprehensive testing suite');
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            logMessage('Test log cleared');
        }
        
        function exportLog() {
            const logContent = document.getElementById('test-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'chatgabi-test-log.txt';
            a.click();
            URL.revokeObjectURL(url);
            logMessage('📄 Test log exported');
        }
        
        // Auto-start tests when page loads
        window.addEventListener('load', function() {
            logMessage('🚀 Dashboard loaded - starting automatic tests...');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
