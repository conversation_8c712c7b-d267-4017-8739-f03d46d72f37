/**
 * A/B Testing JavaScript
 * 
 * Handles A/B test variant assignment, tracking,
 * and conversion optimization for ChatGABI.
 */

(function($) {
    'use strict';

    const ABTesting = {
        
        // Configuration
        config: {
            cookieExpiry: 30, // days
            trackingDelay: 1000 // ms
        },
        
        // Active tests
        activeTests: {
            'hero_cta_test': {
                variants: {
                    'control': 'Start Free Trial',
                    'variant_a': 'Get Started Free',
                    'variant_b': 'Try ChatGABI Free'
                },
                element: '.hero-cta-primary'
            },
            'pricing_display_test': {
                variants: {
                    'control': 'monthly',
                    'variant_a': 'annual',
                    'variant_b': 'credits'
                },
                element: '.pricing-toggle'
            },
            'testimonial_layout_test': {
                variants: {
                    'control': 'grid',
                    'variant_a': 'carousel',
                    'variant_b': 'featured'
                },
                element: '.testimonials-section'
            },
            'roi_calculator_position_test': {
                variants: {
                    'control': 'after_hero',
                    'variant_a': 'before_pricing',
                    'variant_b': 'after_testimonials'
                },
                element: '.interactive-roi-calculator-section'
            }
        },
        
        // User assignments
        userAssignments: {},
        
        // Initialize A/B testing
        init: function() {
            this.loadUserAssignments();
            this.runActiveTests();
            this.bindConversionTracking();
            this.scheduleTracking();
        },
        
        // Load user assignments from cookies
        loadUserAssignments: function() {
            const assignments = this.getCookie('chatgabi_ab_assignments');
            if (assignments) {
                try {
                    this.userAssignments = JSON.parse(assignments);
                } catch (e) {
                    console.warn('Failed to parse A/B test assignments:', e);
                    this.userAssignments = {};
                }
            }
        },
        
        // Save user assignments to cookies
        saveUserAssignments: function() {
            this.setCookie('chatgabi_ab_assignments', JSON.stringify(this.userAssignments), this.config.cookieExpiry);
        },
        
        // Run all active tests
        runActiveTests: function() {
            Object.keys(this.activeTests).forEach(testName => {
                this.runTest(testName);
            });
        },
        
        // Run individual test
        runTest: function(testName) {
            const test = this.activeTests[testName];
            if (!test) return;
            
            // Get or assign variant
            let variant = this.userAssignments[testName];
            if (!variant) {
                variant = this.assignVariant(testName, test.variants);
                this.userAssignments[testName] = variant;
                this.saveUserAssignments();
                
                // Track exposure
                this.trackExposure(testName, variant);
            }
            
            // Apply variant
            this.applyVariant(testName, variant, test);
        },
        
        // Assign variant to user
        assignVariant: function(testName, variants) {
            const variantKeys = Object.keys(variants);
            const userId = this.getUserId();
            const hash = this.hashCode(userId + testName);
            const index = Math.abs(hash) % variantKeys.length;
            return variantKeys[index];
        },
        
        // Apply variant to page
        applyVariant: function(testName, variant, test) {
            const $element = $(test.element);
            if (!$element.length) return;
            
            // Add variant class
            $element.addClass(`ab-test-${testName}-${variant}`);
            
            // Apply specific variant changes
            switch (testName) {
                case 'hero_cta_test':
                    this.applyHeroCTAVariant(variant, test.variants[variant]);
                    break;
                case 'pricing_display_test':
                    this.applyPricingVariant(variant);
                    break;
                case 'testimonial_layout_test':
                    this.applyTestimonialVariant(variant);
                    break;
                case 'roi_calculator_position_test':
                    this.applyROICalculatorVariant(variant);
                    break;
            }
        },
        
        // Apply hero CTA variant
        applyHeroCTAVariant: function(variant, text) {
            $('.hero-cta-primary .cta-text').text(text);
            
            // Track which variant is shown
            console.log(`Hero CTA Test: Showing variant ${variant} with text "${text}"`);
        },
        
        // Apply pricing display variant
        applyPricingVariant: function(variant) {
            const $pricingSection = $('.pricing-section');
            
            switch (variant) {
                case 'control':
                    // Default monthly display
                    break;
                case 'variant_a':
                    // Show annual pricing by default
                    $pricingSection.addClass('show-annual');
                    break;
                case 'variant_b':
                    // Show credits pricing
                    $pricingSection.addClass('show-credits');
                    break;
            }
            
            console.log(`Pricing Test: Showing variant ${variant}`);
        },
        
        // Apply testimonial layout variant
        applyTestimonialVariant: function(variant) {
            const $testimonialsSection = $('.testimonials-section');
            
            switch (variant) {
                case 'control':
                    // Default grid layout
                    break;
                case 'variant_a':
                    // Carousel layout
                    $testimonialsSection.addClass('layout-carousel');
                    break;
                case 'variant_b':
                    // Featured testimonial layout
                    $testimonialsSection.addClass('layout-featured');
                    break;
            }
            
            console.log(`Testimonials Test: Showing variant ${variant}`);
        },
        
        // Apply ROI calculator position variant
        applyROICalculatorVariant: function(variant) {
            const $calculator = $('.interactive-roi-calculator-section');
            if (!$calculator.length) return;
            
            switch (variant) {
                case 'control':
                    // Default position (after hero)
                    break;
                case 'variant_a':
                    // Move before pricing
                    $calculator.insertBefore('.pricing-section');
                    break;
                case 'variant_b':
                    // Move after testimonials
                    $calculator.insertAfter('.enhanced-testimonials-section');
                    break;
            }
            
            console.log(`ROI Calculator Test: Showing variant ${variant}`);
        },
        
        // Bind conversion tracking
        bindConversionTracking: function() {
            // Track signup conversions
            $(document).on('click', '.cta-primary, .signup-btn, .hero-cta-primary', () => {
                this.trackConversion('signup_clicked');
            });
            
            // Track pricing page visits
            $(document).on('click', '.pricing-cta, .view-pricing-btn', () => {
                this.trackConversion('pricing_viewed');
            });
            
            // Track calculator interactions
            $(document).on('click', '#roi-calculator-signup', () => {
                this.trackConversion('calculator_signup');
            });
            
            // Track lead magnet downloads
            $(document).on('click', '.download-btn', () => {
                this.trackConversion('lead_magnet_download');
            });
            
            // Track testimonial interactions
            $(document).on('click', '.case-study-btn', () => {
                this.trackConversion('case_study_viewed');
            });
        },
        
        // Track exposure
        trackExposure: function(testName, variant) {
            // Delay tracking to ensure page is loaded
            setTimeout(() => {
                if (window.chatgabiAB) {
                    $.ajax({
                        url: window.chatgabiAB.ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'chatgabi_track_ab_exposure',
                            nonce: window.chatgabiAB.nonce,
                            test_name: testName,
                            variant: variant
                        },
                        success: (response) => {
                            console.log(`A/B Test Exposure tracked: ${testName} - ${variant}`);
                        },
                        error: (xhr, status, error) => {
                            console.warn('Failed to track A/B test exposure:', error);
                        }
                    });
                }
            }, this.config.trackingDelay);
        },
        
        // Track conversion
        trackConversion: function(conversionType) {
            Object.keys(this.userAssignments).forEach(testName => {
                const variant = this.userAssignments[testName];
                
                if (window.chatgabiAB) {
                    $.ajax({
                        url: window.chatgabiAB.ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'chatgabi_track_ab_conversion',
                            nonce: window.chatgabiAB.nonce,
                            test_name: testName,
                            variant: variant,
                            conversion_type: conversionType
                        },
                        success: (response) => {
                            console.log(`A/B Test Conversion tracked: ${testName} - ${variant} - ${conversionType}`);
                        },
                        error: (xhr, status, error) => {
                            console.warn('Failed to track A/B test conversion:', error);
                        }
                    });
                }
            });
        },
        
        // Schedule periodic tracking
        scheduleTracking: function() {
            // Track page engagement after 30 seconds
            setTimeout(() => {
                this.trackConversion('page_engagement');
            }, 30000);
            
            // Track scroll depth
            let scrollTracked = false;
            $(window).on('scroll', () => {
                if (!scrollTracked && $(window).scrollTop() > $(document).height() * 0.5) {
                    scrollTracked = true;
                    this.trackConversion('scroll_50_percent');
                }
            });
        },
        
        // Utility functions
        getUserId: function() {
            // Try to get user ID from various sources
            if (window.chatgabiAjax && window.chatgabiAjax.userId) {
                return 'user_' + window.chatgabiAjax.userId;
            }
            
            // Use fingerprint for anonymous users
            let fingerprint = this.getCookie('chatgabi_fingerprint');
            if (!fingerprint) {
                fingerprint = this.generateFingerprint();
                this.setCookie('chatgabi_fingerprint', fingerprint, 365);
            }
            
            return 'anon_' + fingerprint;
        },
        
        generateFingerprint: function() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('ChatGABI fingerprint', 2, 2);
            
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                canvas.toDataURL()
            ].join('|');
            
            return this.hashCode(fingerprint).toString();
        },
        
        hashCode: function(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return hash;
        },
        
        setCookie: function(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        },
        
        getCookie: function(name) {
            const nameEQ = name + '=';
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Only run A/B tests on homepage
        if ($('body').hasClass('home') || $('body').hasClass('front-page')) {
            ABTesting.init();
        }
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.ABTesting = ABTesting;

})(jQuery);
