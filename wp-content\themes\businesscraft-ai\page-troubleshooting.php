<?php
/**
 * Template Name: Troubleshooting
 * 
 * Troubleshooting Guide for ChatGABI
 * Common issues, solutions, and technical support
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user data for personalized troubleshooting
$current_user = wp_get_current_user();
$user_credits = 0;
$user_country = 'Ghana';
$last_login = '';

if ($current_user->ID) {
    $user_credits = get_user_meta($current_user->ID, 'businesscraft_credits', true) ?: 0;
    $user_country = get_user_meta($current_user->ID, 'country', true) ?: 'Ghana';
    $last_login = get_user_meta($current_user->ID, 'last_login', true);
}

// Handle diagnostic form submission
$diagnostic_results = array();
$diagnostic_submitted = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_diagnostics'])) {
    $diagnostic_submitted = true;
    
    // Simulate diagnostic checks
    $diagnostic_results = array(
        'browser' => array(
            'status' => 'pass',
            'message' => 'Browser compatibility: Excellent',
            'details' => 'Chrome 120+ detected with full feature support'
        ),
        'connection' => array(
            'status' => 'pass',
            'message' => 'Internet connection: Stable',
            'details' => 'Low latency connection to African servers'
        ),
        'credits' => array(
            'status' => $user_credits > 0 ? 'pass' : 'warning',
            'message' => $user_credits > 0 ? 'Credits available: ' . $user_credits : 'Low credits: ' . $user_credits,
            'details' => $user_credits > 0 ? 'Sufficient credits for API usage' : 'Consider purchasing more credits'
        ),
        'api' => array(
            'status' => 'pass',
            'message' => 'API connectivity: Operational',
            'details' => 'All ChatGABI services responding normally'
        )
    );
}

get_header();
?>

<div class="chatgabi-troubleshooting-page">
    <div class="container">
        <!-- Troubleshooting Hero -->
        <div class="troubleshooting-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🔧</span>
                    <?php _e('ChatGABI Troubleshooting', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Resolve common issues quickly with our comprehensive troubleshooting guide. Get your AI business intelligence back on track in minutes.', 'chatgabi'); ?>
                </p>
                
                <div class="system-status">
                    <div class="status-item">
                        <span class="status-icon operational">🟢</span>
                        <span class="status-text"><?php _e('All Systems Operational', 'chatgabi'); ?></span>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">⚡</span>
                        <span class="status-text"><?php _e('Response Time: 180ms', 'chatgabi'); ?></span>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">🌍</span>
                        <span class="status-text"><?php printf(__('Serving: %s', 'chatgabi'), $user_country); ?></span>
                    </div>
                </div>
                
                <?php if ($current_user->ID): ?>
                    <div class="user-diagnostics">
                        <div class="diagnostic-info">
                            <span class="diagnostic-icon">👤</span>
                            <span><?php printf(__('Account: %s', 'chatgabi'), $current_user->display_name); ?></span>
                        </div>
                        <div class="diagnostic-info">
                            <span class="diagnostic-icon">💳</span>
                            <span><?php printf(__('Credits: %s', 'chatgabi'), number_format($user_credits)); ?></span>
                        </div>
                        <?php if ($last_login): ?>
                            <div class="diagnostic-info">
                                <span class="diagnostic-icon">🕒</span>
                                <span><?php printf(__('Last Login: %s', 'chatgabi'), date('M j, Y', strtotime($last_login))); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Diagnostic Tool -->
        <div class="diagnostic-tool-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Quick Diagnostic Tool', 'chatgabi'); ?></h2>
                <p><?php _e('Run automated checks to identify and resolve common issues', 'chatgabi'); ?></p>
            </div>
            
            <?php if (!$diagnostic_submitted): ?>
                <div class="diagnostic-form">
                    <form method="post" class="diagnostic-form-content">
                        <div class="diagnostic-description">
                            <p><?php _e('This tool will check:', 'chatgabi'); ?></p>
                            <ul class="diagnostic-checks">
                                <li><?php _e('🌐 Browser compatibility and JavaScript support', 'chatgabi'); ?></li>
                                <li><?php _e('🔗 Internet connection and API connectivity', 'chatgabi'); ?></li>
                                <li><?php _e('💳 Account status and credit balance', 'chatgabi'); ?></li>
                                <li><?php _e('⚙️ System performance and response times', 'chatgabi'); ?></li>
                            </ul>
                        </div>
                        
                        <div class="diagnostic-submit">
                            <button type="submit" name="run_diagnostics" class="btn btn-primary btn-large">
                                <span class="btn-icon">🔍</span>
                                <?php _e('Run Diagnostics', 'chatgabi'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            <?php else: ?>
                <div class="diagnostic-results">
                    <h3><?php _e('Diagnostic Results', 'chatgabi'); ?></h3>
                    
                    <div class="results-grid">
                        <?php foreach ($diagnostic_results as $check => $result): ?>
                            <div class="result-item <?php echo esc_attr($result['status']); ?>">
                                <div class="result-header">
                                    <span class="result-icon">
                                        <?php if ($result['status'] === 'pass'): ?>✅<?php elseif ($result['status'] === 'warning'): ?>⚠️<?php else: ?>❌<?php endif; ?>
                                    </span>
                                    <h4><?php echo esc_html($result['message']); ?></h4>
                                </div>
                                <p><?php echo esc_html($result['details']); ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="diagnostic-actions">
                        <form method="post">
                            <button type="submit" name="run_diagnostics" class="btn btn-secondary">
                                <span class="btn-icon">🔄</span>
                                <?php _e('Run Again', 'chatgabi'); ?>
                            </button>
                        </form>
                        <a href="<?php echo esc_url(home_url('/contact')); ?>" class="btn btn-primary">
                            <span class="btn-icon">💬</span>
                            <?php _e('Contact Support', 'chatgabi'); ?>
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Common Issues -->
        <div class="common-issues-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Common Issues & Solutions', 'chatgabi'); ?></h2>
                <p><?php _e('Quick fixes for the most frequently encountered problems', 'chatgabi'); ?></p>
            </div>
            
            <div class="issues-categories">
                <!-- Account & Login Issues -->
                <div class="issue-category">
                    <h3><?php _e('🔐 Account & Login Issues', 'chatgabi'); ?></h3>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('Cannot log in to my account', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Try these steps:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Verify your username/email and password are correct', 'chatgabi'); ?></li>
                                    <li><?php _e('Clear your browser cache and cookies', 'chatgabi'); ?></li>
                                    <li><?php _e('Try logging in from an incognito/private browser window', 'chatgabi'); ?></li>
                                    <li><?php _e('Reset your password using the "Forgot Password" link', 'chatgabi'); ?></li>
                                    <li><?php _e('Check if your account has been temporarily suspended', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Still having issues?', 'chatgabi'); ?></strong>
                                <p><?php _e('Contact our support team with your username and the error message you\'re seeing.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('My free credits are missing', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Check these possibilities:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Credits are awarded only once per account upon registration', 'chatgabi'); ?></li>
                                    <li><?php _e('Check your usage history in the dashboard', 'chatgabi'); ?></li>
                                    <li><?php _e('Verify you completed the email verification process', 'chatgabi'); ?></li>
                                    <li><?php _e('Ensure you didn\'t create multiple accounts', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Need help?', 'chatgabi'); ?></strong>
                                <p><?php _e('Send us your account email and registration date for manual verification.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Chat Issues -->
                <div class="issue-category">
                    <h3><?php _e('🤖 AI Chat Issues', 'chatgabi'); ?></h3>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('AI responses are slow or timing out', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Optimization steps:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Check your internet connection speed', 'chatgabi'); ?></li>
                                    <li><?php _e('Try shorter, more specific questions', 'chatgabi'); ?></li>
                                    <li><?php _e('Avoid asking multiple questions in one message', 'chatgabi'); ?></li>
                                    <li><?php _e('Clear your browser cache and refresh the page', 'chatgabi'); ?></li>
                                    <li><?php _e('Try using a different browser or device', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Performance tip:', 'chatgabi'); ?></strong>
                                <p><?php _e('Our AI performs best with specific, business-focused questions under 200 words.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('AI responses are not relevant to my business', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Improve response quality:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Update your business profile with accurate sector information', 'chatgabi'); ?></li>
                                    <li><?php _e('Specify your country and target market in questions', 'chatgabi'); ?></li>
                                    <li><?php _e('Use industry-specific terminology', 'chatgabi'); ?></li>
                                    <li><?php _e('Provide context about your business stage (startup, growth, etc.)', 'chatgabi'); ?></li>
                                    <li><?php _e('Ask follow-up questions to refine the response', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Example:', 'chatgabi'); ?></strong>
                                <p><?php _e('"What are the regulatory requirements for starting a fintech company in Nigeria?" vs "How do I start a business?"', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment & Credits Issues -->
                <div class="issue-category">
                    <h3><?php _e('💳 Payment & Credits Issues', 'chatgabi'); ?></h3>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('Payment failed or was declined', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Payment troubleshooting:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Verify your card details and billing address', 'chatgabi'); ?></li>
                                    <li><?php _e('Check if your bank allows international transactions', 'chatgabi'); ?></li>
                                    <li><?php _e('Try a different payment method (mobile money, bank transfer)', 'chatgabi'); ?></li>
                                    <li><?php _e('Ensure sufficient funds are available', 'chatgabi'); ?></li>
                                    <li><?php _e('Contact your bank to authorize the transaction', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Alternative payment methods:', 'chatgabi'); ?></strong>
                                <p><?php _e('We support MTN Mobile Money, Vodafone Cash, and direct bank transfers for African customers.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('Credits were deducted but service failed', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Credit recovery process:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Check your usage history for the transaction details', 'chatgabi'); ?></li>
                                    <li><?php _e('Note the exact time and error message received', 'chatgabi'); ?></li>
                                    <li><?php _e('Try the same request again to see if it was temporary', 'chatgabi'); ?></li>
                                    <li><?php _e('Contact support with the transaction ID', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Automatic refund:', 'chatgabi'); ?></strong>
                                <p><?php _e('Failed requests are automatically refunded within 24 hours. Contact us if this doesn\'t happen.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Issues -->
                <div class="issue-category">
                    <h3><?php _e('⚙️ Technical Issues', 'chatgabi'); ?></h3>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('Page won\'t load or keeps refreshing', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Browser troubleshooting:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Hard refresh the page (Ctrl+F5 or Cmd+Shift+R)', 'chatgabi'); ?></li>
                                    <li><?php _e('Clear browser cache and cookies', 'chatgabi'); ?></li>
                                    <li><?php _e('Disable browser extensions temporarily', 'chatgabi'); ?></li>
                                    <li><?php _e('Try a different browser (Chrome, Firefox, Safari)', 'chatgabi'); ?></li>
                                    <li><?php _e('Check if JavaScript is enabled', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Supported browsers:', 'chatgabi'); ?></strong>
                                <p><?php _e('Chrome 90+, Firefox 88+, Safari 14+, Edge 90+. Mobile browsers are fully supported.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-question">
                            <h4><?php _e('Features not working on mobile device', 'chatgabi'); ?></h4>
                            <span class="issue-toggle">+</span>
                        </div>
                        <div class="issue-solution">
                            <div class="solution-steps">
                                <h5><?php _e('Mobile optimization:', 'chatgabi'); ?></h5>
                                <ol>
                                    <li><?php _e('Update your mobile browser to the latest version', 'chatgabi'); ?></li>
                                    <li><?php _e('Close other apps to free up memory', 'chatgabi'); ?></li>
                                    <li><?php _e('Switch from mobile data to WiFi or vice versa', 'chatgabi'); ?></li>
                                    <li><?php _e('Try rotating your device to landscape mode', 'chatgabi'); ?></li>
                                    <li><?php _e('Clear mobile browser cache', 'chatgabi'); ?></li>
                                </ol>
                            </div>
                            <div class="solution-note">
                                <strong><?php _e('Mobile app:', 'chatgabi'); ?></strong>
                                <p><?php _e('A dedicated mobile app is coming soon for iOS and Android with enhanced offline capabilities.', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Codes Reference -->
        <div class="error-codes-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Error Codes Reference', 'chatgabi'); ?></h2>
                <p><?php _e('Understanding error messages and how to resolve them', 'chatgabi'); ?></p>
            </div>

            <div class="error-codes-grid">
                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E001</span>
                        <h4><?php _e('Insufficient Credits', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('You don\'t have enough credits to complete this request.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Purchase more credits or wait for your monthly allocation to reset.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="error-action"><?php _e('Buy Credits', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E002</span>
                        <h4><?php _e('Rate Limit Exceeded', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('You\'ve made too many requests in a short time period.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Wait 60 seconds before making another request, or upgrade your plan.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="error-action"><?php _e('Upgrade Plan', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E003</span>
                        <h4><?php _e('Authentication Failed', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('Your session has expired or login credentials are invalid.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Log out and log back in, or reset your password.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/login')); ?>" class="error-action"><?php _e('Login Again', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E004</span>
                        <h4><?php _e('Service Temporarily Unavailable', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('Our AI service is temporarily down for maintenance.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Try again in a few minutes. Check our status page for updates.', 'chatgabi'); ?></p>
                        <a href="#" class="error-action"><?php _e('Status Page', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E005</span>
                        <h4><?php _e('Invalid Input Format', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('The data you submitted contains invalid characters or format.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Check for special characters, ensure text is under 2000 characters.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/help')); ?>" class="error-action"><?php _e('Input Guidelines', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="error-code-item">
                    <div class="error-header">
                        <span class="error-code">E006</span>
                        <h4><?php _e('Content Policy Violation', 'chatgabi'); ?></h4>
                    </div>
                    <p class="error-description"><?php _e('Your request violates our content policy guidelines.', 'chatgabi'); ?></p>
                    <div class="error-solution">
                        <strong><?php _e('Solution:', 'chatgabi'); ?></strong>
                        <p><?php _e('Rephrase your question to focus on legitimate business topics.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/terms')); ?>" class="error-action"><?php _e('Content Policy', 'chatgabi'); ?></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="system-requirements-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('System Requirements', 'chatgabi'); ?></h2>
                <p><?php _e('Ensure your device meets the minimum requirements for optimal performance', 'chatgabi'); ?></p>
            </div>

            <div class="requirements-grid">
                <div class="requirement-category">
                    <h3><?php _e('🖥️ Desktop Browsers', 'chatgabi'); ?></h3>
                    <div class="requirement-list">
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Google Chrome</strong>
                                <span>Version 90 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Mozilla Firefox</strong>
                                <span>Version 88 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Safari</strong>
                                <span>Version 14 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Microsoft Edge</strong>
                                <span>Version 90 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item limited">
                            <span class="requirement-icon">⚠️</span>
                            <div class="requirement-details">
                                <strong>Internet Explorer</strong>
                                <span>Not supported</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="requirement-category">
                    <h3><?php _e('📱 Mobile Devices', 'chatgabi'); ?></h3>
                    <div class="requirement-list">
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>iOS Safari</strong>
                                <span>iOS 14.0 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Android Chrome</strong>
                                <span>Android 8.0 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Samsung Internet</strong>
                                <span>Version 14 or later</span>
                            </div>
                        </div>
                        <div class="requirement-item limited">
                            <span class="requirement-icon">⚠️</span>
                            <div class="requirement-details">
                                <strong>Opera Mini</strong>
                                <span>Limited functionality</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="requirement-category">
                    <h3><?php _e('🌐 Internet Connection', 'chatgabi'); ?></h3>
                    <div class="requirement-list">
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Broadband/WiFi</strong>
                                <span>5 Mbps or faster</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>4G/LTE Mobile</strong>
                                <span>Stable connection required</span>
                            </div>
                        </div>
                        <div class="requirement-item limited">
                            <span class="requirement-icon">⚠️</span>
                            <div class="requirement-details">
                                <strong>3G Mobile</strong>
                                <span>Slower performance expected</span>
                            </div>
                        </div>
                        <div class="requirement-item limited">
                            <span class="requirement-icon">❌</span>
                            <div class="requirement-details">
                                <strong>2G/EDGE</strong>
                                <span>Not recommended</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="requirement-category">
                    <h3><?php _e('⚙️ System Specifications', 'chatgabi'); ?></h3>
                    <div class="requirement-list">
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>RAM</strong>
                                <span>4GB minimum, 8GB recommended</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>JavaScript</strong>
                                <span>Must be enabled</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Cookies</strong>
                                <span>Must be enabled</span>
                            </div>
                        </div>
                        <div class="requirement-item supported">
                            <span class="requirement-icon">✅</span>
                            <div class="requirement-details">
                                <strong>Screen Resolution</strong>
                                <span>1024x768 minimum</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="contact-support-section glassmorphism-card">
            <div class="support-content">
                <div class="support-header">
                    <h2><?php _e('Still Need Help?', 'chatgabi'); ?></h2>
                    <p><?php _e('Our technical support team is ready to assist you with any issues', 'chatgabi'); ?></p>
                </div>

                <div class="support-options">
                    <div class="support-option priority">
                        <div class="support-icon">🚨</div>
                        <div class="support-details">
                            <h4><?php _e('Critical Issues', 'chatgabi'); ?></h4>
                            <p><?php _e('Account locked, payment problems, data loss', 'chatgabi'); ?></p>
                            <span class="support-response"><?php _e('Response: Within 1 hour', 'chatgabi'); ?></span>
                        </div>
                        <a href="mailto:<EMAIL>" class="support-action btn btn-danger"><?php _e('Report Critical Issue', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option standard">
                        <div class="support-icon">💬</div>
                        <div class="support-details">
                            <h4><?php _e('Live Chat Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Real-time assistance with technical issues', 'chatgabi'); ?></p>
                            <span class="support-response"><?php _e('Available: 9 AM - 6 PM WAT', 'chatgabi'); ?></span>
                        </div>
                        <a href="#" class="support-action btn btn-primary"><?php _e('Start Chat', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option standard">
                        <div class="support-icon">📧</div>
                        <div class="support-details">
                            <h4><?php _e('Email Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Detailed technical assistance and bug reports', 'chatgabi'); ?></p>
                            <span class="support-response"><?php _e('Response: Within 24 hours', 'chatgabi'); ?></span>
                        </div>
                        <a href="mailto:<EMAIL>" class="support-action btn btn-secondary"><?php _e('Send Email', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option community">
                        <div class="support-icon">👥</div>
                        <div class="support-details">
                            <h4><?php _e('Community Forum', 'chatgabi'); ?></h4>
                            <p><?php _e('Get help from other ChatGABI users and experts', 'chatgabi'); ?></p>
                            <span class="support-response"><?php _e('24/7 community support', 'chatgabi'); ?></span>
                        </div>
                        <a href="#" class="support-action btn btn-secondary"><?php _e('Join Forum', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="support-tips">
                    <h3><?php _e('Before Contacting Support', 'chatgabi'); ?></h3>
                    <div class="tips-grid">
                        <div class="tip-item">
                            <span class="tip-icon">📝</span>
                            <div class="tip-content">
                                <h4><?php _e('Gather Information', 'chatgabi'); ?></h4>
                                <p><?php _e('Note the exact error message, time it occurred, and steps to reproduce', 'chatgabi'); ?></p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <span class="tip-icon">🖼️</span>
                            <div class="tip-content">
                                <h4><?php _e('Take Screenshots', 'chatgabi'); ?></h4>
                                <p><?php _e('Visual evidence helps our team understand and resolve issues faster', 'chatgabi'); ?></p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <span class="tip-icon">🔍</span>
                            <div class="tip-content">
                                <h4><?php _e('Try Basic Fixes', 'chatgabi'); ?></h4>
                                <p><?php _e('Clear cache, try different browser, check internet connection', 'chatgabi'); ?></p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <span class="tip-icon">📊</span>
                            <div class="tip-content">
                                <h4><?php _e('Include System Info', 'chatgabi'); ?></h4>
                                <p><?php _e('Browser version, operating system, device type, and internet speed', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Troubleshooting Page Specific Styles */
.chatgabi-troubleshooting-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.troubleshooting-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.system-status {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 40px 0;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.status-icon {
    font-size: 1.2rem;
}

.status-icon.operational {
    color: #27ae60;
}

.user-diagnostics {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.diagnostic-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 20px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.diagnostic-icon {
    font-size: 1.2rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Diagnostic Tool */
.diagnostic-form-content {
    text-align: center;
    padding: 40px;
}

.diagnostic-description p {
    font-size: 1.1rem;
    color: var(--color-text-primary);
    margin-bottom: 20px;
}

.diagnostic-checks {
    list-style: none;
    padding: 0;
    margin: 30px 0;
    display: grid;
    gap: 15px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.diagnostic-checks li {
    padding: 15px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 8px;
    color: var(--color-text-primary);
    font-weight: 600;
    border-left: 4px solid var(--color-nature-green);
}

.diagnostic-submit {
    margin-top: 30px;
}

.diagnostic-results {
    padding: 30px;
}

.diagnostic-results h3 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 30px;
    text-align: center;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.result-item {
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid;
}

.result-item.pass {
    background: rgba(39, 174, 96, 0.1);
    border-color: #27ae60;
}

.result-item.warning {
    background: rgba(243, 156, 18, 0.1);
    border-color: #f39c12;
}

.result-item.fail {
    background: rgba(231, 76, 60, 0.1);
    border-color: #e74c3c;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.result-icon {
    font-size: 1.5rem;
}

.result-item h4 {
    color: var(--color-primary-accent);
    margin: 0;
    font-size: 1.1rem;
}

.result-item p {
    color: var(--color-text-secondary);
    margin: 0;
    line-height: 1.5;
}

.diagnostic-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Common Issues */
.issues-categories {
    display: grid;
    gap: 40px;
}

.issue-category h3 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(var(--color-primary-accent), 0.2);
}

.issue-item {
    margin-bottom: 20px;
    border: 1px solid rgba(var(--color-borders), 0.3);
    border-radius: 12px;
    overflow: hidden;
    background: rgba(var(--color-primary-accent), 0.03);
}

.issue-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.issue-question:hover {
    background: rgba(var(--color-primary-accent), 0.1);
}

.issue-question h4 {
    color: var(--color-primary-accent);
    margin: 0;
    font-size: 1.2rem;
}

.issue-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--color-primary-accent);
    transition: transform 0.3s ease;
}

.issue-item.active .issue-toggle {
    transform: rotate(45deg);
}

.issue-solution {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.issue-item.active .issue-solution {
    padding: 20px;
    max-height: 500px;
}

.solution-steps h5 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.solution-steps ol {
    color: var(--color-text-primary);
    line-height: 1.6;
    padding-left: 20px;
}

.solution-steps li {
    margin-bottom: 8px;
}

.solution-note {
    margin-top: 20px;
    padding: 15px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--color-secondary-accent);
}

.solution-note strong {
    color: var(--color-secondary-accent);
    display: block;
    margin-bottom: 8px;
}

.solution-note p {
    color: var(--color-text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* Error Codes */
.error-codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.error-code-item {
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid #e74c3c;
}

.error-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.error-code {
    background: #e74c3c;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: bold;
    font-family: monospace;
    font-size: 0.9rem;
}

.error-code-item h4 {
    color: var(--color-primary-accent);
    margin: 0;
    font-size: 1.2rem;
}

.error-description {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 15px;
}

.error-solution strong {
    color: var(--color-primary-accent);
    display: block;
    margin-bottom: 8px;
}

.error-solution p {
    color: var(--color-text-secondary);
    line-height: 1.5;
    margin-bottom: 15px;
}

.error-action {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 8px 15px;
    border: 2px solid var(--color-primary-accent);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.error-action:hover {
    background: var(--color-primary-accent);
    color: white;
}

/* System Requirements */
.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.requirement-category h3 {
    color: var(--color-primary-accent);
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-align: center;
}

.requirement-list {
    display: grid;
    gap: 15px;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.requirement-item.supported {
    background: rgba(39, 174, 96, 0.1);
    border-left: 4px solid #27ae60;
}

.requirement-item.limited {
    background: rgba(243, 156, 18, 0.1);
    border-left: 4px solid #f39c12;
}

.requirement-item:hover {
    transform: translateX(5px);
}

.requirement-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.requirement-details strong {
    color: var(--color-primary-accent);
    display: block;
    margin-bottom: 5px;
    font-size: 1rem;
}

.requirement-details span {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Support Options */
.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.support-option {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.support-option.priority {
    background: rgba(231, 76, 60, 0.1);
    border-left: 4px solid #e74c3c;
}

.support-option.standard {
    background: rgba(var(--color-primary-accent), 0.1);
    border-left: 4px solid var(--color-primary-accent);
}

.support-option.community {
    background: rgba(var(--color-nature-green), 0.1);
    border-left: 4px solid var(--color-nature-green);
}

.support-option:hover {
    transform: translateY(-3px);
}

.support-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.support-details {
    flex: 1;
}

.support-details h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.support-details p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.support-response {
    color: var(--color-secondary-accent);
    font-size: 0.8rem;
    font-weight: bold;
}

.support-action {
    flex-shrink: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--color-primary-accent);
    color: white;
}

.btn-primary:hover {
    background: var(--color-secondary-accent);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
}

.btn-icon {
    font-size: 1rem;
}

/* Support Tips */
.support-tips h3 {
    color: var(--color-primary-accent);
    font-size: 1.5rem;
    margin-bottom: 25px;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-tertiary-accent), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-tertiary-accent);
}

.tip-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.tip-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.tip-content p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-troubleshooting-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .troubleshooting-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .system-status,
    .user-diagnostics {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .diagnostic-form-content {
        padding: 25px 15px;
    }

    .results-grid,
    .error-codes-grid,
    .requirements-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .diagnostic-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .support-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .support-option {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .tips-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tip-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .issue-item,
body.theme-dark .error-code-item,
body.theme-dark .requirement-item,
body.theme-dark .support-option,
body.theme-dark .tip-item {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .issue-question:hover {
    background: rgba(110, 127, 243, 0.2);
}

body.theme-dark .solution-note {
    background: rgba(255, 215, 0, 0.1);
}
</style>

<script>
// Troubleshooting Page JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // Issue accordion functionality
    const issueItems = document.querySelectorAll('.issue-item');

    issueItems.forEach(item => {
        const question = item.querySelector('.issue-question');

        if (question) {
            question.addEventListener('click', function() {
                const isActive = item.classList.contains('active');

                // Close all other items
                issueItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current item
                if (isActive) {
                    item.classList.remove('active');
                } else {
                    item.classList.add('active');
                }
            });
        }
    });

    // Diagnostic tool enhancement
    const diagnosticForm = document.querySelector('.diagnostic-form-content form');
    const diagnosticButton = document.querySelector('button[name="run_diagnostics"]');

    if (diagnosticForm && diagnosticButton) {
        diagnosticForm.addEventListener('submit', function(e) {
            // Show loading state
            const originalText = diagnosticButton.innerHTML;
            diagnosticButton.innerHTML = '<span class="btn-icon">⏳</span> Running Diagnostics...';
            diagnosticButton.disabled = true;

            // Simulate diagnostic process
            setTimeout(() => {
                // Re-enable button after form submission
                diagnosticButton.innerHTML = originalText;
                diagnosticButton.disabled = false;
            }, 3000);
        });
    }

    // System requirements checker
    function checkSystemRequirements() {
        const requirements = {
            browser: checkBrowserCompatibility(),
            javascript: checkJavaScript(),
            cookies: checkCookies(),
            connection: checkConnection(),
            resolution: checkScreenResolution()
        };

        updateRequirementStatus(requirements);
        return requirements;
    }

    function checkBrowserCompatibility() {
        const userAgent = navigator.userAgent;
        const browsers = {
            chrome: /Chrome\/(\d+)/.exec(userAgent),
            firefox: /Firefox\/(\d+)/.exec(userAgent),
            safari: /Version\/(\d+).*Safari/.exec(userAgent),
            edge: /Edg\/(\d+)/.exec(userAgent)
        };

        for (const [browser, match] of Object.entries(browsers)) {
            if (match) {
                const version = parseInt(match[1]);
                const minVersions = { chrome: 90, firefox: 88, safari: 14, edge: 90 };
                return {
                    browser: browser,
                    version: version,
                    supported: version >= minVersions[browser]
                };
            }
        }

        return { browser: 'unknown', version: 0, supported: false };
    }

    function checkJavaScript() {
        return true; // If this runs, JavaScript is enabled
    }

    function checkCookies() {
        document.cookie = 'test=1';
        const cookiesEnabled = document.cookie.indexOf('test=1') !== -1;
        document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        return cookiesEnabled;
    }

    function checkConnection() {
        return navigator.onLine;
    }

    function checkScreenResolution() {
        return {
            width: screen.width,
            height: screen.height,
            supported: screen.width >= 1024 && screen.height >= 768
        };
    }

    function updateRequirementStatus(requirements) {
        // Update requirement items based on actual system check
        const requirementItems = document.querySelectorAll('.requirement-item');

        requirementItems.forEach(item => {
            const details = item.querySelector('.requirement-details strong');
            if (!details) return;

            const requirement = details.textContent.toLowerCase();

            if (requirement.includes('javascript')) {
                updateItemStatus(item, requirements.javascript);
            } else if (requirement.includes('cookies')) {
                updateItemStatus(item, requirements.cookies);
            } else if (requirement.includes('screen resolution')) {
                updateItemStatus(item, requirements.resolution.supported);
            }
        });
    }

    function updateItemStatus(item, isSupported) {
        const icon = item.querySelector('.requirement-icon');
        if (isSupported) {
            item.classList.remove('limited');
            item.classList.add('supported');
            icon.textContent = '✅';
        } else {
            item.classList.remove('supported');
            item.classList.add('limited');
            icon.textContent = '⚠️';
        }
    }

    // Run system check on page load
    checkSystemRequirements();

    // Error code search functionality
    function addErrorCodeSearch() {
        const errorCodesSection = document.querySelector('.error-codes-section');
        if (!errorCodesSection) return;

        const searchContainer = document.createElement('div');
        searchContainer.className = 'error-search-container';
        searchContainer.innerHTML = `
            <input type="text" placeholder="Search error codes..." class="error-search-input">
        `;

        const sectionHeader = errorCodesSection.querySelector('.section-header');
        sectionHeader.appendChild(searchContainer);

        const searchInput = searchContainer.querySelector('.error-search-input');
        const errorItems = document.querySelectorAll('.error-code-item');

        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();

            errorItems.forEach(item => {
                const code = item.querySelector('.error-code').textContent.toLowerCase();
                const title = item.querySelector('h4').textContent.toLowerCase();
                const description = item.querySelector('.error-description').textContent.toLowerCase();

                const matches = code.includes(query) ||
                               title.includes(query) ||
                               description.includes(query);

                item.style.display = matches ? 'block' : 'none';
            });
        });
    }

    addErrorCodeSearch();

    // Support option interactions
    const supportOptions = document.querySelectorAll('.support-option');

    supportOptions.forEach(option => {
        const actionButton = option.querySelector('.support-action');

        if (actionButton) {
            actionButton.addEventListener('click', function(e) {
                const supportType = option.querySelector('h4').textContent;

                // Special handling for live chat
                if (supportType.includes('Live Chat')) {
                    e.preventDefault();

                    // Check if chat is available
                    const currentHour = new Date().getHours();
                    const isAvailable = currentHour >= 9 && currentHour < 18;

                    if (isAvailable) {
                        alert('Connecting you to live chat support...\n\nThis would open a chat widget in production.');
                    } else {
                        alert('Live chat is currently offline.\n\nOur support hours are 9 AM - 6 PM WAT.\n\nPlease try email support or check back during business hours.');
                    }
                }

                // Track support interactions
                console.log('Support option clicked:', supportType);
            });
        }
    });

    // Animated counters for system status
    function animateCounters() {
        const statusItems = document.querySelectorAll('.status-item');

        statusItems.forEach(item => {
            const text = item.querySelector('.status-text');
            if (text && text.textContent.includes('180ms')) {
                // Animate response time
                let currentTime = 0;
                const targetTime = 180;
                const increment = targetTime / 30;

                const timer = setInterval(() => {
                    currentTime += increment;
                    if (currentTime >= targetTime) {
                        currentTime = targetTime;
                        clearInterval(timer);
                    }
                    text.textContent = `Response Time: ${Math.round(currentTime)}ms`;
                }, 50);
            }
        });
    }

    animateCounters();

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Animate sections on scroll
    document.querySelectorAll('.glassmorphism-card').forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        section.style.transitionDelay = `${index * 100}ms`;
        observer.observe(section);
    });

    // Copy error code functionality
    const errorCodes = document.querySelectorAll('.error-code');

    errorCodes.forEach(code => {
        code.style.cursor = 'pointer';
        code.title = 'Click to copy error code';

        code.addEventListener('click', function() {
            const codeText = this.textContent;

            navigator.clipboard.writeText(codeText).then(() => {
                // Show feedback
                const originalBg = this.style.backgroundColor;
                this.style.backgroundColor = '#27ae60';
                this.textContent = 'Copied!';

                setTimeout(() => {
                    this.style.backgroundColor = originalBg;
                    this.textContent = codeText;
                }, 1500);
            });
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + / to focus error search
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            const errorSearch = document.querySelector('.error-search-input');
            if (errorSearch) {
                errorSearch.focus();
                errorSearch.select();
            }
        }

        // Escape to close all accordions
        if (e.key === 'Escape') {
            issueItems.forEach(item => {
                item.classList.remove('active');
            });
        }
    });

    // Auto-expand relevant issues based on URL hash
    if (window.location.hash) {
        const hash = window.location.hash.substring(1);
        const relevantIssue = document.querySelector(`[data-issue="${hash}"]`);
        if (relevantIssue) {
            relevantIssue.classList.add('active');
            relevantIssue.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Add custom CSS for search and animations
    const style = document.createElement('style');
    style.textContent = `
        .error-search-container {
            margin-top: 20px;
        }

        .error-search-input {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid var(--color-borders);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .error-search-input:focus {
            outline: none;
            border-color: var(--color-primary-accent);
        }

        .error-code:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        .support-option:hover .support-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .requirement-item:hover .requirement-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .tip-item:hover .tip-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-icon.operational {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .error-search-input {
                max-width: 100%;
            }
        }
    `;
    document.head.appendChild(style);

    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate any responsive elements if needed
        }, 250);
    });
});
</script>

<?php
// Add SEO meta tags
function chatgabi_troubleshooting_meta() {
    $title = __('ChatGABI Troubleshooting Guide - Fix Common Issues & Get Support', 'chatgabi');
    $description = __('Resolve ChatGABI issues quickly with our comprehensive troubleshooting guide. Find solutions for login problems, AI chat issues, payment errors, and technical difficulties.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
    echo '<meta name="robots" content="index, follow">';
}
add_action('wp_head', 'chatgabi_troubleshooting_meta');

get_footer();
?>
