<?php
/**
 * Template Name: API Documentation
 * 
 * API Documentation for ChatGABI
 * Complete developer reference and interactive API explorer
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user data for API key display
$current_user = wp_get_current_user();
$api_key = '';
$user_credits = 0;

if ($current_user->ID) {
    $api_key = get_user_meta($current_user->ID, 'chatgabi_api_key', true);
    $user_credits = get_user_meta($current_user->ID, 'businesscraft_credits', true) ?: 0;
    
    // Generate API key if it doesn't exist
    if (empty($api_key)) {
        $api_key = 'cgabi_' . wp_generate_password(32, false);
        update_user_meta($current_user->ID, 'chatgabi_api_key', $api_key);
    }
}

get_header();
?>

<div class="chatgabi-api-docs-page">
    <div class="container">
        <!-- API Documentation Hero -->
        <div class="api-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🔌</span>
                    <?php _e('ChatGABI API Documentation', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Integrate ChatGABI\'s AI business intelligence into your applications with our comprehensive REST API. Built for African developers and businesses.', 'chatgabi'); ?>
                </p>
                
                <div class="api-stats">
                    <div class="stat-item">
                        <span class="stat-icon">⚡</span>
                        <span class="stat-text"><?php _e('< 200ms Response Time', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🔒</span>
                        <span class="stat-text"><?php _e('Enterprise Security', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🌍</span>
                        <span class="stat-text"><?php _e('African Data Centers', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">📊</span>
                        <span class="stat-text"><?php _e('99.9% Uptime SLA', 'chatgabi'); ?></span>
                    </div>
                </div>
                
                <?php if ($current_user->ID): ?>
                    <div class="api-key-section">
                        <div class="api-key-display">
                            <label><?php _e('Your API Key:', 'chatgabi'); ?></label>
                            <div class="api-key-input">
                                <input type="text" value="<?php echo esc_attr($api_key); ?>" readonly id="api-key-field">
                                <button class="copy-btn" data-copy="<?php echo esc_attr($api_key); ?>">
                                    <span class="copy-icon">📋</span>
                                    <?php _e('Copy', 'chatgabi'); ?>
                                </button>
                            </div>
                            <small class="api-key-note">
                                <?php printf(__('You have %s credits available for API usage', 'chatgabi'), '<strong>' . number_format($user_credits) . '</strong>'); ?>
                            </small>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="api-auth-notice">
                        <p><?php _e('Sign in to get your API key and start building with ChatGABI', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/login')); ?>" class="btn btn-primary">
                            <span class="btn-icon">🔑</span>
                            <?php _e('Get API Access', 'chatgabi'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Start Guide -->
        <div class="quick-start-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Quick Start', 'chatgabi'); ?></h2>
                <p><?php _e('Get up and running with the ChatGABI API in minutes', 'chatgabi'); ?></p>
            </div>
            
            <div class="quick-start-steps">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3><?php _e('Get Your API Key', 'chatgabi'); ?></h3>
                        <p><?php _e('Sign up for a ChatGABI account and copy your API key from above.', 'chatgabi'); ?></p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3><?php _e('Make Your First Request', 'chatgabi'); ?></h3>
                        <p><?php _e('Send a POST request to our AI chat endpoint with your business question.', 'chatgabi'); ?></p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3><?php _e('Handle the Response', 'chatgabi'); ?></h3>
                        <p><?php _e('Process the AI-generated insights and integrate them into your application.', 'chatgabi'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="code-example">
                <div class="code-header">
                    <h4><?php _e('Example Request', 'chatgabi'); ?></h4>
                    <div class="code-tabs">
                        <button class="code-tab active" data-lang="curl">cURL</button>
                        <button class="code-tab" data-lang="javascript">JavaScript</button>
                        <button class="code-tab" data-lang="python">Python</button>
                        <button class="code-tab" data-lang="php">PHP</button>
                    </div>
                </div>
                
                <div class="code-content">
                    <div class="code-block active" data-lang="curl">
                        <pre><code>curl -X POST https://api.chatgabi.com/v1/chat \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the key opportunities in Ghana'\''s fintech sector?",
    "context": {
      "country": "Ghana",
      "sector": "Fintech",
      "language": "en"
    }
  }'</code></pre>
                    </div>
                    
                    <div class="code-block" data-lang="javascript">
                        <pre><code>const response = await fetch('https://api.chatgabi.com/v1/chat', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "What are the key opportunities in Ghana's fintech sector?",
    context: {
      country: "Ghana",
      sector: "Fintech",
      language: "en"
    }
  })
});

const data = await response.json();
console.log(data.response);</code></pre>
                    </div>
                    
                    <div class="code-block" data-lang="python">
                        <pre><code>import requests

url = "https://api.chatgabi.com/v1/chat"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "message": "What are the key opportunities in Ghana's fintech sector?",
    "context": {
        "country": "Ghana",
        "sector": "Fintech",
        "language": "en"
    }
}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result['response'])</code></pre>
                    </div>
                    
                    <div class="code-block" data-lang="php">
                        <pre><code><?php
$url = 'https://api.chatgabi.com/v1/chat';
$headers = [
    'Authorization: Bearer YOUR_API_KEY',
    'Content-Type: application/json'
];
$data = [
    'message' => "What are the key opportunities in Ghana's fintech sector?",
    'context' => [
        'country' => 'Ghana',
        'sector' => 'Fintech',
        'language' => 'en'
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$result = json_decode($response, true);
echo $result['response'];
?></code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Navigation -->
        <div class="api-navigation glassmorphism-card">
            <div class="nav-header">
                <h2><?php _e('API Reference', 'chatgabi'); ?></h2>
                <p><?php _e('Complete documentation for all ChatGABI API endpoints', 'chatgabi'); ?></p>
            </div>
            
            <div class="api-nav-grid">
                <a href="#authentication" class="nav-item">
                    <div class="nav-icon">🔐</div>
                    <div class="nav-content">
                        <h3><?php _e('Authentication', 'chatgabi'); ?></h3>
                        <p><?php _e('API key setup and security', 'chatgabi'); ?></p>
                    </div>
                </a>
                
                <a href="#chat-endpoint" class="nav-item">
                    <div class="nav-icon">💬</div>
                    <div class="nav-content">
                        <h3><?php _e('AI Chat', 'chatgabi'); ?></h3>
                        <p><?php _e('Business intelligence conversations', 'chatgabi'); ?></p>
                    </div>
                </a>
                
                <a href="#templates-endpoint" class="nav-item">
                    <div class="nav-icon">📝</div>
                    <div class="nav-content">
                        <h3><?php _e('Templates', 'chatgabi'); ?></h3>
                        <p><?php _e('Generate business documents', 'chatgabi'); ?></p>
                    </div>
                </a>
                
                <a href="#analysis-endpoint" class="nav-item">
                    <div class="nav-icon">📊</div>
                    <div class="nav-content">
                        <h3><?php _e('Market Analysis', 'chatgabi'); ?></h3>
                        <p><?php _e('Sector and competition insights', 'chatgabi'); ?></p>
                    </div>
                </a>
                
                <a href="#credits-endpoint" class="nav-item">
                    <div class="nav-icon">💳</div>
                    <div class="nav-content">
                        <h3><?php _e('Credits', 'chatgabi'); ?></h3>
                        <p><?php _e('Usage tracking and billing', 'chatgabi'); ?></p>
                    </div>
                </a>
                
                <a href="#webhooks" class="nav-item">
                    <div class="nav-icon">🔗</div>
                    <div class="nav-content">
                        <h3><?php _e('Webhooks', 'chatgabi'); ?></h3>
                        <p><?php _e('Real-time event notifications', 'chatgabi'); ?></p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Authentication Section -->
        <div id="authentication" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Authentication', 'chatgabi'); ?></h2>
                <p><?php _e('Secure your API requests with bearer token authentication', 'chatgabi'); ?></p>
            </div>
            
            <div class="auth-content">
                <div class="auth-method">
                    <h3><?php _e('Bearer Token Authentication', 'chatgabi'); ?></h3>
                    <p><?php _e('All API requests must include your API key in the Authorization header:', 'chatgabi'); ?></p>
                    
                    <div class="code-example">
                        <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                    </div>
                </div>
                
                <div class="auth-security">
                    <h3><?php _e('Security Best Practices', 'chatgabi'); ?></h3>
                    <ul class="security-list">
                        <li><?php _e('Never expose your API key in client-side code', 'chatgabi'); ?></li>
                        <li><?php _e('Store API keys securely in environment variables', 'chatgabi'); ?></li>
                        <li><?php _e('Use HTTPS for all API requests', 'chatgabi'); ?></li>
                        <li><?php _e('Rotate your API keys regularly', 'chatgabi'); ?></li>
                        <li><?php _e('Monitor your API usage for unusual activity', 'chatgabi'); ?></li>
                    </ul>
                </div>
                
                <div class="rate-limits">
                    <h3><?php _e('Rate Limits', 'chatgabi'); ?></h3>
                    <div class="limits-table">
                        <div class="limit-row">
                            <span class="limit-type"><?php _e('Free Tier', 'chatgabi'); ?></span>
                            <span class="limit-value">100 <?php _e('requests/hour', 'chatgabi'); ?></span>
                        </div>
                        <div class="limit-row">
                            <span class="limit-type"><?php _e('Paid Tier', 'chatgabi'); ?></span>
                            <span class="limit-value">1,000 <?php _e('requests/hour', 'chatgabi'); ?></span>
                        </div>
                        <div class="limit-row">
                            <span class="limit-type"><?php _e('Enterprise', 'chatgabi'); ?></span>
                            <span class="limit-value"><?php _e('Custom limits', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Chat Endpoint -->
        <div id="chat-endpoint" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('AI Chat Endpoint', 'chatgabi'); ?></h2>
                <p><?php _e('Get AI-powered business intelligence and advice', 'chatgabi'); ?></p>
            </div>
            
            <div class="endpoint-details">
                <div class="endpoint-info">
                    <div class="method-url">
                        <span class="http-method post">POST</span>
                        <span class="endpoint-url">https://api.chatgabi.com/v1/chat</span>
                    </div>
                    <div class="endpoint-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">1 <?php _e('credit per request', 'chatgabi'); ?></span>
                    </div>
                </div>
                
                <div class="parameters-section">
                    <h3><?php _e('Request Parameters', 'chatgabi'); ?></h3>
                    <div class="parameters-table">
                        <div class="param-row">
                            <div class="param-name">message <span class="required">*</span></div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Your business question or prompt', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">context</div>
                            <div class="param-type">object</div>
                            <div class="param-description"><?php _e('Additional context for better responses', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">context.country</div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Target country (Ghana, Kenya, Nigeria, South Africa)', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">context.sector</div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Business sector or industry', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">context.language</div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Response language (en, tw, sw, yo, zu)', 'chatgabi'); ?></div>
                        </div>
                    </div>
                </div>
                
                <div class="response-section">
                    <h3><?php _e('Response Format', 'chatgabi'); ?></h3>
                    <div class="code-example">
                        <pre><code>{
  "success": true,
  "response": "Based on Ghana's fintech landscape, key opportunities include...",
  "confidence": 0.92,
  "sources": [
    "Bank of Ghana Fintech Guidelines 2023",
    "Ghana Fintech Market Report"
  ],
  "credits_used": 1,
  "credits_remaining": 99,
  "request_id": "req_abc123"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Templates Endpoint -->
        <div id="templates-endpoint" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Templates Endpoint', 'chatgabi'); ?></h2>
                <p><?php _e('Generate professional business documents with AI', 'chatgabi'); ?></p>
            </div>

            <div class="endpoint-details">
                <div class="endpoint-info">
                    <div class="method-url">
                        <span class="http-method post">POST</span>
                        <span class="endpoint-url">https://api.chatgabi.com/v1/templates/generate</span>
                    </div>
                    <div class="endpoint-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">2 <?php _e('credits per request', 'chatgabi'); ?></span>
                    </div>
                </div>

                <div class="parameters-section">
                    <h3><?php _e('Request Parameters', 'chatgabi'); ?></h3>
                    <div class="parameters-table">
                        <div class="param-row">
                            <div class="param-name">template_type <span class="required">*</span></div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('business_plan, marketing_strategy, financial_projection, pitch_deck', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">business_info <span class="required">*</span></div>
                            <div class="param-type">object</div>
                            <div class="param-description"><?php _e('Business information for template generation', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">format</div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Output format: json, html, markdown (default: json)', 'chatgabi'); ?></div>
                        </div>
                    </div>
                </div>

                <div class="response-section">
                    <h3><?php _e('Response Format', 'chatgabi'); ?></h3>
                    <div class="code-example">
                        <pre><code>{
  "success": true,
  "template": {
    "title": "Business Plan for Fintech Startup",
    "sections": [
      {
        "heading": "Executive Summary",
        "content": "Your fintech startup aims to..."
      }
    ]
  },
  "download_url": "https://api.chatgabi.com/downloads/bp_abc123.pdf",
  "credits_used": 2,
  "credits_remaining": 97
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Analysis Endpoint -->
        <div id="analysis-endpoint" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Market Analysis Endpoint', 'chatgabi'); ?></h2>
                <p><?php _e('Get comprehensive market insights and competitive analysis', 'chatgabi'); ?></p>
            </div>

            <div class="endpoint-details">
                <div class="endpoint-info">
                    <div class="method-url">
                        <span class="http-method post">POST</span>
                        <span class="endpoint-url">https://api.chatgabi.com/v1/analysis/market</span>
                    </div>
                    <div class="endpoint-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">3 <?php _e('credits per request', 'chatgabi'); ?></span>
                    </div>
                </div>

                <div class="parameters-section">
                    <h3><?php _e('Request Parameters', 'chatgabi'); ?></h3>
                    <div class="parameters-table">
                        <div class="param-row">
                            <div class="param-name">sector <span class="required">*</span></div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Business sector to analyze', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">country <span class="required">*</span></div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('Target country for analysis', 'chatgabi'); ?></div>
                        </div>
                        <div class="param-row">
                            <div class="param-name">analysis_type</div>
                            <div class="param-type">string</div>
                            <div class="param-description"><?php _e('market_size, competition, opportunities, trends', 'chatgabi'); ?></div>
                        </div>
                    </div>
                </div>

                <div class="response-section">
                    <h3><?php _e('Response Format', 'chatgabi'); ?></h3>
                    <div class="code-example">
                        <pre><code>{
  "success": true,
  "analysis": {
    "market_size": {
      "value": 2.5,
      "currency": "USD",
      "unit": "billion",
      "growth_rate": 15.2
    },
    "key_players": [
      "MTN Mobile Money",
      "Vodafone Cash"
    ],
    "opportunities": [
      "Rural banking penetration",
      "SME lending solutions"
    ],
    "challenges": [
      "Regulatory compliance",
      "Infrastructure limitations"
    ]
  },
  "credits_used": 3,
  "credits_remaining": 94
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credits Endpoint -->
        <div id="credits-endpoint" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Credits Endpoint', 'chatgabi'); ?></h2>
                <p><?php _e('Monitor your API usage and credit balance', 'chatgabi'); ?></p>
            </div>

            <div class="endpoint-details">
                <div class="endpoint-info">
                    <div class="method-url">
                        <span class="http-method get">GET</span>
                        <span class="endpoint-url">https://api.chatgabi.com/v1/credits/balance</span>
                    </div>
                    <div class="endpoint-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value"><?php _e('Free', 'chatgabi'); ?></span>
                    </div>
                </div>

                <div class="response-section">
                    <h3><?php _e('Response Format', 'chatgabi'); ?></h3>
                    <div class="code-example">
                        <pre><code>{
  "success": true,
  "balance": {
    "current": 97,
    "total_purchased": 100,
    "total_used": 3
  },
  "usage_today": 3,
  "usage_this_month": 15,
  "last_purchase": "2024-12-01T10:30:00Z"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Webhooks Section -->
        <div id="webhooks" class="api-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Webhooks', 'chatgabi'); ?></h2>
                <p><?php _e('Receive real-time notifications for important events', 'chatgabi'); ?></p>
            </div>

            <div class="webhooks-content">
                <div class="webhook-events">
                    <h3><?php _e('Available Events', 'chatgabi'); ?></h3>
                    <div class="events-list">
                        <div class="event-item">
                            <div class="event-name">credits.low</div>
                            <div class="event-description"><?php _e('Triggered when credits fall below 10', 'chatgabi'); ?></div>
                        </div>
                        <div class="event-item">
                            <div class="event-name">analysis.completed</div>
                            <div class="event-description"><?php _e('Triggered when market analysis is finished', 'chatgabi'); ?></div>
                        </div>
                        <div class="event-item">
                            <div class="event-name">template.generated</div>
                            <div class="event-description"><?php _e('Triggered when document generation is complete', 'chatgabi'); ?></div>
                        </div>
                    </div>
                </div>

                <div class="webhook-setup">
                    <h3><?php _e('Setting Up Webhooks', 'chatgabi'); ?></h3>
                    <div class="code-example">
                        <pre><code>POST https://api.chatgabi.com/v1/webhooks

{
  "url": "https://your-app.com/webhooks/chatgabi",
  "events": ["credits.low", "analysis.completed"],
  "secret": "your_webhook_secret"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Codes -->
        <div class="error-codes-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Error Codes', 'chatgabi'); ?></h2>
                <p><?php _e('Understanding API error responses and how to handle them', 'chatgabi'); ?></p>
            </div>

            <div class="error-codes-table">
                <div class="error-row">
                    <div class="error-code">400</div>
                    <div class="error-name">Bad Request</div>
                    <div class="error-description"><?php _e('Invalid request parameters or missing required fields', 'chatgabi'); ?></div>
                </div>
                <div class="error-row">
                    <div class="error-code">401</div>
                    <div class="error-name">Unauthorized</div>
                    <div class="error-description"><?php _e('Invalid or missing API key', 'chatgabi'); ?></div>
                </div>
                <div class="error-row">
                    <div class="error-code">402</div>
                    <div class="error-name">Insufficient Credits</div>
                    <div class="error-description"><?php _e('Not enough credits to complete the request', 'chatgabi'); ?></div>
                </div>
                <div class="error-row">
                    <div class="error-code">429</div>
                    <div class="error-name">Rate Limited</div>
                    <div class="error-description"><?php _e('Too many requests, please slow down', 'chatgabi'); ?></div>
                </div>
                <div class="error-row">
                    <div class="error-code">500</div>
                    <div class="error-name">Server Error</div>
                    <div class="error-description"><?php _e('Internal server error, please try again later', 'chatgabi'); ?></div>
                </div>
            </div>
        </div>

        <!-- SDKs and Libraries -->
        <div class="sdks-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('SDKs & Libraries', 'chatgabi'); ?></h2>
                <p><?php _e('Official and community-maintained libraries for popular programming languages', 'chatgabi'); ?></p>
            </div>

            <div class="sdks-grid">
                <div class="sdk-card">
                    <div class="sdk-header">
                        <div class="sdk-icon">🟨</div>
                        <h3>JavaScript/Node.js</h3>
                        <span class="sdk-status official"><?php _e('Official', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Full-featured SDK for Node.js and browser environments', 'chatgabi'); ?></p>
                    <div class="sdk-install">
                        <code>npm install chatgabi-sdk</code>
                    </div>
                    <a href="#" class="sdk-link"><?php _e('View Documentation', 'chatgabi'); ?></a>
                </div>

                <div class="sdk-card">
                    <div class="sdk-header">
                        <div class="sdk-icon">🐍</div>
                        <h3>Python</h3>
                        <span class="sdk-status official"><?php _e('Official', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Python SDK with async support and type hints', 'chatgabi'); ?></p>
                    <div class="sdk-install">
                        <code>pip install chatgabi</code>
                    </div>
                    <a href="#" class="sdk-link"><?php _e('View Documentation', 'chatgabi'); ?></a>
                </div>

                <div class="sdk-card">
                    <div class="sdk-header">
                        <div class="sdk-icon">🐘</div>
                        <h3>PHP</h3>
                        <span class="sdk-status official"><?php _e('Official', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('PHP SDK with Laravel and WordPress integrations', 'chatgabi'); ?></p>
                    <div class="sdk-install">
                        <code>composer require chatgabi/php-sdk</code>
                    </div>
                    <a href="#" class="sdk-link"><?php _e('View Documentation', 'chatgabi'); ?></a>
                </div>

                <div class="sdk-card">
                    <div class="sdk-header">
                        <div class="sdk-icon">🦀</div>
                        <h3>Rust</h3>
                        <span class="sdk-status community"><?php _e('Community', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('High-performance Rust client with tokio async support', 'chatgabi'); ?></p>
                    <div class="sdk-install">
                        <code>cargo add chatgabi</code>
                    </div>
                    <a href="#" class="sdk-link"><?php _e('View Documentation', 'chatgabi'); ?></a>
                </div>
            </div>
        </div>

        <!-- Support Section -->
        <div class="api-support-section glassmorphism-card">
            <div class="support-content">
                <div class="support-header">
                    <h2><?php _e('Developer Support', 'chatgabi'); ?></h2>
                    <p><?php _e('Get help building with the ChatGABI API', 'chatgabi'); ?></p>
                </div>

                <div class="support-options">
                    <div class="support-option">
                        <div class="support-icon">📚</div>
                        <div class="support-details">
                            <h4><?php _e('Documentation', 'chatgabi'); ?></h4>
                            <p><?php _e('Comprehensive guides and examples', 'chatgabi'); ?></p>
                        </div>
                        <a href="<?php echo esc_url(home_url('/help')); ?>" class="support-action btn btn-secondary"><?php _e('Browse Docs', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option">
                        <div class="support-icon">💬</div>
                        <div class="support-details">
                            <h4><?php _e('Developer Discord', 'chatgabi'); ?></h4>
                            <p><?php _e('Join our community of African developers', 'chatgabi'); ?></p>
                        </div>
                        <a href="#" class="support-action btn btn-primary"><?php _e('Join Discord', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option">
                        <div class="support-icon">🐛</div>
                        <div class="support-details">
                            <h4><?php _e('Report Issues', 'chatgabi'); ?></h4>
                            <p><?php _e('Found a bug or have a feature request?', 'chatgabi'); ?></p>
                        </div>
                        <a href="mailto:<EMAIL>" class="support-action btn btn-secondary"><?php _e('Contact Us', 'chatgabi'); ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* API Documentation Page Specific Styles */
.chatgabi-api-docs-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.api-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.api-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 40px 0;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.stat-icon {
    font-size: 1.5rem;
}

/* API Key Section */
.api-key-section {
    max-width: 600px;
    margin: 40px auto 0;
}

.api-key-display label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--color-text-primary);
}

.api-key-input {
    display: flex;
    gap: 0;
    margin-bottom: 10px;
}

.api-key-input input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--color-borders);
    border-right: none;
    border-radius: 8px 0 0 8px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
    font-family: monospace;
    font-size: 0.9rem;
}

.copy-btn {
    padding: 12px 20px;
    background: var(--color-primary-accent);
    color: white;
    border: 2px solid var(--color-primary-accent);
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: var(--color-secondary-accent);
    border-color: var(--color-secondary-accent);
}

.copy-icon {
    margin-right: 5px;
}

.api-key-note {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

.api-auth-notice {
    text-align: center;
    padding: 30px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 12px;
    border: 2px solid var(--color-secondary-accent);
}

.api-auth-notice p {
    color: var(--color-text-primary);
    margin-bottom: 20px;
    font-size: 1.1rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Quick Start Steps */
.quick-start-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--color-nature-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h3 {
    color: var(--color-primary-accent);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.step-content p {
    color: var(--color-text-secondary);
    line-height: 1.5;
}

/* Code Examples */
.code-example {
    background: #1e1e1e;
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
}

.code-header h4 {
    color: #ffffff;
    margin: 0;
    font-size: 1rem;
}

.code-tabs {
    display: flex;
    gap: 5px;
}

.code-tab {
    padding: 6px 12px;
    background: #404040;
    color: #cccccc;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.code-tab.active,
.code-tab:hover {
    background: var(--color-primary-accent);
    color: white;
}

.code-content {
    position: relative;
}

.code-block {
    display: none;
    padding: 0;
}

.code-block.active {
    display: block;
}

.code-block pre {
    margin: 0;
    padding: 20px;
    background: #1e1e1e;
    color: #f8f8f2;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow-x: auto;
}

.code-block code {
    color: #f8f8f2;
}

/* API Navigation */
.api-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    text-decoration: none;
    color: var(--color-text-primary);
    transition: all 0.3s ease;
    border-left: 4px solid var(--color-primary-accent);
}

.nav-item:hover {
    background: rgba(var(--color-primary-accent), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.nav-content h3 {
    color: var(--color-primary-accent);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.nav-content p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* API Sections */
.api-section {
    scroll-margin-top: 100px;
}

.endpoint-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    flex-wrap: wrap;
    gap: 15px;
}

.method-url {
    display: flex;
    align-items: center;
    gap: 15px;
}

.http-method {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 0.8rem;
    color: white;
}

.http-method.post {
    background: #28a745;
}

.http-method.get {
    background: #007bff;
}

.http-method.put {
    background: #ffc107;
    color: #000;
}

.http-method.delete {
    background: #dc3545;
}

.endpoint-url {
    font-family: monospace;
    background: rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    color: var(--color-text-primary);
    font-size: 0.9rem;
}

.endpoint-cost {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cost-label {
    color: var(--color-text-secondary);
    font-weight: 600;
}

.cost-value {
    background: var(--color-secondary-accent);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Parameters Table */
.parameters-table {
    display: grid;
    gap: 15px;
    margin: 20px 0;
}

.param-row {
    display: grid;
    grid-template-columns: 200px 100px 1fr;
    gap: 20px;
    padding: 15px;
    background: rgba(var(--color-primary-accent), 0.03);
    border-radius: 8px;
    align-items: start;
}

.param-name {
    font-family: monospace;
    font-weight: bold;
    color: var(--color-primary-accent);
}

.required {
    color: #e74c3c;
}

.param-type {
    background: rgba(var(--color-secondary-accent), 0.1);
    color: var(--color-secondary-accent);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.param-description {
    color: var(--color-text-secondary);
    line-height: 1.5;
}

/* Authentication Section */
.auth-content {
    display: grid;
    gap: 30px;
}

.security-list {
    list-style: none;
    padding: 0;
}

.security-list li {
    padding: 10px 0;
    color: var(--color-text-primary);
    position: relative;
    padding-left: 25px;
}

.security-list li:before {
    content: "🔒";
    position: absolute;
    left: 0;
}

.limits-table {
    display: grid;
    gap: 10px;
}

.limit-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 8px;
}

.limit-type {
    font-weight: 600;
    color: var(--color-text-primary);
}

.limit-value {
    background: var(--color-secondary-accent);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Webhooks */
.webhooks-content {
    display: grid;
    gap: 30px;
}

.events-list {
    display: grid;
    gap: 15px;
}

.event-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 8px;
}

.event-name {
    font-family: monospace;
    font-weight: bold;
    color: var(--color-primary-accent);
}

.event-description {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Error Codes */
.error-codes-table {
    display: grid;
    gap: 15px;
}

.error-row {
    display: grid;
    grid-template-columns: 80px 150px 1fr;
    gap: 20px;
    padding: 15px;
    background: rgba(var(--color-primary-accent), 0.03);
    border-radius: 8px;
    align-items: center;
}

.error-code {
    background: #e74c3c;
    color: white;
    padding: 8px;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    font-family: monospace;
}

.error-name {
    font-weight: bold;
    color: var(--color-primary-accent);
}

.error-description {
    color: var(--color-text-secondary);
    line-height: 1.5;
}

/* SDKs Grid */
.sdks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.sdk-card {
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
    transition: transform 0.3s ease;
}

.sdk-card:hover {
    transform: translateY(-3px);
}

.sdk-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.sdk-icon {
    font-size: 2rem;
}

.sdk-header h3 {
    color: var(--color-primary-accent);
    margin: 0;
    flex: 1;
}

.sdk-status {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
}

.sdk-status.official {
    background: var(--color-nature-green);
    color: white;
}

.sdk-status.community {
    background: var(--color-secondary-accent);
    color: white;
}

.sdk-card p {
    color: var(--color-text-secondary);
    line-height: 1.5;
    margin-bottom: 15px;
}

.sdk-install {
    background: #1e1e1e;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.sdk-install code {
    color: #f8f8f2;
    font-family: monospace;
    font-size: 0.9rem;
}

.sdk-link {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.sdk-link:hover {
    text-decoration: underline;
}

/* Support Options */
.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.support-option {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.support-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.support-details {
    flex: 1;
}

.support-details h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.support-details p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

.support-action {
    flex-shrink: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--color-primary-accent);
    color: white;
}

.btn-primary:hover {
    background: var(--color-secondary-accent);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.btn-icon {
    font-size: 1rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-api-docs-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .api-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .api-stats {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .api-key-input {
        flex-direction: column;
    }

    .api-key-input input {
        border-radius: 8px;
        border-right: 2px solid var(--color-borders);
        margin-bottom: 10px;
    }

    .copy-btn {
        border-radius: 8px;
    }

    .quick-start-steps {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .step-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .api-nav-grid,
    .sdks-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .endpoint-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .param-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .error-row {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: center;
    }

    .support-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .support-option {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .code-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .code-tabs {
        width: 100%;
        justify-content: center;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .api-key-input input {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .step-item,
body.theme-dark .nav-item,
body.theme-dark .sdk-card,
body.theme-dark .support-option {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .endpoint-info,
body.theme-dark .param-row,
body.theme-dark .limit-row,
body.theme-dark .event-item,
body.theme-dark .error-row {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .endpoint-url {
    background: rgba(0, 0, 0, 0.3);
}
</style>

<script>
// API Documentation JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // Code tab switching functionality
    const codeTabs = document.querySelectorAll('.code-tab');
    const codeBlocks = document.querySelectorAll('.code-block');

    codeTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const lang = this.dataset.lang;
            const container = this.closest('.code-example');

            // Update active tab
            container.querySelectorAll('.code-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update active code block
            container.querySelectorAll('.code-block').forEach(block => {
                block.classList.remove('active');
                if (block.dataset.lang === lang) {
                    block.classList.add('active');
                }
            });
        });
    });

    // API key copy functionality
    const copyBtn = document.querySelector('.copy-btn');

    if (copyBtn) {
        copyBtn.addEventListener('click', function() {
            const apiKey = this.dataset.copy;

            // Copy to clipboard
            navigator.clipboard.writeText(apiKey).then(() => {
                // Show success feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<span class="copy-icon">✅</span> Copied!';
                this.style.background = '#27ae60';

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.background = '';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = apiKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const originalText = this.innerHTML;
                this.innerHTML = '<span class="copy-icon">✅</span> Copied!';
                this.style.background = '#27ae60';

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.background = '';
                }, 2000);
            });
        });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update URL without triggering scroll
                history.pushState(null, null, this.getAttribute('href'));
            }
        });
    });

    // Code block copy functionality
    function addCopyButtonsToCodeBlocks() {
        const codeBlocks = document.querySelectorAll('.code-block pre');

        codeBlocks.forEach(block => {
            const copyButton = document.createElement('button');
            copyButton.className = 'code-copy-btn';
            copyButton.innerHTML = '📋 Copy';
            copyButton.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 0.8rem;
                cursor: pointer;
                transition: all 0.3s ease;
            `;

            copyButton.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(255, 255, 255, 0.2)';
            });

            copyButton.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(255, 255, 255, 0.1)';
            });

            copyButton.addEventListener('click', function() {
                const code = block.textContent;

                navigator.clipboard.writeText(code).then(() => {
                    this.innerHTML = '✅ Copied!';
                    this.style.background = '#27ae60';

                    setTimeout(() => {
                        this.innerHTML = '📋 Copy';
                        this.style.background = 'rgba(255, 255, 255, 0.1)';
                    }, 2000);
                });
            });

            block.style.position = 'relative';
            block.appendChild(copyButton);
        });
    }

    addCopyButtonsToCodeBlocks();

    // Interactive API explorer (simulated)
    function createAPIExplorer() {
        const explorerButton = document.createElement('button');
        explorerButton.className = 'api-explorer-btn btn btn-primary';
        explorerButton.innerHTML = '<span class="btn-icon">🚀</span> Try API Explorer';
        explorerButton.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        explorerButton.addEventListener('click', function() {
            // Simulate API explorer modal
            alert('API Explorer\n\nThis would open an interactive API testing interface where you can:\n\n• Test endpoints with your API key\n• See real-time responses\n• Generate code snippets\n• Monitor usage\n\nComing soon in the full ChatGABI platform!');
        });

        document.body.appendChild(explorerButton);
    }

    // Only show API explorer if user is logged in
    if (document.querySelector('.api-key-display')) {
        createAPIExplorer();
    }

    // Endpoint sections animation
    const apiSections = document.querySelectorAll('.api-section');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    apiSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        section.style.transitionDelay = `${index * 100}ms`;
        observer.observe(section);
    });

    // Navigation items hover effects
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // SDK cards interaction
    const sdkCards = document.querySelectorAll('.sdk-card');

    sdkCards.forEach(card => {
        card.addEventListener('click', function() {
            const sdkName = this.querySelector('h3').textContent;

            // Simulate SDK documentation redirect
            console.log(`Opening ${sdkName} documentation...`);

            // In production, this would redirect to actual SDK docs
            alert(`${sdkName} SDK Documentation\n\nThis would redirect to comprehensive documentation for the ${sdkName} SDK including:\n\n• Installation guide\n• Authentication setup\n• Code examples\n• API reference\n• Best practices`);
        });

        card.style.cursor = 'pointer';
    });

    // Dynamic API key replacement in code examples
    function updateCodeExamplesWithAPIKey() {
        const apiKeyField = document.getElementById('api-key-field');
        if (!apiKeyField) return;

        const apiKey = apiKeyField.value;
        const codeBlocks = document.querySelectorAll('.code-block code');

        codeBlocks.forEach(block => {
            block.innerHTML = block.innerHTML.replace(/YOUR_API_KEY/g, apiKey);
        });
    }

    updateCodeExamplesWithAPIKey();

    // Rate limit indicator (simulated)
    function createRateLimitIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'rate-limit-indicator';
        indicator.innerHTML = `
            <div class="rate-limit-header">
                <span class="rate-limit-icon">⚡</span>
                <span class="rate-limit-title">API Rate Limit</span>
            </div>
            <div class="rate-limit-bar">
                <div class="rate-limit-fill" style="width: 25%"></div>
            </div>
            <div class="rate-limit-text">25/100 requests this hour</div>
        `;
        indicator.style.cssText = `
            position: fixed;
            top: 100px;
            right: 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 200px;
            font-size: 0.9rem;
        `;

        // Only show for logged-in users
        if (document.querySelector('.api-key-display')) {
            document.body.appendChild(indicator);
        }
    }

    createRateLimitIndicator();

    // Search functionality for API documentation
    function addAPISearch() {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'api-search-container';
        searchContainer.innerHTML = `
            <input type="text" placeholder="Search API documentation..." class="api-search-input">
            <div class="api-search-results"></div>
        `;
        searchContainer.style.cssText = `
            position: fixed;
            top: 30px;
            right: 30px;
            z-index: 1000;
        `;

        const searchInput = searchContainer.querySelector('.api-search-input');
        const searchResults = searchContainer.querySelector('.api-search-results');

        searchInput.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--color-borders);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            width: 250px;
            font-size: 0.9rem;
        `;

        searchResults.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--color-borders);
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
            display: none;
        `;

        // Search functionality
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim().toLowerCase();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performAPISearch(query, searchResults);
                }, 300);
            } else {
                searchResults.style.display = 'none';
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchContainer.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });

        document.body.appendChild(searchContainer);
    }

    function performAPISearch(query, resultsContainer) {
        // Simulate API documentation search
        const searchableContent = [
            { title: 'Authentication', section: '#authentication', description: 'API key setup and security' },
            { title: 'AI Chat Endpoint', section: '#chat-endpoint', description: 'Get AI-powered business intelligence' },
            { title: 'Templates Endpoint', section: '#templates-endpoint', description: 'Generate business documents' },
            { title: 'Market Analysis', section: '#analysis-endpoint', description: 'Market insights and analysis' },
            { title: 'Credits Management', section: '#credits-endpoint', description: 'Monitor usage and billing' },
            { title: 'Webhooks', section: '#webhooks', description: 'Real-time event notifications' },
            { title: 'Error Codes', section: '.error-codes-section', description: 'API error responses' },
            { title: 'Rate Limits', section: '#authentication', description: 'Request rate limiting' }
        ];

        const results = searchableContent.filter(item =>
            item.title.toLowerCase().includes(query) ||
            item.description.toLowerCase().includes(query)
        );

        if (results.length > 0) {
            resultsContainer.innerHTML = results.map(result => `
                <div class="search-result-item" data-section="${result.section}">
                    <div class="result-title">${result.title}</div>
                    <div class="result-description">${result.description}</div>
                </div>
            `).join('');

            resultsContainer.style.display = 'block';

            // Add click handlers
            resultsContainer.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', function() {
                    const section = document.querySelector(this.dataset.section);
                    if (section) {
                        section.scrollIntoView({ behavior: 'smooth' });
                        resultsContainer.style.display = 'none';
                    }
                });
            });
        } else {
            resultsContainer.innerHTML = '<div class="no-results">No results found</div>';
            resultsContainer.style.display = 'block';
        }
    }

    addAPISearch();

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.api-search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Escape to close search results
        if (e.key === 'Escape') {
            const searchResults = document.querySelector('.api-search-results');
            if (searchResults) {
                searchResults.style.display = 'none';
            }
        }
    });

    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate any responsive elements if needed
        }, 250);
    });
});
</script>

<?php
// Add SEO meta tags
function chatgabi_api_docs_meta() {
    $title = __('ChatGABI API Documentation - Developer Reference & Integration Guide', 'chatgabi');
    $description = __('Complete API documentation for ChatGABI AI business intelligence platform. Integrate African-focused AI tools with REST API, SDKs, and comprehensive developer resources.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
    echo '<meta name="robots" content="index, follow">';
}
add_action('wp_head', 'chatgabi_api_docs_meta');

get_footer();
?>
