<?php
/**
 * Template Name: Cookie Policy
 * 
 * Cookie Policy page for ChatGABI
 * Comprehensive cookie usage disclosure
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="chatgabi-legal-page cookie-policy-page">
    <div class="container">
        <!-- Page Header -->
        <div class="legal-page-header glassmorphism-card">
            <div class="header-content">
                <h1 class="page-title">
                    <span class="page-icon">🍪</span>
                    <?php _e('Cookie Policy', 'chatgabi'); ?>
                </h1>
                <p class="page-subtitle">
                    <?php _e('This policy explains how ChatGABI uses cookies and similar technologies to enhance your experience and provide our AI-powered business intelligence services.', 'chatgabi'); ?>
                </p>
                <div class="last-updated">
                    <strong><?php _e('Last Updated:', 'chatgabi'); ?></strong> <?php echo date('F j, Y'); ?>
                </div>
            </div>
        </div>

        <!-- Cookie Policy Content -->
        <div class="legal-content">
            <!-- Section 1: What Are Cookies -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('1. What Are Cookies?', 'chatgabi'); ?></h2>
                <p><?php _e('Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better experience by remembering your preferences and enabling essential functionality.', 'chatgabi'); ?></p>
                <p><?php _e('ChatGABI uses cookies to enhance your AI business intelligence experience, maintain your session, and provide personalized content relevant to African entrepreneurs.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 2: Types of Cookies We Use -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('2. Types of Cookies We Use', 'chatgabi'); ?></h2>
                
                <h3><?php _e('2.1 Essential Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies are necessary for ChatGABI to function properly:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Session management and user authentication', 'chatgabi'); ?></li>
                    <li><?php _e('Security and fraud prevention', 'chatgabi'); ?></li>
                    <li><?php _e('Payment processing through Paystack', 'chatgabi'); ?></li>
                    <li><?php _e('Language preference storage', 'chatgabi'); ?></li>
                    <li><?php _e('Theme preference (light/dark mode)', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('2.2 Functional Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies enhance your experience by remembering your choices:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('User preferences and settings', 'chatgabi'); ?></li>
                    <li><?php _e('Country and region selection', 'chatgabi'); ?></li>
                    <li><?php _e('Industry sector preferences', 'chatgabi'); ?></li>
                    <li><?php _e('Template and tool usage history', 'chatgabi'); ?></li>
                    <li><?php _e('Dashboard customization settings', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('2.3 Analytics Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies help us understand how you use ChatGABI:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Page views and user journey tracking', 'chatgabi'); ?></li>
                    <li><?php _e('Feature usage and engagement metrics', 'chatgabi'); ?></li>
                    <li><?php _e('Performance monitoring and optimization', 'chatgabi'); ?></li>
                    <li><?php _e('Error tracking and debugging information', 'chatgabi'); ?></li>
                    <li><?php _e('A/B testing for feature improvements', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('2.4 Marketing Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies help us provide relevant content and communications:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Email campaign tracking and optimization', 'chatgabi'); ?></li>
                    <li><?php _e('Content personalization based on interests', 'chatgabi'); ?></li>
                    <li><?php _e('Social media integration and sharing', 'chatgabi'); ?></li>
                    <li><?php _e('Referral source tracking', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 3: Third-Party Cookies -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('3. Third-Party Cookies', 'chatgabi'); ?></h2>
                <p><?php _e('ChatGABI integrates with trusted third-party services that may set their own cookies:', 'chatgabi'); ?></p>
                
                <h3><?php _e('3.1 OpenAI Integration', 'chatgabi'); ?></h3>
                <p><?php _e('Our AI features are powered by OpenAI, which may use cookies for:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('API request authentication and rate limiting', 'chatgabi'); ?></li>
                    <li><?php _e('Service optimization and performance monitoring', 'chatgabi'); ?></li>
                    <li><?php _e('Security and abuse prevention', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('3.2 Paystack Payment Processing', 'chatgabi'); ?></h3>
                <p><?php _e('Paystack, our payment processor, uses cookies for:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Secure payment processing and fraud prevention', 'chatgabi'); ?></li>
                    <li><?php _e('Payment method preferences and history', 'chatgabi'); ?></li>
                    <li><?php _e('Transaction security and verification', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('3.3 SendPulse Email Services', 'chatgabi'); ?></h3>
                <p><?php _e('SendPulse may use cookies for:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Email delivery optimization', 'chatgabi'); ?></li>
                    <li><?php _e('Campaign performance tracking', 'chatgabi'); ?></li>
                    <li><?php _e('Subscriber preference management', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 4: Cookie Duration -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('4. Cookie Duration', 'chatgabi'); ?></h2>
                <p><?php _e('Cookies have different lifespans depending on their purpose:', 'chatgabi'); ?></p>
                
                <h3><?php _e('4.1 Session Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies are deleted when you close your browser and are used for:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('User authentication and session management', 'chatgabi'); ?></li>
                    <li><?php _e('Shopping cart and temporary data storage', 'chatgabi'); ?></li>
                    <li><?php _e('Security tokens and CSRF protection', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('4.2 Persistent Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('These cookies remain on your device for a specified period:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('User preferences: 1 year', 'chatgabi'); ?></li>
                    <li><?php _e('Language settings: 6 months', 'chatgabi'); ?></li>
                    <li><?php _e('Analytics data: 2 years', 'chatgabi'); ?></li>
                    <li><?php _e('Marketing preferences: 1 year', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 5: Managing Your Cookie Preferences -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('5. Managing Your Cookie Preferences', 'chatgabi'); ?></h2>
                
                <h3><?php _e('5.1 Browser Settings', 'chatgabi'); ?></h3>
                <p><?php _e('You can control cookies through your browser settings:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Block all cookies (may affect website functionality)', 'chatgabi'); ?></li>
                    <li><?php _e('Block third-party cookies only', 'chatgabi'); ?></li>
                    <li><?php _e('Delete existing cookies', 'chatgabi'); ?></li>
                    <li><?php _e('Set preferences for specific websites', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('5.2 ChatGABI Cookie Controls', 'chatgabi'); ?></h3>
                <p><?php _e('We provide controls for managing your cookie preferences:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Cookie consent banner on first visit', 'chatgabi'); ?></li>
                    <li><?php _e('Preference center in your account settings', 'chatgabi'); ?></li>
                    <li><?php _e('Granular control over cookie categories', 'chatgabi'); ?></li>
                    <li><?php _e('Easy opt-out options for non-essential cookies', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('5.3 Impact of Disabling Cookies', 'chatgabi'); ?></h3>
                <p><?php _e('Disabling certain cookies may affect your ChatGABI experience:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Essential cookies: Website may not function properly', 'chatgabi'); ?></li>
                    <li><?php _e('Functional cookies: Loss of personalization and preferences', 'chatgabi'); ?></li>
                    <li><?php _e('Analytics cookies: No impact on functionality', 'chatgabi'); ?></li>
                    <li><?php _e('Marketing cookies: Less relevant content and communications', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 6: African Market Considerations -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('6. African Market Considerations', 'chatgabi'); ?></h2>
                
                <h3><?php _e('6.1 Data Localization', 'chatgabi'); ?></h3>
                <p><?php _e('We consider data localization requirements in our target markets:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Ghana: Compliance with Data Protection Act requirements', 'chatgabi'); ?></li>
                    <li><?php _e('Kenya: Adherence to Data Protection Act guidelines', 'chatgabi'); ?></li>
                    <li><?php _e('Nigeria: Compliance with NDPR data residency considerations', 'chatgabi'); ?></li>
                    <li><?php _e('South Africa: POPIA compliance for cross-border data transfers', 'chatgabi'); ?></li>
                </ul>

                <h3><?php _e('6.2 Mobile-First Approach', 'chatgabi'); ?></h3>
                <p><?php _e('Recognizing mobile internet usage patterns in Africa, our cookie implementation:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Minimizes data usage and storage requirements', 'chatgabi'); ?></li>
                    <li><?php _e('Optimizes for slower internet connections', 'chatgabi'); ?></li>
                    <li><?php _e('Provides clear mobile cookie consent interfaces', 'chatgabi'); ?></li>
                    <li><?php _e('Respects data cost considerations', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 7: Cookie Security -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('7. Cookie Security', 'chatgabi'); ?></h2>
                <p><?php _e('We implement security measures to protect cookie data:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Secure flag for HTTPS-only transmission', 'chatgabi'); ?></li>
                    <li><?php _e('HttpOnly flag to prevent JavaScript access', 'chatgabi'); ?></li>
                    <li><?php _e('SameSite attribute for CSRF protection', 'chatgabi'); ?></li>
                    <li><?php _e('Regular security audits and updates', 'chatgabi'); ?></li>
                    <li><?php _e('Encryption of sensitive cookie data', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 8: Updates to This Policy -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('8. Updates to This Policy', 'chatgabi'); ?></h2>
                <p><?php _e('We may update this Cookie Policy to reflect changes in our practices or legal requirements. Updates will be posted on this page with a revised "Last Updated" date.', 'chatgabi'); ?></p>
                <p><?php _e('Significant changes will be communicated through our website and email notifications to registered users.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 9: Contact Information -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('9. Contact Information', 'chatgabi'); ?></h2>
                <p><?php _e('If you have questions about our use of cookies or this policy, please contact us:', 'chatgabi'); ?></p>
                <div class="contact-info">
                    <p><strong><?php _e('Swiftmind', 'chatgabi'); ?></strong></p>
                    <p><?php _e('Email: <EMAIL>', 'chatgabi'); ?></p>
                    <p><?php _e('Address: Accra, Ghana', 'chatgabi'); ?></p>
                    <p><?php _e('Product: ChatGABI - AI Business Intelligence Platform', 'chatgabi'); ?></p>
                    <p><?php _e('Subject Line: Cookie Policy Inquiry', 'chatgabi'); ?></p>
                </div>
            </section>
        </div>
    </div>
</div>

<style>
/* Cookie Policy Specific Styles */
.cookie-policy-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.legal-page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-icon {
    font-size: 2.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.last-updated {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
    padding: 10px 20px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 8px;
    display: inline-block;
}

.legal-content {
    max-width: 900px;
    margin: 0 auto;
}

.legal-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 25px;
}

.legal-section h2 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--color-secondary-accent);
    padding-bottom: 10px;
}

.legal-section h3 {
    color: var(--color-tertiary-accent);
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
}

.legal-section p {
    line-height: 1.7;
    margin-bottom: 15px;
    color: var(--color-text-primary);
}

.legal-section ul {
    margin: 15px 0;
    padding-left: 25px;
}

.legal-section li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: var(--color-text-primary);
}

.contact-info {
    background: rgba(var(--color-nature-green), 0.1);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.contact-info p {
    margin-bottom: 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .cookie-policy-page {
        padding: 20px 0;
    }
    
    .legal-page-header {
        padding: 25px 20px;
    }
    
    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .legal-section {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .legal-section h2 {
        font-size: 1.5rem;
    }
    
    .legal-section h3 {
        font-size: 1.2rem;
    }
}

/* Dark Theme Support */
body.theme-dark .legal-page-header,
body.theme-dark .legal-section {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .page-title {
    color: var(--color-secondary-accent);
}

body.theme-dark .legal-section h2 {
    color: var(--color-secondary-accent);
    border-bottom-color: var(--color-primary-accent);
}

body.theme-dark .legal-section h3 {
    color: var(--color-tertiary-accent);
}

body.theme-dark .contact-info {
    background: rgba(39, 174, 96, 0.2);
}
</style>

<?php
// Add SEO meta tags
function chatgabi_cookie_policy_meta() {
    $title = __('Cookie Policy - ChatGABI | Swiftmind', 'chatgabi');
    $description = __('Learn how ChatGABI uses cookies to enhance your AI business intelligence experience. Comprehensive cookie policy with controls for managing your preferences and privacy.', 'chatgabi');
    
    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
}
add_action('wp_head', 'chatgabi_cookie_policy_meta');

get_footer();
?>
