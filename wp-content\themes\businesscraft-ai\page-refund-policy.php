<?php
/**
 * Template Name: Refund Policy
 * 
 * Refund Policy page for ChatGABI
 * Clear statement: No refunds for credit purchases
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="chatgabi-legal-page refund-policy-page">
    <div class="container">
        <!-- Page Header -->
        <div class="legal-page-header glassmorphism-card">
            <div class="header-content">
                <h1 class="page-title">
                    <span class="page-icon">💳</span>
                    <?php _e('Refund Policy', 'chatgabi'); ?>
                </h1>
                <p class="page-subtitle">
                    <?php _e('This policy explains our refund terms for ChatGABI credit purchases. Please read carefully before making any purchases.', 'chatgabi'); ?>
                </p>
                <div class="last-updated">
                    <strong><?php _e('Last Updated:', 'chatgabi'); ?></strong> <?php echo date('F j, Y'); ?>
                </div>
            </div>
        </div>

        <!-- Refund Policy Content -->
        <div class="legal-content">
            <!-- Section 1: No Refund Policy Statement -->
            <section class="legal-section glassmorphism-card no-refund-highlight">
                <h2><?php _e('1. No Refund Policy', 'chatgabi'); ?></h2>
                <div class="important-notice">
                    <h3><?php _e('⚠️ IMPORTANT: NO REFUNDS PROVIDED', 'chatgabi'); ?></h3>
                    <p><strong><?php _e('All credit purchases on ChatGABI are final and non-refundable. Once you purchase credits, they cannot be refunded under any circumstances.', 'chatgabi'); ?></strong></p>
                </div>
                <p><?php _e('This policy applies to all credit packages purchased through our platform, regardless of the payment method used or the amount purchased.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 2: Free Credit Testing System -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('2. Free Credit Testing System', 'chatgabi'); ?></h2>
                <p><?php _e('We understand the importance of testing our service before making a purchase. Therefore, we provide:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Free credits for new users to test ChatGABI features', 'chatgabi'); ?></li>
                    <li><?php _e('Full access to AI-powered business intelligence tools during the trial', 'chatgabi'); ?></li>
                    <li><?php _e('Opportunity to experience our template generation and market analysis features', 'chatgabi'); ?></li>
                    <li><?php _e('Complete evaluation of our multi-language support and African market focus', 'chatgabi'); ?></li>
                </ul>
                <p><?php _e('We strongly encourage all users to fully utilize their free credits before making any purchases to ensure ChatGABI meets their business needs.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 3: Rationale for No Refund Policy -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('3. Rationale for No Refund Policy', 'chatgabi'); ?></h2>
                <p><?php _e('Our no-refund policy exists for the following reasons:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Digital credits are consumed immediately upon use for AI processing', 'chatgabi'); ?></li>
                    <li><?php _e('AI computations through OpenAI incur real costs that cannot be recovered', 'chatgabi'); ?></li>
                    <li><?php _e('Free testing credits allow full evaluation before purchase', 'chatgabi'); ?></li>
                    <li><?php _e('Clear pricing and feature descriptions are provided before purchase', 'chatgabi'); ?></li>
                    <li><?php _e('Credits do not expire, providing long-term value', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 4: African Market Legal Compliance -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('4. African Market Legal Compliance', 'chatgabi'); ?></h2>
                
                <h3><?php _e('4.1 Ghana Consumer Protection', 'chatgabi'); ?></h3>
                <p><?php _e('Under Ghana\'s Consumer Protection Act, digital services with clear terms and free trial periods may operate with no-refund policies when consumers are adequately informed before purchase.', 'chatgabi'); ?></p>

                <h3><?php _e('4.2 Kenya Consumer Rights', 'chatgabi'); ?></h3>
                <p><?php _e('Kenya\'s Consumer Protection Act recognizes that digital credits consumed upon use may not be eligible for refunds when clear terms are provided and free testing is available.', 'chatgabi'); ?></p>

                <h3><?php _e('4.3 Nigeria Consumer Protection', 'chatgabi'); ?></h3>
                <p><?php _e('Nigeria\'s Federal Competition and Consumer Protection Act allows no-refund policies for digital services when consumers are clearly informed and have testing opportunities.', 'chatgabi'); ?></p>

                <h3><?php _e('4.4 South Africa Consumer Rights', 'chatgabi'); ?></h3>
                <p><?php _e('Under South Africa\'s Consumer Protection Act, digital services may have no-refund policies when terms are clearly disclosed and consumers have adequate opportunity to evaluate the service.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 5: Exceptional Circumstances -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('5. Exceptional Circumstances', 'chatgabi'); ?></h2>
                <p><?php _e('While our general policy is no refunds, we may consider exceptions in the following rare circumstances:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Technical errors resulting in duplicate charges (credit adjustment only)', 'chatgabi'); ?></li>
                    <li><?php _e('Unauthorized transactions reported within 24 hours', 'chatgabi'); ?></li>
                    <li><?php _e('System failures preventing credit usage for extended periods', 'chatgabi'); ?></li>
                    <li><?php _e('Billing errors by our payment processor', 'chatgabi'); ?></li>
                </ul>
                <p><?php _e('Any exceptions are at our sole discretion and require thorough investigation. Contact us immediately if you believe an exceptional circumstance applies.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 6: Credit Management -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('6. Credit Management', 'chatgabi'); ?></h2>
                
                <h3><?php _e('6.1 Credit Expiration', 'chatgabi'); ?></h3>
                <p><?php _e('Purchased credits do not expire and remain in your account until used. This provides long-term value for your investment.', 'chatgabi'); ?></p>

                <h3><?php _e('6.2 Credit Transfer', 'chatgabi'); ?></h3>
                <p><?php _e('Credits are non-transferable between accounts and cannot be converted to cash or other forms of value.', 'chatgabi'); ?></p>

                <h3><?php _e('6.3 Account Closure', 'chatgabi'); ?></h3>
                <p><?php _e('If you close your account, any remaining credits will be forfeited and cannot be refunded or transferred.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 7: Payment Processing -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('7. Payment Processing', 'chatgabi'); ?></h2>
                <p><?php _e('All payments are processed securely through Paystack, our trusted payment partner. Payment-related issues should be addressed as follows:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Payment failures: Contact Paystack customer support', 'chatgabi'); ?></li>
                    <li><?php _e('Billing disputes: Contact your bank or card issuer', 'chatgabi'); ?></li>
                    <li><?php _e('Technical payment issues: Contact ChatGABI support', 'chatgabi'); ?></li>
                    <li><?php _e('Unauthorized charges: Report immediately to both ChatGABI and your bank', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 8: Alternative Solutions -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('8. Alternative Solutions', 'chatgabi'); ?></h2>
                <p><?php _e('Instead of refunds, we offer the following alternatives to address concerns:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Technical support to resolve usage issues', 'chatgabi'); ?></li>
                    <li><?php _e('Training and guidance on maximizing credit value', 'chatgabi'); ?></li>
                    <li><?php _e('Feature explanations and optimization tips', 'chatgabi'); ?></li>
                    <li><?php _e('Account management assistance', 'chatgabi'); ?></li>
                    <li><?php _e('Business consultation to improve AI prompt effectiveness', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 9: Dispute Resolution Process -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('9. Dispute Resolution Process', 'chatgabi'); ?></h2>
                <p><?php _e('If you have concerns about your credit purchase, please follow this process:', 'chatgabi'); ?></p>
                <ol>
                    <li><?php _e('Contact our support <NAME_EMAIL> with detailed information', 'chatgabi'); ?></li>
                    <li><?php _e('Provide transaction details, account information, and specific concerns', 'chatgabi'); ?></li>
                    <li><?php _e('Allow 48-72 hours for our team to investigate and respond', 'chatgabi'); ?></li>
                    <li><?php _e('If unsatisfied, escalate to management for final review', 'chatgabi'); ?></li>
                    <li><?php _e('For legal disputes, follow the process outlined in our Terms of Service', 'chatgabi'); ?></li>
                </ol>
            </section>

            <!-- Section 10: Fraud Prevention -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('10. Fraud Prevention', 'chatgabi'); ?></h2>
                <p><?php _e('To protect all users, we implement fraud prevention measures:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Transaction monitoring and verification', 'chatgabi'); ?></li>
                    <li><?php _e('Account security checks for unusual activity', 'chatgabi'); ?></li>
                    <li><?php _e('Payment verification through Paystack security systems', 'chatgabi'); ?></li>
                    <li><?php _e('Cooperation with financial institutions on fraud investigations', 'chatgabi'); ?></li>
                </ul>
                <p><?php _e('Fraudulent transactions will be investigated and may result in account suspension and legal action.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 11: Changes to This Policy -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('11. Changes to This Policy', 'chatgabi'); ?></h2>
                <p><?php _e('We may update this Refund Policy from time to time. Any changes will be posted on this page with an updated "Last Updated" date. Continued use of ChatGABI after changes constitutes acceptance of the updated policy.', 'chatgabi'); ?></p>
                <p><?php _e('Significant changes affecting user rights will be communicated via email to registered users.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 12: Contact Information -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('12. Contact Information', 'chatgabi'); ?></h2>
                <p><?php _e('For questions about this Refund Policy or to report payment issues, contact us:', 'chatgabi'); ?></p>
                <div class="contact-info">
                    <p><strong><?php _e('Swiftmind', 'chatgabi'); ?></strong></p>
                    <p><?php _e('Email: <EMAIL>', 'chatgabi'); ?></p>
                    <p><?php _e('Address: Accra, Ghana', 'chatgabi'); ?></p>
                    <p><?php _e('Product: ChatGABI - AI Business Intelligence Platform', 'chatgabi'); ?></p>
                    <p><?php _e('Response Time: 24-48 hours for payment-related inquiries', 'chatgabi'); ?></p>
                </div>
            </section>
        </div>
    </div>
</div>

<style>
/* Refund Policy Specific Styles */
.refund-policy-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.legal-page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-icon {
    font-size: 2.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.last-updated {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
    padding: 10px 20px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 8px;
    display: inline-block;
}

.legal-content {
    max-width: 900px;
    margin: 0 auto;
}

.legal-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 25px;
}

/* Special styling for no-refund highlight */
.no-refund-highlight {
    border: 2px solid #e74c3c;
    background: rgba(231, 76, 60, 0.05);
}

.important-notice {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid #e74c3c;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.important-notice h3 {
    color: #e74c3c;
    font-size: 1.4rem;
    margin-bottom: 15px;
    font-weight: bold;
}

.important-notice p {
    color: #c0392b;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.legal-section h2 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--color-secondary-accent);
    padding-bottom: 10px;
}

.legal-section h3 {
    color: var(--color-tertiary-accent);
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
}

.legal-section p {
    line-height: 1.7;
    margin-bottom: 15px;
    color: var(--color-text-primary);
}

.legal-section ul,
.legal-section ol {
    margin: 15px 0;
    padding-left: 25px;
}

.legal-section li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: var(--color-text-primary);
}

.contact-info {
    background: rgba(var(--color-nature-green), 0.1);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.contact-info p {
    margin-bottom: 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .refund-policy-page {
        padding: 20px 0;
    }
    
    .legal-page-header {
        padding: 25px 20px;
    }
    
    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .legal-section {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .legal-section h2 {
        font-size: 1.5rem;
    }
    
    .legal-section h3 {
        font-size: 1.2rem;
    }
    
    .important-notice {
        padding: 15px;
    }
    
    .important-notice h3 {
        font-size: 1.2rem;
    }
    
    .important-notice p {
        font-size: 1rem;
    }
}

/* Dark Theme Support */
body.theme-dark .legal-page-header,
body.theme-dark .legal-section {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .no-refund-highlight {
    background: rgba(231, 76, 60, 0.1);
    border-color: #e74c3c;
}

body.theme-dark .important-notice {
    background: rgba(231, 76, 60, 0.15);
}

body.theme-dark .page-title {
    color: var(--color-secondary-accent);
}

body.theme-dark .legal-section h2 {
    color: var(--color-secondary-accent);
    border-bottom-color: var(--color-primary-accent);
}

body.theme-dark .legal-section h3 {
    color: var(--color-tertiary-accent);
}

body.theme-dark .contact-info {
    background: rgba(39, 174, 96, 0.2);
}
</style>

<?php
// Add SEO meta tags
function chatgabi_refund_policy_meta() {
    $title = __('Refund Policy - ChatGABI | Swiftmind', 'chatgabi');
    $description = __('ChatGABI Refund Policy: All credit purchases are final and non-refundable. Free credits available for testing. Learn about our no-refund policy and African market compliance.', 'chatgabi');
    
    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
}
add_action('wp_head', 'chatgabi_refund_policy_meta');

get_footer();
?>
