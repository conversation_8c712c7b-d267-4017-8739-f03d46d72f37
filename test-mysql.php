<?php
/**
 * MySQL Connection Test for ChatGABI
 */

header('Content-Type: text/plain');

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'chatgabi_test';

try {
    // Test MySQL connection
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ MySQL Connection: Success\n";
    echo "📊 MySQL Version: " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "\n";
    
    // Test if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'chatgabi_test'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Database 'chatgabi_test': Exists\n";
        
        // Connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "📋 Tables found: " . count($tables) . "\n";
        foreach ($tables as $table) {
            echo "   - $table\n";
        }
        
        // Test sample data
        if (in_array('wp_chatgabi_conversions', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM wp_chatgabi_conversions");
            $count = $stmt->fetchColumn();
            echo "📈 Sample conversions: $count records\n";
        }
        
    } else {
        echo "⚠️ Database 'chatgabi_test': Not found\n";
        echo "💡 Please import chatgabi-test-setup.sql\n";
    }
    
} catch (PDOException $e) {
    echo "❌ MySQL Connection: Failed\n";
    echo "🔍 Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure MySQL service is running in XAMPP\n";
}

echo "\n🔧 XAMPP Status Check:\n";
echo "📍 Expected location: C:\\xampp\\\n";
echo "🌐 Web server: http://localhost/\n";
echo "🗄️ phpMyAdmin: http://localhost/phpmyadmin/\n";
?>
