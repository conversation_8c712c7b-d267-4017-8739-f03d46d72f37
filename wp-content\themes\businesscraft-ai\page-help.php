<?php
/**
 * Template Name: Help Center
 * 
 * Help Center and User Guide for ChatGABI
 * Comprehensive support resources and tutorials
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user data for personalization
$current_user = wp_get_current_user();
$user_credits = 0;
$user_country = 'Ghana';

if ($current_user->ID) {
    $user_credits = get_user_meta($current_user->ID, 'businesscraft_credits', true) ?: 0;
    $user_country = get_user_meta($current_user->ID, 'country', true) ?: 'Ghana';
}

// Handle search functionality
$search_query = '';
$search_results = array();

if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_query = sanitize_text_field($_GET['search']);
    
    // Simulate search results (in production, this would search a knowledge base)
    $knowledge_base = array(
        array(
            'title' => 'How to use AI Chat Intelligence',
            'content' => 'Learn how to interact with ChatGABI\'s AI for business insights and advice.',
            'category' => 'Getting Started',
            'tags' => array('ai', 'chat', 'intelligence', 'business', 'advice')
        ),
        array(
            'title' => 'Creating Business Templates',
            'content' => 'Step-by-step guide to generating professional business documents.',
            'category' => 'Templates',
            'tags' => array('templates', 'documents', 'business', 'generation')
        ),
        array(
            'title' => 'Market Analysis Features',
            'content' => 'Discover market opportunities and analyze competition in your sector.',
            'category' => 'Analysis',
            'tags' => array('market', 'analysis', 'competition', 'opportunities')
        ),
        array(
            'title' => 'Managing Your Credits',
            'content' => 'Understanding the credit system and how to purchase more credits.',
            'category' => 'Account',
            'tags' => array('credits', 'billing', 'purchase', 'account')
        ),
        array(
            'title' => 'Multi-language Support',
            'content' => 'Using ChatGABI in Twi, Swahili, Yoruba, Zulu, and English.',
            'category' => 'Features',
            'tags' => array('language', 'multilingual', 'twi', 'swahili', 'yoruba', 'zulu')
        )
    );
    
    foreach ($knowledge_base as $article) {
        $search_terms = explode(' ', strtolower($search_query));
        $match_score = 0;
        
        foreach ($search_terms as $term) {
            if (strpos(strtolower($article['title']), $term) !== false) {
                $match_score += 3;
            }
            if (strpos(strtolower($article['content']), $term) !== false) {
                $match_score += 2;
            }
            if (in_array($term, array_map('strtolower', $article['tags']))) {
                $match_score += 1;
            }
        }
        
        if ($match_score > 0) {
            $article['score'] = $match_score;
            $search_results[] = $article;
        }
    }
    
    // Sort by relevance
    usort($search_results, function($a, $b) {
        return $b['score'] - $a['score'];
    });
}

get_header();
?>

<div class="chatgabi-help-page">
    <div class="container">
        <!-- Help Hero Section -->
        <div class="help-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🆘</span>
                    <?php _e('ChatGABI Help Center', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Get the most out of your AI business intelligence platform with our comprehensive guides, tutorials, and support resources.', 'chatgabi'); ?>
                </p>
                
                <!-- Search Bar -->
                <div class="help-search">
                    <form method="get" class="search-form">
                        <div class="search-input-group">
                            <input type="text" 
                                   name="search" 
                                   value="<?php echo esc_attr($search_query); ?>"
                                   placeholder="<?php esc_attr_e('Search for help articles, guides, or features...', 'chatgabi'); ?>"
                                   class="search-input">
                            <button type="submit" class="search-button">
                                <span class="search-icon">🔍</span>
                                <?php _e('Search', 'chatgabi'); ?>
                            </button>
                        </div>
                    </form>
                    
                    <div class="popular-searches">
                        <span class="popular-label"><?php _e('Popular:', 'chatgabi'); ?></span>
                        <a href="?search=credits" class="popular-tag"><?php _e('Credits', 'chatgabi'); ?></a>
                        <a href="?search=templates" class="popular-tag"><?php _e('Templates', 'chatgabi'); ?></a>
                        <a href="?search=ai+chat" class="popular-tag"><?php _e('AI Chat', 'chatgabi'); ?></a>
                        <a href="?search=market+analysis" class="popular-tag"><?php _e('Market Analysis', 'chatgabi'); ?></a>
                    </div>
                </div>
                
                <?php if ($current_user->ID): ?>
                    <div class="user-help-context">
                        <div class="context-item">
                            <span class="context-icon">💳</span>
                            <span><?php printf(__('You have %s credits available', 'chatgabi'), '<strong>' . number_format($user_credits) . '</strong>'); ?></span>
                        </div>
                        <div class="context-item">
                            <span class="context-icon">🌍</span>
                            <span><?php printf(__('Account region: %s', 'chatgabi'), '<strong>' . $user_country . '</strong>'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (!empty($search_results)): ?>
            <!-- Search Results -->
            <div class="search-results-section glassmorphism-card">
                <div class="section-header">
                    <h2><?php printf(__('Search Results for "%s"', 'chatgabi'), esc_html($search_query)); ?></h2>
                    <p><?php printf(_n('%d result found', '%d results found', count($search_results), 'chatgabi'), count($search_results)); ?></p>
                </div>
                
                <div class="search-results">
                    <?php foreach ($search_results as $result): ?>
                        <div class="search-result-item">
                            <div class="result-header">
                                <h3><?php echo esc_html($result['title']); ?></h3>
                                <span class="result-category"><?php echo esc_html($result['category']); ?></span>
                            </div>
                            <p><?php echo esc_html($result['content']); ?></p>
                            <div class="result-tags">
                                <?php foreach ($result['tags'] as $tag): ?>
                                    <span class="tag"><?php echo esc_html($tag); ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php elseif (!empty($search_query)): ?>
            <!-- No Results -->
            <div class="no-results-section glassmorphism-card">
                <div class="no-results-content">
                    <div class="no-results-icon">🔍</div>
                    <h2><?php _e('No Results Found', 'chatgabi'); ?></h2>
                    <p><?php printf(__('We couldn\'t find any help articles matching "%s". Try different keywords or browse our categories below.', 'chatgabi'), esc_html($search_query)); ?></p>
                    <a href="<?php echo esc_url(remove_query_arg('search')); ?>" class="btn btn-primary">
                        <span class="btn-icon">📚</span>
                        <?php _e('Browse All Categories', 'chatgabi'); ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Quick Start Guide -->
        <div class="quick-start-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Quick Start Guide', 'chatgabi'); ?></h2>
                <p><?php _e('New to ChatGABI? Follow these steps to get started with AI business intelligence', 'chatgabi'); ?></p>
            </div>
            
            <div class="quick-start-steps">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3><?php _e('Set Up Your Profile', 'chatgabi'); ?></h3>
                        <p><?php _e('Complete your business information to get personalized AI recommendations.', 'chatgabi'); ?></p>
                        <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="step-link"><?php _e('Go to Dashboard', 'chatgabi'); ?></a>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3><?php _e('Try AI Chat Intelligence', 'chatgabi'); ?></h3>
                        <p><?php _e('Ask questions about your business, market, or industry to get AI-powered insights.', 'chatgabi'); ?></p>
                        <a href="#ai-chat-guide" class="step-link"><?php _e('Learn More', 'chatgabi'); ?></a>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3><?php _e('Generate Business Documents', 'chatgabi'); ?></h3>
                        <p><?php _e('Create professional business plans, proposals, and reports with AI assistance.', 'chatgabi'); ?></p>
                        <a href="#templates-guide" class="step-link"><?php _e('Explore Templates', 'chatgabi'); ?></a>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3><?php _e('Analyze Your Market', 'chatgabi'); ?></h3>
                        <p><?php _e('Discover opportunities, analyze competition, and understand market trends.', 'chatgabi'); ?></p>
                        <a href="#market-analysis-guide" class="step-link"><?php _e('Start Analysis', 'chatgabi'); ?></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Categories -->
        <div class="help-categories-section">
            <div class="section-header">
                <h2><?php _e('Browse by Category', 'chatgabi'); ?></h2>
                <p><?php _e('Find detailed guides and tutorials organized by topic', 'chatgabi'); ?></p>
            </div>
            
            <div class="categories-grid">
                <!-- Getting Started -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">🚀</div>
                        <h3><?php _e('Getting Started', 'chatgabi'); ?></h3>
                        <span class="article-count">8 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Everything you need to know to start using ChatGABI effectively.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('Account Setup & Profile', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Understanding Credits', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Dashboard Overview', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('First AI Conversation', 'chatgabi'); ?></a>
                    </div>
                </div>

                <!-- AI Features -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">🤖</div>
                        <h3><?php _e('AI Features', 'chatgabi'); ?></h3>
                        <span class="article-count">12 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Master ChatGABI\'s AI-powered business intelligence tools.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('AI Chat Best Practices', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Prompt Engineering Tips', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Multi-language Support', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('African Context Intelligence', 'chatgabi'); ?></a>
                    </div>
                </div>

                <!-- Business Templates -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">📝</div>
                        <h3><?php _e('Business Templates', 'chatgabi'); ?></h3>
                        <span class="article-count">15 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Create professional documents with AI-powered templates.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('Business Plan Generator', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Financial Projections', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Marketing Strategies', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Export & Sharing Options', 'chatgabi'); ?></a>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">📊</div>
                        <h3><?php _e('Market Analysis', 'chatgabi'); ?></h3>
                        <span class="article-count">10 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Discover opportunities and analyze your business environment.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('Competitor Analysis', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Market Sizing', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Trend Identification', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('SWOT Analysis', 'chatgabi'); ?></a>
                    </div>
                </div>

                <!-- Account & Billing -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">💳</div>
                        <h3><?php _e('Account & Billing', 'chatgabi'); ?></h3>
                        <span class="article-count">6 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Manage your account, credits, and billing information.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('Purchasing Credits', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Payment Methods', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Usage Analytics', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Account Security', 'chatgabi'); ?></a>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="category-card glassmorphism-card">
                    <div class="category-header">
                        <div class="category-icon">🔧</div>
                        <h3><?php _e('Troubleshooting', 'chatgabi'); ?></h3>
                        <span class="article-count">9 <?php _e('articles', 'chatgabi'); ?></span>
                    </div>
                    <p><?php _e('Solve common issues and get technical support.', 'chatgabi'); ?></p>
                    <div class="category-articles">
                        <a href="#" class="article-link"><?php _e('Common Error Messages', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Browser Compatibility', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Performance Issues', 'chatgabi'); ?></a>
                        <a href="#" class="article-link"><?php _e('Contact Support', 'chatgabi'); ?></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Tutorials -->
        <div class="video-tutorials-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Video Tutorials', 'chatgabi'); ?></h2>
                <p><?php _e('Watch step-by-step video guides to master ChatGABI features', 'chatgabi'); ?></p>
            </div>

            <div class="videos-grid">
                <div class="video-card">
                    <div class="video-thumbnail">
                        <div class="video-placeholder">
                            <span class="play-icon">▶️</span>
                            <span class="video-duration">3:45</span>
                        </div>
                    </div>
                    <div class="video-content">
                        <h4><?php _e('Getting Started with ChatGABI', 'chatgabi'); ?></h4>
                        <p><?php _e('Complete walkthrough of your first ChatGABI session', 'chatgabi'); ?></p>
                        <div class="video-meta">
                            <span class="video-views">2.1K <?php _e('views', 'chatgabi'); ?></span>
                            <span class="video-date"><?php _e('Dec 2024', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <div class="video-placeholder">
                            <span class="play-icon">▶️</span>
                            <span class="video-duration">5:20</span>
                        </div>
                    </div>
                    <div class="video-content">
                        <h4><?php _e('Creating Business Plans with AI', 'chatgabi'); ?></h4>
                        <p><?php _e('Generate comprehensive business plans using AI templates', 'chatgabi'); ?></p>
                        <div class="video-meta">
                            <span class="video-views">1.8K <?php _e('views', 'chatgabi'); ?></span>
                            <span class="video-date"><?php _e('Nov 2024', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <div class="video-placeholder">
                            <span class="play-icon">▶️</span>
                            <span class="video-duration">4:15</span>
                        </div>
                    </div>
                    <div class="video-content">
                        <h4><?php _e('Market Analysis for African Businesses', 'chatgabi'); ?></h4>
                        <p><?php _e('Discover market opportunities in Ghana, Kenya, Nigeria, and South Africa', 'chatgabi'); ?></p>
                        <div class="video-meta">
                            <span class="video-views">3.2K <?php _e('views', 'chatgabi'); ?></span>
                            <span class="video-date"><?php _e('Nov 2024', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <div class="video-placeholder">
                            <span class="play-icon">▶️</span>
                            <span class="video-duration">2:30</span>
                        </div>
                    </div>
                    <div class="video-content">
                        <h4><?php _e('Multi-language Features Demo', 'chatgabi'); ?></h4>
                        <p><?php _e('Using ChatGABI in Twi, Swahili, Yoruba, and Zulu', 'chatgabi'); ?></p>
                        <div class="video-meta">
                            <span class="video-views">1.5K <?php _e('views', 'chatgabi'); ?></span>
                            <span class="video-date"><?php _e('Oct 2024', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Frequently Asked Questions -->
        <div class="faq-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Frequently Asked Questions', 'chatgabi'); ?></h2>
                <p><?php _e('Quick answers to the most common questions about ChatGABI', 'chatgabi'); ?></p>
            </div>

            <div class="faq-categories">
                <div class="faq-category">
                    <h3><?php _e('General Questions', 'chatgabi'); ?></h3>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('What is ChatGABI and how does it work?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('ChatGABI is an AI-powered business intelligence platform designed specifically for African entrepreneurs. It uses advanced artificial intelligence to provide business insights, generate documents, analyze markets, and offer strategic advice tailored to African business contexts and regulations.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('Which African countries does ChatGABI support?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('ChatGABI currently focuses on Ghana, Kenya, Nigeria, and South Africa, with specialized knowledge of business regulations, market conditions, and cultural contexts in these countries. We\'re expanding to other African markets based on user demand.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('What languages does ChatGABI support?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('ChatGABI supports English, Twi (Ghana), Swahili (Kenya/Tanzania), Yoruba (Nigeria), and Zulu (South Africa). The AI understands cultural nuances and business terminology in each language.', 'chatgabi'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="faq-category">
                    <h3><?php _e('Credits & Billing', 'chatgabi'); ?></h3>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('How does the credit system work?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('ChatGABI uses a credit-based system where different features consume different amounts of credits: AI Chat (1 credit), Template Enhancement (2 credits), Market Analysis (3 credits), Document Wizards (5 credits). Credits never expire and you only pay for what you use.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('What payment methods do you accept?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('We accept major credit cards (Visa, Mastercard, Verve), mobile money (MTN, Vodafone, AirtelTigo), bank transfers, and digital wallets through our secure Paystack integration. All payments are processed in local currencies.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('Do I get free credits when I sign up?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('Yes! New users receive 25 free credits upon registration to explore all ChatGABI features. No credit card required for the free trial.', 'chatgabi'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="faq-category">
                    <h3><?php _e('Features & Usage', 'chatgabi'); ?></h3>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('Can I export the documents ChatGABI creates?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('Yes, all generated documents can be exported to PDF, DOCX, and other popular formats. You can also share documents directly via email or download them for offline use.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('How accurate is the market analysis?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('Our market analysis is based on real-time data from African markets, government sources, and industry reports. While we strive for accuracy, we recommend using our insights as a starting point and conducting additional research for critical business decisions.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h4><?php _e('Is my business data secure?', 'chatgabi'); ?></h4>
                            <span class="faq-toggle">+</span>
                        </div>
                        <div class="faq-answer">
                            <p><?php _e('Absolutely. We use enterprise-grade security with end-to-end encryption and comply with African data protection regulations including POPIA (South Africa) and NDPR (Nigeria). Your business information is never shared with third parties.', 'chatgabi'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="contact-support-section glassmorphism-card">
            <div class="support-content">
                <div class="support-header">
                    <h2><?php _e('Still Need Help?', 'chatgabi'); ?></h2>
                    <p><?php _e('Our support team is here to help you succeed with ChatGABI', 'chatgabi'); ?></p>
                </div>

                <div class="support-options">
                    <div class="support-option">
                        <div class="support-icon">💬</div>
                        <div class="support-details">
                            <h4><?php _e('Live Chat Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Get instant help from our support team', 'chatgabi'); ?></p>
                            <span class="support-availability"><?php _e('Available 9 AM - 6 PM WAT', 'chatgabi'); ?></span>
                        </div>
                        <a href="#" class="support-action btn btn-primary"><?php _e('Start Chat', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option">
                        <div class="support-icon">📧</div>
                        <div class="support-details">
                            <h4><?php _e('Email Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Send us a detailed message about your issue', 'chatgabi'); ?></p>
                            <span class="support-availability"><?php _e('Response within 24 hours', 'chatgabi'); ?></span>
                        </div>
                        <a href="mailto:<EMAIL>" class="support-action btn btn-secondary"><?php _e('Send Email', 'chatgabi'); ?></a>
                    </div>

                    <div class="support-option">
                        <div class="support-icon">📞</div>
                        <div class="support-details">
                            <h4><?php _e('Phone Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Speak directly with our technical team', 'chatgabi'); ?></p>
                            <span class="support-availability"><?php _e('Enterprise customers only', 'chatgabi'); ?></span>
                        </div>
                        <a href="<?php echo esc_url(home_url('/contact')); ?>" class="support-action btn btn-secondary"><?php _e('Schedule Call', 'chatgabi'); ?></a>
                    </div>
                </div>

                <div class="support-stats">
                    <div class="stat-item">
                        <span class="stat-number">< 2 min</span>
                        <span class="stat-label"><?php _e('Average response time', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">98%</span>
                        <span class="stat-label"><?php _e('Customer satisfaction', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label"><?php _e('Knowledge base access', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Help Center Page Specific Styles */
.chatgabi-help-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.help-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Search Bar */
.help-search {
    max-width: 600px;
    margin: 0 auto 30px;
}

.search-input-group {
    display: flex;
    gap: 0;
    margin-bottom: 20px;
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid var(--color-borders);
    border-right: none;
    border-radius: 12px 0 0 12px;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary-accent);
}

.search-button {
    padding: 15px 25px;
    background: var(--color-primary-accent);
    color: white;
    border: 2px solid var(--color-primary-accent);
    border-radius: 0 12px 12px 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-button:hover {
    background: var(--color-secondary-accent);
    border-color: var(--color-secondary-accent);
}

.search-icon {
    margin-right: 8px;
}

.popular-searches {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.popular-label {
    color: var(--color-text-secondary);
    font-weight: 600;
}

.popular-tag {
    padding: 5px 12px;
    background: rgba(var(--color-primary-accent), 0.1);
    color: var(--color-primary-accent);
    text-decoration: none;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.popular-tag:hover {
    background: var(--color-primary-accent);
    color: white;
}

.user-help-context {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.context-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 20px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.context-icon {
    font-size: 1.2rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Search Results */
.search-results {
    display: grid;
    gap: 20px;
}

.search-result-item {
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    gap: 15px;
}

.result-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.3rem;
    margin: 0;
}

.result-category {
    background: var(--color-secondary-accent);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    flex-shrink: 0;
}

.search-result-item p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 15px;
}

.result-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* No Results */
.no-results-content {
    text-align: center;
    padding: 60px 40px;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.no-results-content h2 {
    color: var(--color-primary-accent);
    font-size: 2rem;
    margin-bottom: 15px;
}

.no-results-content p {
    color: var(--color-text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

/* Quick Start Steps */
.quick-start-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--color-nature-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h3 {
    color: var(--color-primary-accent);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.step-content p {
    color: var(--color-text-secondary);
    line-height: 1.5;
    margin-bottom: 15px;
}

.step-link {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.step-link:hover {
    text-decoration: underline;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.category-card {
    padding: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.category-icon {
    font-size: 2.5rem;
}

.category-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.4rem;
    margin: 0;
    flex: 1;
}

.article-count {
    background: rgba(var(--color-secondary-accent), 0.1);
    color: var(--color-secondary-accent);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.category-card p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.category-articles {
    display: grid;
    gap: 8px;
}

.article-link {
    color: var(--color-text-primary);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.article-link:hover {
    background: rgba(var(--color-primary-accent), 0.1);
    color: var(--color-primary-accent);
    padding-left: 20px;
}

/* Video Tutorials */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.video-card {
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.video-card:hover {
    transform: translateY(-3px);
}

.video-thumbnail {
    position: relative;
    height: 160px;
    background: linear-gradient(135deg, var(--color-primary-accent), var(--color-secondary-accent));
}

.video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    position: relative;
}

.play-icon {
    font-size: 3rem;
    color: white;
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.video-content {
    padding: 20px;
}

.video-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.video-content p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--color-text-secondary);
}

/* FAQ Section */
.faq-categories {
    display: grid;
    gap: 40px;
}

.faq-category h3 {
    color: var(--color-primary-accent);
    font-size: 1.5rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(var(--color-primary-accent), 0.2);
}

.faq-item {
    margin-bottom: 15px;
    border: 1px solid rgba(var(--color-borders), 0.3);
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(var(--color-primary-accent), 0.05);
    cursor: pointer;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: rgba(var(--color-primary-accent), 0.1);
}

.faq-question h4 {
    color: var(--color-primary-accent);
    margin: 0;
    font-size: 1.1rem;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--color-primary-accent);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 200px;
}

.faq-answer p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Support Section */
.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.support-option {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.support-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.support-details {
    flex: 1;
}

.support-details h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.support-details p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 5px;
}

.support-availability {
    color: var(--color-secondary-accent);
    font-size: 0.8rem;
    font-weight: bold;
}

.support-action {
    flex-shrink: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--color-primary-accent);
    color: white;
}

.btn-primary:hover {
    background: var(--color-secondary-accent);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.support-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--color-secondary-accent);
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-help-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .help-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .search-input-group {
        flex-direction: column;
    }

    .search-input {
        border-radius: 12px;
        border-right: 2px solid var(--color-borders);
        margin-bottom: 10px;
    }

    .search-button {
        border-radius: 12px;
    }

    .user-help-context {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .categories-grid,
    .videos-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .quick-start-steps {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .step-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .support-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .support-option {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .support-stats {
        flex-direction: column;
        gap: 20px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .search-input {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .search-result-item,
body.theme-dark .category-card,
body.theme-dark .step-item,
body.theme-dark .video-card,
body.theme-dark .support-option {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .faq-question {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .faq-question:hover {
    background: rgba(110, 127, 243, 0.2);
}
</style>

<script>
// Help Center JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // FAQ Accordion Functionality
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');

        if (question && answer) {
            question.addEventListener('click', function() {
                const isActive = item.classList.contains('active');

                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current item
                if (isActive) {
                    item.classList.remove('active');
                } else {
                    item.classList.add('active');
                }
            });
        }
    });

    // Search functionality enhancement
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');

    if (searchInput) {
        // Auto-focus search input
        searchInput.focus();

        // Search suggestions (simulated)
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    // In production, this would fetch real suggestions
                    showSearchSuggestions(query);
                }, 300);
            } else {
                hideSearchSuggestions();
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target)) {
                hideSearchSuggestions();
            }
        });
    }

    function showSearchSuggestions(query) {
        // Remove existing suggestions
        hideSearchSuggestions();

        const suggestions = [
            'How to use AI chat',
            'Credit system explained',
            'Business templates guide',
            'Market analysis tutorial',
            'Multi-language support',
            'Account security settings'
        ];

        const filteredSuggestions = suggestions.filter(suggestion =>
            suggestion.toLowerCase().includes(query.toLowerCase())
        );

        if (filteredSuggestions.length > 0) {
            const suggestionsList = document.createElement('div');
            suggestionsList.className = 'search-suggestions';

            filteredSuggestions.slice(0, 5).forEach(suggestion => {
                const suggestionItem = document.createElement('div');
                suggestionItem.className = 'suggestion-item';
                suggestionItem.textContent = suggestion;

                suggestionItem.addEventListener('click', function() {
                    searchInput.value = suggestion;
                    hideSearchSuggestions();
                    searchForm.submit();
                });

                suggestionsList.appendChild(suggestionItem);
            });

            searchInput.parentNode.appendChild(suggestionsList);
        }
    }

    function hideSearchSuggestions() {
        const existingSuggestions = document.querySelector('.search-suggestions');
        if (existingSuggestions) {
            existingSuggestions.remove();
        }
    }

    // Video card interactions
    const videoCards = document.querySelectorAll('.video-card');

    videoCards.forEach(card => {
        card.addEventListener('click', function() {
            const videoTitle = this.querySelector('h4').textContent;

            // Simulate video modal (in production, this would open a video player)
            alert(`Opening video: ${videoTitle}\n\nThis would open a video player in a production environment.`);
        });

        card.style.cursor = 'pointer';
    });

    // Category card hover effects
    const categoryCards = document.querySelectorAll('.category-card');

    categoryCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Search result highlighting
    const searchQuery = new URLSearchParams(window.location.search).get('search');
    if (searchQuery) {
        highlightSearchTerms(searchQuery);
    }

    function highlightSearchTerms(query) {
        const searchResults = document.querySelectorAll('.search-result-item');
        const terms = query.toLowerCase().split(' ');

        searchResults.forEach(result => {
            const title = result.querySelector('h3');
            const content = result.querySelector('p');

            if (title && content) {
                highlightText(title, terms);
                highlightText(content, terms);
            }
        });
    }

    function highlightText(element, terms) {
        let html = element.innerHTML;

        terms.forEach(term => {
            if (term.length > 2) {
                const regex = new RegExp(`(${term})`, 'gi');
                html = html.replace(regex, '<mark>$1</mark>');
            }
        });

        element.innerHTML = html;
    }

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Animate elements on scroll
    document.querySelectorAll('.category-card, .video-card, .step-item, .support-option').forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        element.style.transitionDelay = `${index * 100}ms`;
        observer.observe(element);
    });

    // Popular search tags tracking
    const popularTags = document.querySelectorAll('.popular-tag');

    popularTags.forEach(tag => {
        tag.addEventListener('click', function() {
            // Track popular search clicks (in production, this would send analytics)
            console.log('Popular search clicked:', this.textContent);
        });
    });

    // Support option interactions
    const supportOptions = document.querySelectorAll('.support-option');

    supportOptions.forEach(option => {
        const actionButton = option.querySelector('.support-action');

        if (actionButton) {
            actionButton.addEventListener('click', function(e) {
                const supportType = option.querySelector('h4').textContent;

                // Track support interactions
                console.log('Support option clicked:', supportType);

                // Special handling for live chat
                if (supportType.includes('Live Chat')) {
                    e.preventDefault();

                    // Check if chat is available (simulated)
                    const isAvailable = new Date().getHours() >= 9 && new Date().getHours() < 18;

                    if (isAvailable) {
                        alert('Connecting you to live chat support...\n\nThis would open a chat widget in production.');
                    } else {
                        alert('Live chat is currently offline.\n\nOur support hours are 9 AM - 6 PM WAT.\n\nPlease try email support or check back during business hours.');
                    }
                }
            });
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Escape to close search suggestions
        if (e.key === 'Escape') {
            hideSearchSuggestions();
        }
    });

    // Add custom CSS for search suggestions and highlights
    const style = document.createElement('style');
    style.textContent = `
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid var(--color-borders);
            border-top: none;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .suggestion-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.2s ease;
            border-bottom: 1px solid rgba(var(--color-borders), 0.3);
        }

        .suggestion-item:hover {
            background: rgba(var(--color-primary-accent), 0.1);
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .search-input-group {
            position: relative;
        }

        mark {
            background: rgba(var(--color-secondary-accent), 0.3);
            color: var(--color-primary-accent);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .video-card:hover .play-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .category-card:hover .category-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .support-option:hover .support-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .step-item:hover .step-number {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        @media (max-width: 768px) {
            .search-suggestions {
                border-radius: 0 0 8px 8px;
            }

            .suggestion-item {
                padding: 10px 15px;
            }
        }
    `;
    document.head.appendChild(style);

    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate any responsive elements if needed
        }, 250);
    });
});
</script>

<?php
// Add SEO meta tags
function chatgabi_help_meta() {
    $title = __('ChatGABI Help Center - Comprehensive Support & User Guides', 'chatgabi');
    $description = __('Get help with ChatGABI AI business intelligence platform. Find tutorials, FAQs, troubleshooting guides, and support resources for African entrepreneurs.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
    echo '<meta name="robots" content="index, follow">';
}
add_action('wp_head', 'chatgabi_help_meta');

get_footer();
?>
