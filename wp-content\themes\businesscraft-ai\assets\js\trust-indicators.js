/**
 * ChatGABI Trust Indicators JavaScript
 * 
 * Handles dynamic trust indicators, live activity updates,
 * and social proof animations for homepage conversion optimization.
 */

(function($) {
    'use strict';

    const TrustIndicators = {
        
        // Configuration
        config: {
            updateInterval: 30000, // 30 seconds
            activityInterval: 15000, // 15 seconds
            animationDuration: 500
        },
        
        // Initialize trust indicators
        init: function() {
            this.bindEvents();
            this.startLiveUpdates();
            this.animateCounters();
            this.initActivityFeed();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Track trust indicator interactions
            $('.trust-indicator').on('click', function() {
                const indicatorType = $(this).find('.trust-label').text();
                window.ChatGABI.ConversionTracker.trackConversion('trust_indicator_click', {
                    indicator_type: indicatorType,
                    position: 'trust_bar'
                });
            });
            
            // Track activity feed interactions
            $('.recent-activity-feed').on('click', '.activity-item', function() {
                window.ChatGABI.ConversionTracker.trackConversion('activity_feed_click', {
                    activity_type: 'recent_signup',
                    position: 'trust_bar'
                });
            });
        },
        
        // Start live updates
        startLiveUpdates: function() {
            // Update trust data periodically
            setInterval(() => {
                this.updateTrustData();
            }, this.config.updateInterval);
            
            // Update activity feed more frequently
            setInterval(() => {
                this.updateActivityFeed();
            }, this.config.activityInterval);
            
            // Initial update
            this.updateTrustData();
        },
        
        // Update trust data via AJAX
        updateTrustData: function() {
            $.ajax({
                url: chatgabiTrust.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_trust_data',
                    nonce: chatgabiTrust.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.updateTrustDisplay(response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.log('Trust data update failed:', error);
                }
            });
        },
        
        // Update trust display elements
        updateTrustDisplay: function(data) {
            // Update user count with animation
            this.animateNumber('#user-count', data.user_count + '+');
            
            // Update live activity
            const onlineText = chatgabiTrust.strings.users_online.replace('%d', data.online_users);
            this.updateWithFade('#live-activity', onlineText);
            
            // Update recent activity
            if (data.recent_activity) {
                this.updateActivityItem(data.recent_activity);
            }
        },
        
        // Animate number changes
        animateNumber: function(selector, newValue) {
            const $element = $(selector);
            const currentValue = $element.text();
            
            if (currentValue !== newValue) {
                $element.fadeOut(this.config.animationDuration / 2, function() {
                    $(this).text(newValue).fadeIn(this.config.animationDuration / 2);
                });
            }
        },
        
        // Update element with fade effect
        updateWithFade: function(selector, newText) {
            const $element = $(selector);
            const currentText = $element.text();
            
            if (currentText !== newText) {
                $element.fadeOut(this.config.animationDuration / 2, function() {
                    $(this).text(newText).fadeIn(this.config.animationDuration / 2);
                });
            }
        },
        
        // Animate counters on page load
        animateCounters: function() {
            $('.trust-number').each(function() {
                const $this = $(this);
                const text = $this.text();
                const number = parseInt(text.replace(/[^\d]/g, ''));
                
                if (number && number > 0) {
                    $this.text('0');
                    
                    // Animate counter
                    $({ counter: 0 }).animate({ counter: number }, {
                        duration: 2000,
                        easing: 'swing',
                        step: function() {
                            const current = Math.ceil(this.counter);
                            if (text.includes('+')) {
                                $this.text(current.toLocaleString() + '+');
                            } else {
                                $this.text(current.toLocaleString());
                            }
                        },
                        complete: function() {
                            $this.text(text);
                        }
                    });
                }
            });
        },
        
        // Initialize activity feed
        initActivityFeed: function() {
            // Add entrance animation to activity items
            $('.activity-item').each(function(index) {
                $(this).css({
                    opacity: 0,
                    transform: 'translateX(-20px)'
                }).delay(index * 200).animate({
                    opacity: 1
                }, {
                    duration: 500,
                    step: function(now) {
                        $(this).css('transform', `translateX(${-20 + (20 * now)}px)`);
                    }
                });
            });
        },
        
        // Update activity feed
        updateActivityFeed: function() {
            $.ajax({
                url: chatgabiTrust.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_trust_data',
                    nonce: chatgabiTrust.nonce
                },
                success: (response) => {
                    if (response.success && response.data.recent_activity) {
                        this.addActivityItem(response.data.recent_activity);
                    }
                }
            });
        },
        
        // Add new activity item
        addActivityItem: function(activity) {
            const $feed = $('#recent-activity');
            const $newItem = $(`
                <div class="activity-item new-activity">
                    <span class="activity-text">${activity.text}</span>
                    <span class="activity-time">${activity.time}</span>
                </div>
            `);
            
            // Add new item with animation
            $newItem.css({
                opacity: 0,
                transform: 'translateX(-20px)',
                background: 'rgba(39, 174, 96, 0.1)'
            });
            
            $feed.prepend($newItem);
            
            // Animate in
            $newItem.animate({
                opacity: 1
            }, {
                duration: 500,
                step: function(now) {
                    $(this).css('transform', `translateX(${-20 + (20 * now)}px)`);
                },
                complete: function() {
                    // Remove highlight after 3 seconds
                    setTimeout(() => {
                        $newItem.animate({
                            background: 'transparent'
                        }, 1000).removeClass('new-activity');
                    }, 3000);
                }
            });
            
            // Remove old items if more than 3
            const $items = $feed.find('.activity-item');
            if ($items.length > 3) {
                $items.slice(3).fadeOut(500, function() {
                    $(this).remove();
                });
            }
        },
        
        // Update single activity item
        updateActivityItem: function(activity) {
            const $existingItem = $('.activity-item').first();
            const newText = activity.text;
            const currentText = $existingItem.find('.activity-text').text();
            
            if (currentText !== newText) {
                this.addActivityItem(activity);
            }
        },
        
        // Add visual feedback for interactions
        addInteractionFeedback: function() {
            $('.trust-indicator').on('mouseenter', function() {
                $(this).find('.trust-icon').addClass('pulse-animation');
            }).on('mouseleave', function() {
                $(this).find('.trust-icon').removeClass('pulse-animation');
            });
        },
        
        // Track scroll-based trust indicator visibility
        trackVisibility: function() {
            const $trustSection = $('.trust-indicators-section');
            let hasTracked = false;
            
            $(window).on('scroll', function() {
                if (!hasTracked && $trustSection.length) {
                    const sectionTop = $trustSection.offset().top;
                    const scrollTop = $(window).scrollTop();
                    const windowHeight = $(window).height();
                    
                    if (scrollTop + windowHeight > sectionTop + 100) {
                        hasTracked = true;
                        window.ChatGABI.ConversionTracker.trackConversion('trust_indicators_viewed', {
                            section: 'trust_bar',
                            scroll_depth: Math.round((scrollTop / $(document).height()) * 100)
                        });
                    }
                }
            });
        }
    };
    
    // Add CSS animations
    const trustStyles = `
        <style>
        .pulse-animation {
            animation: trust-pulse 1s ease-in-out;
        }
        
        @keyframes trust-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .new-activity {
            transition: background-color 1s ease;
        }
        
        .trust-indicator {
            position: relative;
            overflow: hidden;
        }
        
        .trust-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .trust-indicator:hover::before {
            left: 100%;
        }
        
        @media (max-width: 768px) {
            .trust-indicator {
                transform: none !important;
            }
            
            .trust-indicator:hover {
                transform: none !important;
            }
        }
        </style>
    `;
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Add styles
        $('head').append(trustStyles);
        
        // Initialize trust indicators
        if ($('.trust-indicators-section').length) {
            TrustIndicators.init();
            TrustIndicators.addInteractionFeedback();
            TrustIndicators.trackVisibility();
        }
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.TrustIndicators = TrustIndicators;

})(jQuery);
