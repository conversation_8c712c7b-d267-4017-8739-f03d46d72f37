<?php
/**
 * WordPress Configuration for ChatGABI Testing
 * 
 * Copy this to wp-config.php in your WordPress installation
 */

// ** MySQL settings ** //
define('DB_NAME', 'chatgabi_test');
define('DB_USER', 'root');
define('DB_PASSWORD', ''); // Default XAMPP MySQL password is empty
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Authentication Unique Keys and Salts ** //
define('AUTH_KEY',         'test-auth-key-for-local-development-only');
define('SECURE_AUTH_KEY',  'test-secure-auth-key-for-local-development');
define('LOGGED_IN_KEY',    'test-logged-in-key-for-local-development');
define('NONCE_KEY',        'test-nonce-key-for-local-development-only');
define('AUTH_SALT',        'test-auth-salt-for-local-development-only');
define('SECURE_AUTH_SALT', 'test-secure-auth-salt-for-local-development');
define('LOGGED_IN_SALT',   'test-logged-in-salt-for-local-development');
define('NONCE_SALT',       'test-nonce-salt-for-local-development-only');

// ** WordPress Database Table prefix ** //
$table_prefix = 'wp_';

// ** WordPress debugging ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
define('SCRIPT_DEBUG', true);

// ** ChatGABI specific constants ** //
define('CHATGABI_THEME_URL', get_template_directory_uri());
define('CHATGABI_THEME_DIR', get_template_directory());
define('CHATGABI_VERSION', '1.0.0');

// ** Testing mode ** //
define('CHATGABI_TESTING_MODE', true);
define('CHATGABI_DISABLE_EXTERNAL_APIS', true); // Disable external API calls during testing

// ** Local development settings ** //
define('WP_HOME', 'http://localhost/chatgabi');
define('WP_SITEURL', 'http://localhost/chatgabi');

// ** Disable file editing ** //
define('DISALLOW_FILE_EDIT', false); // Allow for testing

// ** Memory limit ** //
define('WP_MEMORY_LIMIT', '256M');

// ** That's all, stop editing! Happy publishing. ** //
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

require_once(ABSPATH . 'wp-settings.php');
?>
