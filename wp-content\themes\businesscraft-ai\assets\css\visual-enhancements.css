/**
 * ChatGABI Visual Enhancements
 * 
 * Enhanced visual elements, graphics, and animations
 * for improved engagement and conversion optimization.
 */

/* ===== VISUAL ENHANCEMENT VARIABLES ===== */
:root {
    /* African-Inspired Color Palette */
    --color-african-gold: #FFD700;
    --color-african-sunset: #E67E22;
    --color-african-earth: #8B4513;
    --color-african-sky: #3D4E81;
    --color-african-nature: #27AE60;
    
    /* Glassmorphism Effects */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-blur: blur(10px);
    
    /* Shadows and Depth */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-heavy: rgba(0, 0, 0, 0.3);
    
    /* Animation Timings */
    --animation-fast: 0.2s;
    --animation-normal: 0.3s;
    --animation-slow: 0.5s;
}

/* ===== ENHANCED VISUAL ELEMENTS ===== */

/* African Pattern Backgrounds */
.african-pattern-bg {
    background-image: 
        radial-gradient(circle at 25% 25%, var(--color-african-gold) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--color-african-sunset) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px;
    background-position: 0 0, 25px 25px;
    opacity: 0.1;
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% { background-position: 0 0, 25px 25px; }
    100% { background-position: 50px 50px, 75px 75px; }
}

/* Kente-Inspired Border Patterns */
.kente-border {
    position: relative;
    overflow: hidden;
}

.kente-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--color-african-gold) 0%, 
        var(--color-african-sunset) 25%, 
        var(--color-african-nature) 50%, 
        var(--color-african-sky) 75%, 
        var(--color-african-gold) 100%);
    background-size: 200% 100%;
    animation: kenteFlow 3s ease-in-out infinite;
}

@keyframes kenteFlow {
    0%, 100% { background-position: 0% 0%; }
    50% { background-position: 100% 0%; }
}

/* Enhanced Glassmorphism Cards */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: 0 8px 32px var(--shadow-light);
    transition: all var(--animation-normal) ease;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--animation-slow) ease;
}

.glass-card:hover::before {
    left: 100%;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px var(--shadow-medium);
}

/* African Flag Animations */
.flag-wave {
    display: inline-block;
    animation: flagWave 2s ease-in-out infinite;
    transform-origin: left center;
}

@keyframes flagWave {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(2deg) scale(1.05); }
    75% { transform: rotate(-2deg) scale(1.05); }
}

/* Success Metrics with Visual Appeal */
.metric-visual {
    position: relative;
    display: inline-block;
}

.metric-visual::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--color-african-gold), var(--color-african-sunset));
    border-radius: 2px;
    transform: scaleX(0);
    animation: metricGrow 2s ease-out forwards;
    animation-delay: 1s;
}

@keyframes metricGrow {
    to { transform: scaleX(1); }
}

/* Enhanced Button Styles with African Inspiration */
.btn-african-primary {
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    color: #2c3e50;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all var(--animation-normal) ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-african-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--animation-normal) ease;
}

.btn-african-primary:hover::before {
    left: 100%;
}

.btn-african-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    color: #2c3e50;
}

.btn-african-primary:active {
    transform: translateY(-1px);
}

/* Pulsing Success Indicators */
.success-pulse {
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
    }
    50% { 
        transform: scale(1.05); 
        box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
    }
}

/* Floating Elements Animation */
.floating-element {
    animation: floatAround 6s ease-in-out infinite;
}

.floating-element:nth-child(odd) {
    animation-direction: reverse;
}

@keyframes floatAround {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.8;
    }
    33% { 
        transform: translateY(-15px) rotate(2deg); 
        opacity: 1;
    }
    66% { 
        transform: translateY(10px) rotate(-2deg); 
        opacity: 0.9;
    }
}

/* Enhanced Trust Indicators */
.trust-badge {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 50px;
    padding: 8px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: white;
    animation: trustGlow 3s ease-in-out infinite;
}

@keyframes trustGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.6); }
}

/* African Business Icons */
.african-business-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--color-african-sky), var(--color-african-nature));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin-bottom: 16px;
    animation: iconBounce 2s ease-in-out infinite;
    box-shadow: 0 4px 15px var(--shadow-medium);
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Progress Indicators */
.progress-indicator {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-african-gold), var(--color-african-sunset));
    border-radius: 3px;
    transform: translateX(-100%);
    animation: progressFill 3s ease-out forwards;
    animation-delay: 0.5s;
}

@keyframes progressFill {
    to { transform: translateX(0); }
}

/* Enhanced Testimonial Cards */
.testimonial-enhanced {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 24px;
    position: relative;
    transition: all var(--animation-normal) ease;
    overflow: hidden;
}

.testimonial-enhanced::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 60px;
    color: var(--color-african-gold);
    opacity: 0.3;
    font-family: serif;
}

.testimonial-enhanced:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px var(--shadow-medium);
}

/* Country Flag Hover Effects */
.country-flag {
    display: inline-block;
    font-size: 24px;
    transition: all var(--animation-normal) ease;
    cursor: pointer;
}

.country-flag:hover {
    transform: scale(1.2) rotate(5deg);
    filter: brightness(1.2);
}

/* Loading Animations */
.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: var(--color-african-gold);
    border-radius: 50%;
    animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingBounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Enhanced Visual Feedback */
.visual-feedback {
    position: relative;
    overflow: hidden;
}

.visual-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--animation-fast) ease;
}

.visual-feedback:active::after {
    width: 200px;
    height: 200px;
}

/* Responsive Visual Enhancements */
@media (max-width: 768px) {
    .african-business-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .btn-african-primary {
        padding: 14px 24px;
        font-size: 15px;
    }
    
    .glass-card {
        border-radius: 12px;
    }
    
    .testimonial-enhanced {
        padding: 20px;
        border-radius: 16px;
    }
    
    .floating-element {
        animation-duration: 4s;
    }
}

@media (max-width: 480px) {
    .african-business-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .btn-african-primary {
        padding: 12px 20px;
        font-size: 14px;
    }
    
    .trust-badge {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Dark Mode Adaptations */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.3);
        --glass-border: rgba(255, 255, 255, 0.1);
        --shadow-light: rgba(255, 255, 255, 0.1);
        --shadow-medium: rgba(255, 255, 255, 0.2);
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    .floating-element,
    .african-business-icon,
    .flag-wave,
    .success-pulse,
    .loading-dot {
        animation: none;
    }
    
    .glass-card:hover,
    .testimonial-enhanced:hover,
    .btn-african-primary:hover {
        transform: none;
    }
}
