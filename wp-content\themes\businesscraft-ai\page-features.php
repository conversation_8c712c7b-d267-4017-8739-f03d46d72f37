<?php
/**
 * Template Name: Features
 *
 * Features page for ChatGABI
 * Comprehensive showcase of AI business intelligence capabilities
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get user data for personalization
$current_user = wp_get_current_user();
$user_credits = 0;
$user_country = 'Ghana';
$user_sector = 'General';

if ($current_user->ID) {
    $user_credits = get_user_meta($current_user->ID, 'businesscraft_credits', true) ?: 0;
    $user_country = get_user_meta($current_user->ID, 'country', true) ?: 'Ghana';
    $user_sector = get_user_meta($current_user->ID, 'business_sector', true) ?: 'General';
}
?>

<div class="chatgabi-features-page">
    <div class="container">
        <!-- Features Hero Section -->
        <div class="features-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🧠</span>
                    <?php _e('ChatGABI Features', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Discover the power of AI-driven business intelligence designed specifically for African entrepreneurs. From market analysis to document generation, ChatGABI transforms how you build and grow your business.', 'chatgabi'); ?>
                </p>

                <?php if ($current_user->ID): ?>
                    <div class="user-context-bar">
                        <div class="context-item">
                            <span class="context-icon">👤</span>
                            <span><?php echo esc_html($current_user->display_name); ?></span>
                        </div>
                        <div class="context-item">
                            <span class="context-icon">🌍</span>
                            <span><?php echo esc_html($user_country); ?></span>
                        </div>
                        <div class="context-item">
                            <span class="context-icon">🏢</span>
                            <span><?php echo esc_html($user_sector); ?></span>
                        </div>
                        <div class="context-item">
                            <span class="context-icon">💳</span>
                            <span><?php echo number_format($user_credits); ?> <?php _e('Credits', 'chatgabi'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">67</span>
                        <span class="stat-label"><?php _e('Business Sectors', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <span class="stat-label"><?php _e('African Countries', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label"><?php _e('Languages', 'chatgabi'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label"><?php _e('Templates', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Features Section -->
        <div class="core-features-section">
            <div class="section-header">
                <h2><?php _e('Core AI Features', 'chatgabi'); ?></h2>
                <p><?php _e('Powerful AI-driven tools designed for African business success', 'chatgabi'); ?></p>
            </div>

            <div class="features-grid">
                <!-- AI Chat Intelligence -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">🤖</div>
                    <h3><?php _e('AI Chat Intelligence', 'chatgabi'); ?></h3>
                    <p><?php _e('Conversational AI powered by OpenAI GPT-3.5-turbo, trained on African business contexts and market intelligence.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('Real-time business advice and insights', 'chatgabi'); ?></li>
                            <li><?php _e('Context-aware responses for African markets', 'chatgabi'); ?></li>
                            <li><?php _e('Multi-language support (English, Twi, Swahili, Yoruba, Zulu)', 'chatgabi'); ?></li>
                            <li><?php _e('400-token optimized responses for efficiency', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">1 <?php _e('credit per query', 'chatgabi'); ?></span>
                    </div>
                </div>

                <!-- Business Templates -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">📝</div>
                    <h3><?php _e('AI Business Templates', 'chatgabi'); ?></h3>
                    <p><?php _e('Generate professional business documents with AI-powered templates tailored for African markets and regulations.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('500+ pre-built business templates', 'chatgabi'); ?></li>
                            <li><?php _e('AI-enhanced content generation', 'chatgabi'); ?></li>
                            <li><?php _e('Country-specific legal compliance', 'chatgabi'); ?></li>
                            <li><?php _e('Industry-specific customization', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">2 <?php _e('credits per enhancement', 'chatgabi'); ?></span>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">📊</div>
                    <h3><?php _e('Market Intelligence', 'chatgabi'); ?></h3>
                    <p><?php _e('Real-time market analysis and opportunity identification across 67 business sectors in Ghana, Kenya, Nigeria, and South Africa.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('Sector-specific market data and trends', 'chatgabi'); ?></li>
                            <li><?php _e('Opportunity alerts and notifications', 'chatgabi'); ?></li>
                            <li><?php _e('Competitive landscape analysis', 'chatgabi'); ?></li>
                            <li><?php _e('Investment and funding insights', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">3 <?php _e('credits per analysis', 'chatgabi'); ?></span>
                    </div>
                </div>

                <!-- Document Wizards -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">🧙‍♂️</div>
                    <h3><?php _e('Document Wizards', 'chatgabi'); ?></h3>
                    <p><?php _e('Step-by-step guided creation of professional business documents with AI assistance and African market context.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('Business plan wizard with financial projections', 'chatgabi'); ?></li>
                            <li><?php _e('Marketing strategy development', 'chatgabi'); ?></li>
                            <li><?php _e('Legal document templates', 'chatgabi'); ?></li>
                            <li><?php _e('Export to PDF, DOCX, and other formats', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">5 <?php _e('credits per document', 'chatgabi'); ?></span>
                    </div>
                </div>

                <!-- African Context Engine -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">🌍</div>
                    <h3><?php _e('African Context Engine', 'chatgabi'); ?></h3>
                    <p><?php _e('Advanced cultural intelligence system that understands African business practices, regulations, and market dynamics.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('Cultural business philosophy integration', 'chatgabi'); ?></li>
                            <li><?php _e('Local business terminology and practices', 'chatgabi'); ?></li>
                            <li><?php _e('Regulatory compliance guidance', 'chatgabi'); ?></li>
                            <li><?php _e('Payment method and currency preferences', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value"><?php _e('Included in all features', 'chatgabi'); ?></span>
                    </div>
                </div>

                <!-- Multi-Language Support -->
                <div class="feature-card glassmorphism-card">
                    <div class="feature-icon">🗣️</div>
                    <h3><?php _e('Multi-Language AI', 'chatgabi'); ?></h3>
                    <p><?php _e('Communicate with ChatGABI in your preferred African language with real-time AI translation and cultural context.', 'chatgabi'); ?></p>
                    <div class="feature-details">
                        <ul>
                            <li><?php _e('English, Twi, Swahili, Yoruba, Zulu support', 'chatgabi'); ?></li>
                            <li><?php _e('Real-time AI translation with cultural context', 'chatgabi'); ?></li>
                            <li><?php _e('Business terminology localization', 'chatgabi'); ?></li>
                            <li><?php _e('Quality scoring and validation', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    <div class="feature-cost">
                        <span class="cost-label"><?php _e('Cost:', 'chatgabi'); ?></span>
                        <span class="cost-value">1 <?php _e('credit per translation', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Features Section -->
        <div class="advanced-features-section">
            <div class="section-header">
                <h2><?php _e('Advanced Business Intelligence', 'chatgabi'); ?></h2>
                <p><?php _e('Professional-grade tools for serious entrepreneurs', 'chatgabi'); ?></p>
            </div>

            <div class="advanced-features-grid">
                <!-- Credit Management System -->
                <div class="advanced-feature glassmorphism-card">
                    <div class="feature-header">
                        <span class="feature-icon">💳</span>
                        <h3><?php _e('Smart Credit System', 'chatgabi'); ?></h3>
                    </div>
                    <p><?php _e('Flexible, transparent credit-based pricing with multi-currency support and detailed usage tracking.', 'chatgabi'); ?></p>
                    <div class="feature-highlights">
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Pay-per-use model - no subscriptions', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Multi-currency support (GHS, KES, NGN, ZAR, USD)', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Detailed usage analytics and cost tracking', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Secure Paystack payment integration', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Performance Analytics -->
                <div class="advanced-feature glassmorphism-card">
                    <div class="feature-header">
                        <span class="feature-icon">📈</span>
                        <h3><?php _e('Performance Analytics', 'chatgabi'); ?></h3>
                    </div>
                    <p><?php _e('Comprehensive analytics dashboard tracking your business intelligence usage and ROI across all ChatGABI features.', 'chatgabi'); ?></p>
                    <div class="feature-highlights">
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Real-time usage tracking and insights', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('ROI calculation and business impact metrics', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Feature usage optimization recommendations', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Export reports for business planning', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Security & Compliance -->
                <div class="advanced-feature glassmorphism-card">
                    <div class="feature-header">
                        <span class="feature-icon">🔒</span>
                        <h3><?php _e('Security & Compliance', 'chatgabi'); ?></h3>
                    </div>
                    <p><?php _e('Enterprise-grade security with full compliance to African data protection regulations and international standards.', 'chatgabi'); ?></p>
                    <div class="feature-highlights">
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('POPIA, NDPR, and GDPR compliance', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('End-to-end encryption for all data', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Regular security audits and updates', 'chatgabi'); ?></span>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">✅</span>
                            <span><?php _e('Data residency options for African markets', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Demo Section -->
        <div class="demo-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Try ChatGABI Now', 'chatgabi'); ?></h2>
                <p><?php _e('Experience the power of AI business intelligence with our interactive demo', 'chatgabi'); ?></p>
            </div>

            <div class="demo-content">
                <?php if (!$current_user->ID): ?>
                    <!-- Not logged in - Show registration CTA -->
                    <div class="demo-cta">
                        <h3><?php _e('Get Started with 25 Free Credits', 'chatgabi'); ?></h3>
                        <p><?php _e('Create your account and receive 25 free credits to explore all ChatGABI features. No credit card required.', 'chatgabi'); ?></p>
                        <div class="cta-actions">
                            <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary btn-large">
                                <span class="btn-icon">🚀</span>
                                <?php _e('Start Free Trial', 'chatgabi'); ?>
                            </a>
                            <a href="<?php echo esc_url(home_url('/login')); ?>" class="btn btn-secondary">
                                <span class="btn-icon">🔑</span>
                                <?php _e('Login', 'chatgabi'); ?>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Logged in - Show feature access -->
                    <div class="demo-dashboard">
                        <h3><?php _e('Welcome back, %s!', 'chatgabi'); ?></h3>
                        <p><?php printf(__('You have %s credits available. Choose a feature to explore:', 'chatgabi'), '<strong>' . number_format($user_credits) . '</strong>'); ?></p>
                        <div class="feature-access-grid">
                            <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="feature-access-card">
                                <span class="access-icon">🤖</span>
                                <h4><?php _e('AI Chat', 'chatgabi'); ?></h4>
                                <p><?php _e('Start a conversation with our AI assistant', 'chatgabi'); ?></p>
                                <span class="access-cost">1 <?php _e('credit', 'chatgabi'); ?></span>
                            </a>
                            <a href="<?php echo esc_url(home_url('/templates')); ?>" class="feature-access-card">
                                <span class="access-icon">📝</span>
                                <h4><?php _e('Templates', 'chatgabi'); ?></h4>
                                <p><?php _e('Generate business documents', 'chatgabi'); ?></p>
                                <span class="access-cost">2 <?php _e('credits', 'chatgabi'); ?></span>
                            </a>
                            <a href="<?php echo esc_url(home_url('/market-analysis')); ?>" class="feature-access-card">
                                <span class="access-icon">📊</span>
                                <h4><?php _e('Market Analysis', 'chatgabi'); ?></h4>
                                <p><?php _e('Get sector insights and opportunities', 'chatgabi'); ?></p>
                                <span class="access-cost">3 <?php _e('credits', 'chatgabi'); ?></span>
                            </a>
                            <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="feature-access-card">
                                <span class="access-icon">💳</span>
                                <h4><?php _e('Buy Credits', 'chatgabi'); ?></h4>
                                <p><?php _e('Purchase more credits to continue', 'chatgabi'); ?></p>
                                <span class="access-cost"><?php _e('From $10', 'chatgabi'); ?></span>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Comparison Section -->
        <div class="comparison-section">
            <div class="section-header">
                <h2><?php _e('Why Choose ChatGABI?', 'chatgabi'); ?></h2>
                <p><?php _e('See how ChatGABI compares to other business intelligence solutions', 'chatgabi'); ?></p>
            </div>

            <div class="comparison-table glassmorphism-card">
                <table>
                    <thead>
                        <tr>
                            <th><?php _e('Feature', 'chatgabi'); ?></th>
                            <th class="chatgabi-column">
                                <span class="logo">ChatGABI</span>
                                <span class="tagline"><?php _e('AI for Africa', 'chatgabi'); ?></span>
                            </th>
                            <th><?php _e('Generic AI Tools', 'chatgabi'); ?></th>
                            <th><?php _e('Traditional Consultants', 'chatgabi'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php _e('African Market Focus', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">✅</span></td>
                            <td><span class="cross">❌</span></td>
                            <td><span class="partial">⚠️</span></td>
                        </tr>
                        <tr>
                            <td><?php _e('Multi-Language Support', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">✅</span> <small>5 languages</small></td>
                            <td><span class="partial">⚠️</span> <small>Limited</small></td>
                            <td><span class="cross">❌</span></td>
                        </tr>
                        <tr>
                            <td><?php _e('Real-time AI Assistance', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">✅</span> <small>24/7</small></td>
                            <td><span class="check">✅</span></td>
                            <td><span class="cross">❌</span> <small>Scheduled</small></td>
                        </tr>
                        <tr>
                            <td><?php _e('Cost per Query', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">$0.10</span></td>
                            <td><span class="partial">$0.20+</span></td>
                            <td><span class="cross">$50+</span></td>
                        </tr>
                        <tr>
                            <td><?php _e('Local Compliance', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">✅</span> <small>POPIA, NDPR</small></td>
                            <td><span class="cross">❌</span></td>
                            <td><span class="check">✅</span></td>
                        </tr>
                        <tr>
                            <td><?php _e('Business Templates', 'chatgabi'); ?></td>
                            <td class="chatgabi-column"><span class="check">500+</span></td>
                            <td><span class="partial">50+</span></td>
                            <td><span class="check">Custom</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Testimonials Section -->
        <div class="testimonials-section">
            <div class="section-header">
                <h2><?php _e('What African Entrepreneurs Say', 'chatgabi'); ?></h2>
                <p><?php _e('Real feedback from business owners across Africa', 'chatgabi'); ?></p>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card glassmorphism-card">
                    <div class="testimonial-content">
                        <p>"<?php _e('ChatGABI helped me create a comprehensive business plan for my tech startup in Lagos. The AI understood Nigerian market dynamics perfectly.', 'chatgabi'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4>Adaora Okafor</h4>
                            <span><?php _e('Tech Entrepreneur, Lagos, Nigeria', 'chatgabi'); ?></span>
                        </div>
                        <div class="author-flag">🇳🇬</div>
                    </div>
                </div>

                <div class="testimonial-card glassmorphism-card">
                    <div class="testimonial-content">
                        <p>"<?php _e('The multi-language support in Swahili made it easy for my team to collaborate. ChatGABI truly understands African business culture.', 'chatgabi'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4>James Mwangi</h4>
                            <span><?php _e('Agriculture Business, Nairobi, Kenya', 'chatgabi'); ?></span>
                        </div>
                        <div class="author-flag">🇰🇪</div>
                    </div>
                </div>

                <div class="testimonial-card glassmorphism-card">
                    <div class="testimonial-content">
                        <p>"<?php _e('From market analysis to financial projections, ChatGABI provided insights that helped secure funding for my renewable energy project.', 'chatgabi'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4>Thandiwe Ndlovu</h4>
                            <span><?php _e('Clean Energy Entrepreneur, Cape Town, South Africa', 'chatgabi'); ?></span>
                        </div>
                        <div class="author-flag">🇿🇦</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="faq-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Frequently Asked Questions', 'chatgabi'); ?></h2>
                <p><?php _e('Common questions about ChatGABI features and capabilities', 'chatgabi'); ?></p>
            </div>

            <div class="faq-items">
                <div class="faq-item">
                    <h4><?php _e('How does the credit system work?', 'chatgabi'); ?></h4>
                    <p><?php _e('Credits are consumed when you use AI-powered features. Different features require different amounts of credits based on computational complexity. You can purchase credits in packages or start with 25 free credits.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Which African languages are supported?', 'chatgabi'); ?></h4>
                    <p><?php _e('ChatGABI supports English, Twi (Ghana), Swahili (Kenya/Tanzania), Yoruba (Nigeria), and Zulu (South Africa) with real-time AI translation and cultural context.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Is my business data secure?', 'chatgabi'); ?></h4>
                    <p><?php _e('Yes, we use enterprise-grade security with end-to-end encryption and comply with African data protection regulations including POPIA and NDPR.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Can I export generated documents?', 'chatgabi'); ?></h4>
                    <p><?php _e('Yes, all generated business documents can be exported to PDF, DOCX, and other popular formats for easy sharing and printing.', 'chatgabi'); ?></p>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="final-cta-section glassmorphism-card">
            <div class="cta-content">
                <h2><?php _e('Ready to Transform Your Business?', 'chatgabi'); ?></h2>
                <p><?php _e('Join thousands of African entrepreneurs who are already using ChatGABI to build successful businesses with AI-powered intelligence.', 'chatgabi'); ?></p>

                <div class="cta-actions">
                    <?php if (!$current_user->ID): ?>
                        <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">🚀</span>
                            <?php _e('Start Free Trial - 25 Credits', 'chatgabi'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="btn btn-secondary">
                            <span class="btn-icon">💰</span>
                            <?php _e('View Pricing', 'chatgabi'); ?>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">📊</span>
                            <?php _e('Go to Dashboard', 'chatgabi'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/pricing')); ?>" class="btn btn-secondary">
                            <span class="btn-icon">💳</span>
                            <?php _e('Buy More Credits', 'chatgabi'); ?>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="cta-guarantee">
                    <p><small><?php _e('✅ No subscription required • ✅ Pay only for what you use • ✅ 24/7 AI support', 'chatgabi'); ?></small></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Features Page Specific Styles */
.chatgabi-features-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.features-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.user-context-bar {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.context-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: rgba(var(--color-primary-accent), 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-text-primary);
}

.context-icon {
    font-size: 1.1rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    color: var(--color-secondary-accent);
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: var(--color-text-secondary);
    font-weight: 600;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.feature-card {
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
}

.feature-card h3 {
    font-size: 1.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.feature-details ul {
    list-style: none;
    padding: 0;
    text-align: left;
    margin: 20px 0;
}

.feature-details li {
    padding: 8px 0;
    color: var(--color-text-primary);
    position: relative;
    padding-left: 25px;
}

.feature-details li:before {
    content: "✓";
    color: var(--color-nature-green);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.feature-cost {
    background: rgba(var(--color-secondary-accent), 0.1);
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid var(--color-secondary-accent);
}

.cost-label {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

.cost-value {
    color: var(--color-primary-accent);
    font-weight: bold;
    font-size: 1.1rem;
}

/* Advanced Features */
.advanced-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.advanced-feature {
    padding: 30px;
}

.feature-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.feature-header .feature-icon {
    font-size: 2.5rem;
    margin: 0;
}

.feature-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.4rem;
    margin: 0;
}

.feature-highlights {
    margin-top: 20px;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: var(--color-text-primary);
}

.highlight-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

/* Demo Section */
.demo-cta {
    text-align: center;
}

.demo-cta h3 {
    color: var(--color-primary-accent);
    font-size: 2rem;
    margin-bottom: 15px;
}

.demo-cta p {
    color: var(--color-text-secondary);
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Feature Access Grid */
.feature-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.feature-access-card {
    background: rgba(var(--color-primary-accent), 0.05);
    border: 2px solid rgba(var(--color-primary-accent), 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.feature-access-card:hover {
    background: rgba(var(--color-primary-accent), 0.1);
    border-color: var(--color-primary-accent);
    transform: translateY(-3px);
}

.access-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 10px;
}

.feature-access-card h4 {
    color: var(--color-primary-accent);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.feature-access-card p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.access-cost {
    background: var(--color-secondary-accent);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Comparison Table */
.comparison-table {
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.comparison-table th,
.comparison-table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid rgba(var(--color-borders), 0.3);
}

.comparison-table th {
    background: rgba(var(--color-primary-accent), 0.1);
    color: var(--color-primary-accent);
    font-weight: bold;
}

.chatgabi-column {
    background: rgba(var(--color-secondary-accent), 0.1);
    border-left: 3px solid var(--color-secondary-accent);
    border-right: 3px solid var(--color-secondary-accent);
}

.chatgabi-column .logo {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--color-primary-accent);
    display: block;
}

.chatgabi-column .tagline {
    font-size: 0.8rem;
    color: var(--color-text-secondary);
}

.check {
    color: var(--color-nature-green);
    font-size: 1.2rem;
}

.cross {
    color: #e74c3c;
    font-size: 1.2rem;
}

.partial {
    color: #f39c12;
    font-size: 1.2rem;
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    padding: 30px;
}

.testimonial-content p {
    font-style: italic;
    color: var(--color-text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    color: var(--color-primary-accent);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.author-info span {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

.author-flag {
    font-size: 2rem;
}

/* FAQ */
.faq-items {
    display: grid;
    gap: 20px;
}

.faq-item {
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.faq-item h4 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.faq-item p {
    color: var(--color-text-secondary);
    line-height: 1.6;
}

/* Final CTA */
.final-cta-section {
    text-align: center;
    background: rgba(var(--color-secondary-accent), 0.1);
    border: 2px solid var(--color-secondary-accent);
}

.final-cta-section h2 {
    color: var(--color-primary-accent);
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.final-cta-section p {
    color: var(--color-text-secondary);
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-guarantee {
    margin-top: 20px;
}

.cta-guarantee small {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-features-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .features-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .user-context-bar {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .hero-stats {
        gap: 25px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .features-grid,
    .advanced-features-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-access-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .comparison-table {
        font-size: 0.9rem;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 10px 8px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .feature-card:hover {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .context-item {
    background: rgba(110, 127, 243, 0.2);
}

body.theme-dark .feature-cost {
    background: rgba(255, 215, 0, 0.1);
}

body.theme-dark .feature-access-card {
    background: rgba(110, 127, 243, 0.1);
    border-color: rgba(110, 127, 243, 0.3);
}

body.theme-dark .feature-access-card:hover {
    background: rgba(110, 127, 243, 0.2);
}

body.theme-dark .comparison-table th {
    background: rgba(110, 127, 243, 0.2);
}

body.theme-dark .chatgabi-column {
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--color-secondary-accent);
}

body.theme-dark .testimonial-card {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .faq-item {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .final-cta-section {
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--color-secondary-accent);
}
</style>

<?php
// Add SEO meta tags
function chatgabi_features_meta() {
    $title = __('ChatGABI Features - AI Business Intelligence for African Entrepreneurs', 'chatgabi');
    $description = __('Discover ChatGABI\'s powerful AI features: business templates, market analysis, multi-language support, and African context intelligence. Transform your business with AI-powered insights designed for Ghana, Kenya, Nigeria, and South Africa.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary_large_image">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';

    // Schema.org structured data
    echo '<script type="application/ld+json">';
    echo json_encode(array(
        '@context' => 'https://schema.org',
        '@type' => 'SoftwareApplication',
        'name' => 'ChatGABI',
        'description' => $description,
        'applicationCategory' => 'BusinessApplication',
        'operatingSystem' => 'Web',
        'offers' => array(
            '@type' => 'Offer',
            'price' => '10',
            'priceCurrency' => 'USD',
            'description' => 'Starting from $10 for 100 credits'
        ),
        'featureList' => array(
            'AI Business Intelligence',
            'Multi-language Support',
            'African Market Focus',
            'Business Templates',
            'Market Analysis',
            'Document Generation'
        )
    ));
    echo '</script>';
}
add_action('wp_head', 'chatgabi_features_meta');

get_footer();
?>