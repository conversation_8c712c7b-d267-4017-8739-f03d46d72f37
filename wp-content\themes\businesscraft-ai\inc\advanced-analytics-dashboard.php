<?php
/**
 * Advanced Analytics Dashboard for BusinessCraft AI
 * 
 * Provides comprehensive analytics including:
 * - User behavior insights and AI usage patterns
 * - Business growth metrics and success tracking
 * - African market trend analysis and opportunities
 * - Real-time performance monitoring
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize advanced analytics dashboard
 */
function businesscraft_ai_init_advanced_analytics() {
    // Add AJAX handlers
    add_action('wp_ajax_get_advanced_analytics', 'businesscraft_ai_handle_get_advanced_analytics');
    add_action('wp_ajax_get_user_behavior_insights', 'businesscraft_ai_handle_get_user_behavior_insights');
    add_action('wp_ajax_get_business_growth_metrics', 'businesscraft_ai_handle_get_business_growth_metrics');
    add_action('wp_ajax_get_market_trend_analysis', 'businesscraft_ai_handle_get_market_trend_analysis');
    add_action('wp_ajax_export_analytics_data', 'businesscraft_ai_handle_export_analytics_data');

    // Phase 3: Advanced analytics handlers
    add_action('wp_ajax_chatgabi_get_realtime_metrics', 'chatgabi_get_realtime_metrics');
    add_action('wp_ajax_chatgabi_get_conversion_funnel', 'chatgabi_get_conversion_funnel');
    add_action('wp_ajax_chatgabi_get_african_market_insights', 'chatgabi_get_african_market_insights');
    add_action('wp_ajax_chatgabi_get_ab_test_dashboard', 'chatgabi_get_ab_test_dashboard');

    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_advanced_analytics_routes');

    // Enqueue analytics scripts
    add_action('wp_enqueue_scripts', 'businesscraft_ai_enqueue_advanced_analytics_scripts');
    add_action('admin_enqueue_scripts', 'chatgabi_enqueue_phase3_analytics_scripts');

    // Create analytics tables
    add_action('init', 'businesscraft_ai_create_advanced_analytics_tables');

    // Schedule analytics processing
    add_action('businesscraft_ai_process_analytics', 'businesscraft_ai_process_daily_analytics');
    if (!wp_next_scheduled('businesscraft_ai_process_analytics')) {
        wp_schedule_event(time(), 'daily', 'businesscraft_ai_process_analytics');
    }

    // Add admin menu for Phase 3 analytics
    add_action('admin_menu', 'chatgabi_add_phase3_analytics_menu');
}
add_action('init', 'businesscraft_ai_init_advanced_analytics');

/**
 * Register REST API routes for advanced analytics
 */
function businesscraft_ai_register_advanced_analytics_routes() {
    register_rest_route('businesscraft-ai/v1', '/analytics/dashboard', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_dashboard_analytics',
        'permission_callback' => function() {
            return current_user_can('manage_options') || current_user_can('edit_posts');
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/analytics/user-behavior', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_user_behavior',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/analytics/market-trends', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_market_trends',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/analytics/export', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_export_analytics',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        }
    ));
}

/**
 * Enqueue advanced analytics scripts
 */
function businesscraft_ai_enqueue_advanced_analytics_scripts() {
    if (is_page('dashboard') || is_admin()) {
        wp_enqueue_script(
            'businesscraft-ai-advanced-analytics',
            get_template_directory_uri() . '/assets/js/advanced-analytics.js',
            array('jquery', 'chart-js'),
            '1.0.0',
            true
        );
        
        // Enqueue Chart.js
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );
        
        wp_localize_script('businesscraft-ai-advanced-analytics', 'businesscraftAdvancedAnalytics', array(
            'restUrl' => rest_url('businesscraft-ai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'currentUserId' => get_current_user_id(),
            'isAdmin' => current_user_can('manage_options'),
            'strings' => array(
                'loading' => __('Loading analytics...', 'businesscraft-ai'),
                'error' => __('Failed to load analytics data', 'businesscraft-ai'),
                'noData' => __('No data available for the selected period', 'businesscraft-ai'),
                'exportSuccess' => __('Analytics data exported successfully!', 'businesscraft-ai'),
                'exportError' => __('Failed to export analytics data', 'businesscraft-ai')
            )
        ));
        
        wp_enqueue_style(
            'businesscraft-ai-advanced-analytics',
            get_template_directory_uri() . '/assets/css/advanced-analytics.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * Get comprehensive dashboard analytics
 */
function businesscraft_ai_get_dashboard_analytics($period = 30, $user_id = null) {
    global $wpdb;
    
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    $date_to = date('Y-m-d');
    
    // User filter
    $user_filter = $user_id ? $wpdb->prepare("AND user_id = %d", $user_id) : "";
    
    $analytics = array(
        'summary' => businesscraft_ai_get_analytics_summary($date_from, $date_to, $user_id),
        'user_behavior' => businesscraft_ai_get_user_behavior_insights($date_from, $date_to, $user_id),
        'business_growth' => businesscraft_ai_get_business_growth_metrics($date_from, $date_to, $user_id),
        'market_trends' => businesscraft_ai_get_market_trend_analysis($date_from, $date_to),
        'ai_usage' => businesscraft_ai_get_ai_usage_patterns($date_from, $date_to, $user_id),
        'feature_adoption' => businesscraft_ai_get_feature_adoption_metrics($date_from, $date_to, $user_id),
        'performance' => businesscraft_ai_get_performance_metrics($date_from, $date_to)
    );
    
    return $analytics;
}

/**
 * Get analytics summary
 */
function businesscraft_ai_get_analytics_summary($date_from, $date_to, $user_id = null) {
    global $wpdb;
    
    $user_filter = $user_id ? $wpdb->prepare("AND user_id = %d", $user_id) : "";
    
    // Total conversations
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
    $total_conversations = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}",
        $date_from, $date_to
    ));
    
    // Total templates generated
    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';
    $total_templates = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$templates_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}",
        $date_from, $date_to
    ));
    
    // Total credits used
    $credits_table = $wpdb->prefix . 'chatgabi_credit_transactions';
    $total_credits = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(ABS(amount)) FROM {$credits_table} 
         WHERE transaction_type = 'debit' AND DATE(created_at) BETWEEN %s AND %s {$user_filter}",
        $date_from, $date_to
    )) ?: 0;
    
    // Active users
    $active_users = $user_id ? 1 : $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT user_id) FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s",
        $date_from, $date_to
    ));
    
    // Calculate growth rates
    $previous_period_start = date('Y-m-d', strtotime("-" . ($period * 2) . " days"));
    $previous_period_end = date('Y-m-d', strtotime("-{$period} days"));
    
    $previous_conversations = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}",
        $previous_period_start, $previous_period_end
    ));
    
    $conversation_growth = $previous_conversations > 0 ? 
        (($total_conversations - $previous_conversations) / $previous_conversations) * 100 : 0;
    
    return array(
        'total_conversations' => intval($total_conversations),
        'total_templates' => intval($total_templates),
        'total_credits_used' => intval($total_credits),
        'active_users' => intval($active_users),
        'conversation_growth' => round($conversation_growth, 2),
        'avg_conversations_per_user' => $active_users > 0 ? round($total_conversations / $active_users, 2) : 0,
        'avg_templates_per_user' => $active_users > 0 ? round($total_templates / $active_users, 2) : 0
    );
}

/**
 * Get user behavior insights
 */
function businesscraft_ai_get_user_behavior_insights($date_from, $date_to, $user_id = null) {
    global $wpdb;
    
    $user_filter = $user_id ? $wpdb->prepare("AND user_id = %d", $user_id) : "";
    
    // Most active hours
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
    $hourly_activity = $wpdb->get_results($wpdb->prepare(
        "SELECT HOUR(created_at) as hour, COUNT(*) as count 
         FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}
         GROUP BY HOUR(created_at) 
         ORDER BY count DESC",
        $date_from, $date_to
    ));
    
    // Most popular features
    $feature_usage = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            CASE 
                WHEN message LIKE '%template%' THEN 'Templates'
                WHEN message LIKE '%business plan%' THEN 'Business Plans'
                WHEN message LIKE '%marketing%' THEN 'Marketing'
                WHEN message LIKE '%financial%' THEN 'Financial'
                ELSE 'General Chat'
            END as feature,
            COUNT(*) as usage_count
         FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}
         GROUP BY feature 
         ORDER BY usage_count DESC",
        $date_from, $date_to
    ));
    
    // Session duration analysis
    $session_durations = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            user_id,
            DATE(created_at) as session_date,
            MIN(created_at) as session_start,
            MAX(created_at) as session_end,
            TIMESTAMPDIFF(MINUTE, MIN(created_at), MAX(created_at)) as duration_minutes
         FROM {$conversations_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}
         GROUP BY user_id, DATE(created_at)
         HAVING COUNT(*) > 1",
        $date_from, $date_to
    ));
    
    $avg_session_duration = 0;
    if (!empty($session_durations)) {
        $total_duration = array_sum(array_column($session_durations, 'duration_minutes'));
        $avg_session_duration = round($total_duration / count($session_durations), 2);
    }
    
    return array(
        'hourly_activity' => $hourly_activity,
        'feature_usage' => $feature_usage,
        'avg_session_duration' => $avg_session_duration,
        'total_sessions' => count($session_durations)
    );
}

/**
 * Get business growth metrics
 */
function businesscraft_ai_get_business_growth_metrics($date_from, $date_to, $user_id = null) {
    global $wpdb;
    
    $user_filter = $user_id ? $wpdb->prepare("AND user_id = %d", $user_id) : "";
    
    // Template completion rates
    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';
    $template_stats = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            template_type,
            COUNT(*) as total_created,
            SUM(CASE WHEN LENGTH(generated_content) > 1000 THEN 1 ELSE 0 END) as completed
         FROM {$templates_table} 
         WHERE DATE(created_at) BETWEEN %s AND %s {$user_filter}
         GROUP BY template_type",
        $date_from, $date_to
    ));
    
    // User progression tracking
    $user_progression = array();
    if (!$user_id) {
        $user_progression = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                u.ID as user_id,
                u.user_registered,
                COUNT(DISTINCT c.id) as total_conversations,
                COUNT(DISTINCT t.id) as total_templates,
                MAX(c.created_at) as last_activity
             FROM {$wpdb->users} u
             LEFT JOIN {$wpdb->prefix}chatgabi_conversations c ON u.ID = c.user_id
             LEFT JOIN {$wpdb->prefix}chatgabi_generated_templates t ON u.ID = t.user_id
             WHERE DATE(u.user_registered) <= %s
             GROUP BY u.ID
             ORDER BY total_conversations DESC
             LIMIT 50",
            $date_to
        ));
    }
    
    return array(
        'template_stats' => $template_stats,
        'user_progression' => $user_progression,
        'completion_rate' => businesscraft_ai_calculate_completion_rate($template_stats)
    );
}

/**
 * Calculate template completion rate
 */
function businesscraft_ai_calculate_completion_rate($template_stats) {
    $total_created = 0;
    $total_completed = 0;
    
    foreach ($template_stats as $stat) {
        $total_created += $stat->total_created;
        $total_completed += $stat->completed;
    }
    
    return $total_created > 0 ? round(($total_completed / $total_created) * 100, 2) : 0;
}

/**
 * AJAX Handlers
 */
function businesscraft_ai_handle_get_advanced_analytics() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $period = intval($_POST['period'] ?? 30);
    $user_id = current_user_can('manage_options') ? null : get_current_user_id();
    
    $analytics = businesscraft_ai_get_dashboard_analytics($period, $user_id);
    
    wp_send_json_success($analytics);
}

function businesscraft_ai_handle_get_user_behavior_insights() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $period = intval($_POST['period'] ?? 30);
    $user_id = get_current_user_id();
    
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    $date_to = date('Y-m-d');
    
    $insights = businesscraft_ai_get_user_behavior_insights($date_from, $date_to, $user_id);
    
    wp_send_json_success($insights);
}

/**
 * Create advanced analytics tables
 */
function businesscraft_ai_create_advanced_analytics_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // User behavior tracking table
    $behavior_table = $wpdb->prefix . 'chatgabi_user_behavior';
    $behavior_sql = "CREATE TABLE IF NOT EXISTS {$behavior_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        action_type varchar(50) NOT NULL,
        action_data longtext,
        session_id varchar(100),
        ip_address varchar(45),
        user_agent text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY action_type (action_type),
        KEY session_id (session_id),
        KEY created_at (created_at)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($behavior_sql);
}

/**
 * Phase 3: Advanced Analytics Functions
 */

/**
 * Get real-time metrics
 */
function chatgabi_get_realtime_metrics() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    global $wpdb;

    // Users online in last 5 minutes
    $users_online = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT user_id)
         FROM {$wpdb->prefix}chatgabi_conversions
         WHERE timestamp > %s",
        date('Y-m-d H:i:s', strtotime('-5 minutes'))
    ));

    // Conversions in last hour
    $hourly_conversions = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*)
         FROM {$wpdb->prefix}chatgabi_conversions
         WHERE timestamp > %s",
        date('Y-m-d H:i:s', strtotime('-1 hour'))
    ));

    // Revenue today
    $daily_revenue = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(meta_value)
         FROM {$wpdb->posts} p
         JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
         WHERE p.post_type = 'shop_order'
         AND p.post_status = 'wc-completed'
         AND pm.meta_key = '_order_total'
         AND DATE(p.post_date) = %s",
        date('Y-m-d')
    )) ?: 0;

    // Active A/B tests
    $active_ab_tests = $wpdb->get_var(
        "SELECT COUNT(DISTINCT test_name)
         FROM {$wpdb->prefix}chatgabi_ab_exposures
         WHERE DATE(timestamp) = CURDATE()"
    );

    wp_send_json_success(array(
        'users_online' => intval($users_online),
        'hourly_conversions' => intval($hourly_conversions),
        'daily_revenue' => floatval($daily_revenue),
        'active_ab_tests' => intval($active_ab_tests),
        'timestamp' => current_time('mysql')
    ));
}

/**
 * Get conversion funnel data
 */
function chatgabi_get_conversion_funnel() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    global $wpdb;

    $period = intval($_POST['period'] ?? 30);
    $date_from = date('Y-m-d', strtotime("-{$period} days"));

    // Define funnel steps
    $funnel_steps = array(
        'homepage_view' => 'Homepage Views',
        'signup_started' => 'Signup Started',
        'signup_completed' => 'Signup Completed',
        'first_chat' => 'First Chat',
        'template_created' => 'Template Created',
        'credit_purchase' => 'Credit Purchase'
    );

    $funnel_data = array();
    foreach ($funnel_steps as $step => $label) {
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT user_id)
             FROM {$wpdb->prefix}chatgabi_conversions
             WHERE event_type = %s
             AND DATE(timestamp) >= %s",
            $step, $date_from
        ));

        $funnel_data[] = array(
            'step' => $step,
            'label' => $label,
            'count' => intval($count)
        );
    }

    wp_send_json_success($funnel_data);
}

/**
 * Get African market insights
 */
function chatgabi_get_african_market_insights() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    global $wpdb;

    $period = intval($_POST['period'] ?? 30);
    $date_from = date('Y-m-d', strtotime("-{$period} days"));

    // Country performance
    $country_performance = $wpdb->get_results($wpdb->prepare(
        "SELECT
            JSON_UNQUOTE(JSON_EXTRACT(event_data, '$.country')) as country,
            COUNT(*) as total_events,
            COUNT(DISTINCT user_id) as unique_users,
            SUM(CASE WHEN event_type = 'credit_purchase' THEN 1 ELSE 0 END) as purchases
         FROM {$wpdb->prefix}chatgabi_conversions
         WHERE DATE(timestamp) >= %s
         AND JSON_EXTRACT(event_data, '$.country') IS NOT NULL
         GROUP BY country
         ORDER BY total_events DESC",
        $date_from
    ), ARRAY_A);

    // Language usage
    $language_usage = $wpdb->get_results($wpdb->prepare(
        "SELECT
            JSON_UNQUOTE(JSON_EXTRACT(event_data, '$.language')) as language,
            COUNT(*) as usage_count,
            COUNT(DISTINCT user_id) as unique_users
         FROM {$wpdb->prefix}chatgabi_conversions
         WHERE DATE(timestamp) >= %s
         AND JSON_EXTRACT(event_data, '$.language') IS NOT NULL
         GROUP BY language
         ORDER BY usage_count DESC",
        $date_from
    ), ARRAY_A);

    // Industry trends
    $industry_trends = $wpdb->get_results($wpdb->prepare(
        "SELECT
            JSON_UNQUOTE(JSON_EXTRACT(event_data, '$.industry')) as industry,
            COUNT(*) as activity_count,
            AVG(CASE WHEN event_type = 'template_created' THEN 1 ELSE 0 END) as completion_rate
         FROM {$wpdb->prefix}chatgabi_conversions
         WHERE DATE(timestamp) >= %s
         AND JSON_EXTRACT(event_data, '$.industry') IS NOT NULL
         GROUP BY industry
         ORDER BY activity_count DESC",
        $date_from
    ), ARRAY_A);

    wp_send_json_success(array(
        'country_performance' => $country_performance,
        'language_usage' => $language_usage,
        'industry_trends' => $industry_trends
    ));
}

/**
 * Get A/B test dashboard data
 */
function chatgabi_get_ab_test_dashboard() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    global $wpdb;

    // Get all active tests
    $active_tests = $wpdb->get_results(
        "SELECT DISTINCT test_name, MIN(timestamp) as start_date
         FROM {$wpdb->prefix}chatgabi_ab_exposures
         WHERE DATE(timestamp) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
         GROUP BY test_name
         ORDER BY start_date DESC",
        ARRAY_A
    );

    $test_results = array();
    foreach ($active_tests as $test) {
        $test_name = $test['test_name'];
        $results = chatgabi_ab_testing()->get_test_results($test_name);
        $results['start_date'] = $test['start_date'];
        $test_results[$test_name] = $results;
    }

    wp_send_json_success($test_results);
}

/**
 * Add Phase 3 analytics admin menu
 */
function chatgabi_add_phase3_analytics_menu() {
    add_submenu_page(
        'chatgabi-admin',
        __('Advanced Analytics', 'chatgabi'),
        __('Advanced Analytics', 'chatgabi'),
        'manage_options',
        'chatgabi-advanced-analytics',
        'chatgabi_phase3_analytics_page'
    );
}

/**
 * Phase 3 analytics admin page
 */
function chatgabi_phase3_analytics_page() {
    ?>
    <div class="wrap chatgabi-analytics-dashboard">
        <h1><?php _e('ChatGABI Advanced Analytics Dashboard', 'chatgabi'); ?></h1>

        <!-- Real-time Metrics -->
        <div class="analytics-section realtime-metrics">
            <h2><?php _e('Real-time Metrics', 'chatgabi'); ?></h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="users-online">-</div>
                    <div class="metric-label"><?php _e('Users Online', 'chatgabi'); ?></div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="hourly-conversions">-</div>
                    <div class="metric-label"><?php _e('Conversions (1h)', 'chatgabi'); ?></div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="daily-revenue">-</div>
                    <div class="metric-label"><?php _e('Revenue Today', 'chatgabi'); ?></div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="active-tests">-</div>
                    <div class="metric-label"><?php _e('Active A/B Tests', 'chatgabi'); ?></div>
                </div>
            </div>
        </div>

        <!-- Conversion Funnel -->
        <div class="analytics-section conversion-funnel">
            <h2><?php _e('Conversion Funnel', 'chatgabi'); ?></h2>
            <canvas id="funnel-chart" width="400" height="200"></canvas>
        </div>

        <!-- African Market Insights -->
        <div class="analytics-section african-insights">
            <h2><?php _e('African Market Insights', 'chatgabi'); ?></h2>
            <div class="insights-grid">
                <div class="insight-card">
                    <h3><?php _e('Country Performance', 'chatgabi'); ?></h3>
                    <canvas id="country-chart" width="300" height="200"></canvas>
                </div>
                <div class="insight-card">
                    <h3><?php _e('Language Usage', 'chatgabi'); ?></h3>
                    <canvas id="language-chart" width="300" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- A/B Testing Dashboard -->
        <div class="analytics-section ab-testing">
            <h2><?php _e('A/B Testing Results', 'chatgabi'); ?></h2>
            <div id="ab-tests-container">
                <div class="loading"><?php _e('Loading A/B test data...', 'chatgabi'); ?></div>
            </div>
        </div>

    </div>

    <style>
    .chatgabi-analytics-dashboard {
        max-width: 1200px;
    }

    .analytics-section {
        background: white;
        margin: 20px 0;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .metric-card {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--color-african-sky, #3d4e81);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--color-african-sky, #3d4e81);
        margin-bottom: 5px;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .insights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .insight-card {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .insight-card h3 {
        margin-top: 0;
        color: var(--color-african-sky, #3d4e81);
    }

    .loading {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }

    #ab-tests-container .test-result {
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--color-african-gold, #ffd700);
    }

    .test-result h4 {
        margin-top: 0;
        color: var(--color-african-sky, #3d4e81);
    }

    .variant-results {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .variant-card {
        padding: 10px;
        background: white;
        border-radius: 4px;
        text-align: center;
    }

    .variant-card.winner {
        border: 2px solid var(--color-african-nature, #28a745);
    }
    </style>
    <?php
}

/**
 * Enqueue Phase 3 analytics scripts
 */
function chatgabi_enqueue_phase3_analytics_scripts($hook) {
    if ($hook !== 'chatgabi_page_chatgabi-advanced-analytics') {
        return;
    }

    wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true);
    wp_enqueue_script(
        'chatgabi-phase3-analytics',
        CHATGABI_THEME_URL . '/assets/js/phase3-analytics-dashboard.js',
        array('jquery', 'chart-js'),
        CHATGABI_VERSION,
        true
    );

    wp_localize_script('chatgabi-phase3-analytics', 'chatgabiPhase3Analytics', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('chatgabi_analytics_nonce'),
        'refreshInterval' => 30000 // 30 seconds
    ));
}
