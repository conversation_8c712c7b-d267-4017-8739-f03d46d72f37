<?php
/**
 * Interactive Business Plan Builder Template Part
 * 
 * Advanced step-by-step business plan builder with AI assistance,
 * real-time collaboration, and African market customization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user data
$user_country = chatgabi_get_user_country();
$user_currency = chatgabi_get_user_currency();
$is_logged_in = is_user_logged_in();
?>

<section class="interactive-business-plan-builder">
    <div class="container">
        
        <!-- Builder Header -->
        <div class="builder-header">
            <h2><?php _e('Build Your Business Plan with AI', 'chatgabi'); ?></h2>
            <p class="builder-subtitle">
                <?php _e('Create a comprehensive, investor-ready business plan in 15 minutes. Our AI guides you through each step with African market insights.', 'chatgabi'); ?>
            </p>
            
            <!-- Progress Indicator -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <span class="step-number">1</span>
                        <span class="step-label"><?php _e('Business Idea', 'chatgabi'); ?></span>
                    </div>
                    <div class="step" data-step="2">
                        <span class="step-number">2</span>
                        <span class="step-label"><?php _e('Market Analysis', 'chatgabi'); ?></span>
                    </div>
                    <div class="step" data-step="3">
                        <span class="step-number">3</span>
                        <span class="step-label"><?php _e('Financial Plan', 'chatgabi'); ?></span>
                    </div>
                    <div class="step" data-step="4">
                        <span class="step-number">4</span>
                        <span class="step-label"><?php _e('Marketing Strategy', 'chatgabi'); ?></span>
                    </div>
                    <div class="step" data-step="5">
                        <span class="step-number">5</span>
                        <span class="step-label"><?php _e('Operations', 'chatgabi'); ?></span>
                    </div>
                    <div class="step" data-step="6">
                        <span class="step-number">6</span>
                        <span class="step-label"><?php _e('Review & Export', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Builder Content -->
        <div class="builder-content">
            
            <!-- Step 1: Business Idea -->
            <div class="builder-step active" id="step-1">
                <div class="step-content">
                    <div class="step-main">
                        <h3><?php _e('Tell us about your business idea', 'chatgabi'); ?></h3>
                        <p><?php _e('Let\'s start with the basics. Our AI will help you refine and develop your concept.', 'chatgabi'); ?></p>
                        
                        <div class="form-group">
                            <label for="business-name"><?php _e('Business Name', 'chatgabi'); ?></label>
                            <input type="text" id="business-name" placeholder="<?php _e('Enter your business name', 'chatgabi'); ?>">
                            <div class="ai-suggestion" id="name-suggestions"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="business-description"><?php _e('Business Description', 'chatgabi'); ?></label>
                            <textarea id="business-description" rows="4" placeholder="<?php _e('Describe your business idea in a few sentences...', 'chatgabi'); ?>"></textarea>
                            <div class="ai-suggestion" id="description-suggestions"></div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="industry"><?php _e('Industry', 'chatgabi'); ?></label>
                                <select id="industry">
                                    <option value=""><?php _e('Select industry', 'chatgabi'); ?></option>
                                    <option value="technology"><?php _e('Technology', 'chatgabi'); ?></option>
                                    <option value="agriculture"><?php _e('Agriculture', 'chatgabi'); ?></option>
                                    <option value="retail"><?php _e('Retail & E-commerce', 'chatgabi'); ?></option>
                                    <option value="manufacturing"><?php _e('Manufacturing', 'chatgabi'); ?></option>
                                    <option value="services"><?php _e('Professional Services', 'chatgabi'); ?></option>
                                    <option value="healthcare"><?php _e('Healthcare', 'chatgabi'); ?></option>
                                    <option value="education"><?php _e('Education', 'chatgabi'); ?></option>
                                    <option value="finance"><?php _e('Finance', 'chatgabi'); ?></option>
                                    <option value="tourism"><?php _e('Tourism & Hospitality', 'chatgabi'); ?></option>
                                    <option value="other"><?php _e('Other', 'chatgabi'); ?></option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="target-country"><?php _e('Primary Market', 'chatgabi'); ?></label>
                                <select id="target-country">
                                    <option value="GH" <?php selected($user_country, 'GH'); ?>>🇬🇭 <?php _e('Ghana', 'chatgabi'); ?></option>
                                    <option value="KE" <?php selected($user_country, 'KE'); ?>>🇰🇪 <?php _e('Kenya', 'chatgabi'); ?></option>
                                    <option value="NG" <?php selected($user_country, 'NG'); ?>>🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?></option>
                                    <option value="ZA" <?php selected($user_country, 'ZA'); ?>>🇿🇦 <?php _e('South Africa', 'chatgabi'); ?></option>
                                    <option value="CI">🇨🇮 <?php _e('Côte d\'Ivoire', 'chatgabi'); ?></option>
                                    <option value="OTHER"><?php _e('Other African Country', 'chatgabi'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="business-stage"><?php _e('Business Stage', 'chatgabi'); ?></label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="business-stage" value="idea">
                                    <span class="radio-custom"></span>
                                    <span class="radio-label"><?php _e('Just an idea', 'chatgabi'); ?></span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="business-stage" value="planning">
                                    <span class="radio-custom"></span>
                                    <span class="radio-label"><?php _e('Planning stage', 'chatgabi'); ?></span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="business-stage" value="startup">
                                    <span class="radio-custom"></span>
                                    <span class="radio-label"><?php _e('Early startup', 'chatgabi'); ?></span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="business-stage" value="existing">
                                    <span class="radio-custom"></span>
                                    <span class="radio-label"><?php _e('Existing business', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Assistant Panel -->
                    <div class="ai-assistant-panel">
                        <div class="ai-avatar">
                            <img src="<?php echo CHATGABI_THEME_URL; ?>/assets/images/ai-assistant-avatar.png" alt="AI Assistant">
                        </div>
                        <div class="ai-chat">
                            <div class="ai-message">
                                <p><?php _e('Hi! I\'m your AI business advisor. I\'ll help you create a comprehensive business plan tailored for the African market.', 'chatgabi'); ?></p>
                                <p><?php _e('Let\'s start with your business idea. What problem are you solving?', 'chatgabi'); ?></p>
                            </div>
                            <div class="ai-suggestions">
                                <h4><?php _e('Popular business ideas in your area:', 'chatgabi'); ?></h4>
                                <div class="suggestion-tags" id="business-idea-suggestions">
                                    <span class="suggestion-tag"><?php _e('E-commerce Platform', 'chatgabi'); ?></span>
                                    <span class="suggestion-tag"><?php _e('Agricultural Tech', 'chatgabi'); ?></span>
                                    <span class="suggestion-tag"><?php _e('Mobile Money Services', 'chatgabi'); ?></span>
                                    <span class="suggestion-tag"><?php _e('Educational Platform', 'chatgabi'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 2: Market Analysis -->
            <div class="builder-step" id="step-2">
                <div class="step-content">
                    <div class="step-main">
                        <h3><?php _e('Market Analysis', 'chatgabi'); ?></h3>
                        <p><?php _e('Let\'s analyze your target market and competition in the African context.', 'chatgabi'); ?></p>
                        
                        <div class="form-group">
                            <label for="target-customers"><?php _e('Target Customers', 'chatgabi'); ?></label>
                            <textarea id="target-customers" rows="3" placeholder="<?php _e('Describe your ideal customers...', 'chatgabi'); ?>"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="market-size"><?php _e('Market Size Estimate', 'chatgabi'); ?></label>
                            <div class="input-with-currency">
                                <span class="currency-symbol"><?php echo esc_html($user_currency['symbol']); ?></span>
                                <input type="text" id="market-size" placeholder="<?php _e('Total addressable market', 'chatgabi'); ?>">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="competitors"><?php _e('Main Competitors', 'chatgabi'); ?></label>
                            <textarea id="competitors" rows="3" placeholder="<?php _e('List your main competitors and what makes you different...', 'chatgabi'); ?>"></textarea>
                        </div>
                        
                        <div class="market-insights" id="market-insights">
                            <!-- AI-generated market insights will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="ai-assistant-panel">
                        <div class="ai-chat">
                            <div class="ai-message">
                                <p><?php _e('Based on your industry and location, here are some market insights:', 'chatgabi'); ?></p>
                            </div>
                            <div class="market-data" id="ai-market-data">
                                <!-- Dynamic market data will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional steps would continue here... -->
            <!-- For brevity, I'll include the navigation and key features -->
            
            <!-- Step Navigation -->
            <div class="step-navigation">
                <button class="nav-btn prev-btn" id="prev-step" disabled>
                    <span class="btn-icon">←</span>
                    <?php _e('Previous', 'chatgabi'); ?>
                </button>
                
                <div class="nav-center">
                    <button class="save-draft-btn" id="save-draft">
                        <span class="btn-icon">💾</span>
                        <?php _e('Save Draft', 'chatgabi'); ?>
                    </button>
                    <span class="save-status" id="save-status"></span>
                </div>
                
                <button class="nav-btn next-btn" id="next-step">
                    <?php _e('Next', 'chatgabi'); ?>
                    <span class="btn-icon">→</span>
                </button>
            </div>
            
        </div>
        
        <!-- Login Prompt for Non-logged Users -->
        <?php if (!$is_logged_in): ?>
        <div class="login-prompt-overlay" id="login-prompt">
            <div class="login-prompt">
                <h3><?php _e('Save Your Progress', 'chatgabi'); ?></h3>
                <p><?php _e('Create a free account to save your business plan and access AI assistance.', 'chatgabi'); ?></p>
                <div class="login-buttons">
                    <a href="<?php echo wp_registration_url(); ?>" class="btn-primary">
                        <?php _e('Create Free Account', 'chatgabi'); ?>
                    </a>
                    <a href="<?php echo wp_login_url(); ?>" class="btn-secondary">
                        <?php _e('Login', 'chatgabi'); ?>
                    </a>
                </div>
                <button class="close-prompt" id="close-login-prompt">&times;</button>
            </div>
        </div>
        <?php endif; ?>
        
    </div>
</section>

<!-- AI Chat Modal -->
<div class="ai-chat-modal-overlay" id="ai-chat-modal">
    <div class="ai-chat-modal">
        <div class="modal-header">
            <h3><?php _e('AI Business Advisor', 'chatgabi'); ?></h3>
            <button class="modal-close" id="ai-chat-close">&times;</button>
        </div>
        <div class="modal-content">
            <div class="chat-messages" id="chat-messages">
                <!-- Chat messages will be dynamically added here -->
            </div>
            <div class="chat-input">
                <input type="text" id="chat-input" placeholder="<?php _e('Ask me anything about your business plan...', 'chatgabi'); ?>">
                <button id="send-chat" class="send-btn">
                    <span class="btn-icon">📤</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Interactive Business Plan Builder Styles */
.interactive-business-plan-builder {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.builder-header {
    text-align: center;
    margin-bottom: 3rem;
}

.builder-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.builder-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto 2rem auto;
}

/* Progress Indicator */
.progress-container {
    max-width: 800px;
    margin: 0 auto;
}

.progress-bar {
    height: 6px;
    background: #dee2e6;
    border-radius: 3px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-african-gold), var(--color-african-sunset));
    width: 16.67%; /* 1/6 steps */
    transition: width 0.5s ease;
}

.progress-steps {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number,
.step.completed .step-number {
    background: var(--color-african-sky);
    color: white;
}

.step-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 600;
}

.step.active .step-label {
    color: var(--color-african-sky);
}

/* Builder Content */
.builder-content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.builder-step {
    display: none;
    min-height: 600px;
}

.builder-step.active {
    display: block;
}

.step-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    min-height: 600px;
}

.step-main {
    padding: 3rem;
    border-right: 1px solid #dee2e6;
}

.step-main h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.step-main p {
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Form Elements */
.form-group {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--color-african-sky);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-african-sky);
}

.input-with-currency {
    display: flex;
    align-items: center;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.input-with-currency:focus-within {
    border-color: var(--color-african-sky);
}

.currency-symbol {
    font-weight: 700;
    color: var(--color-african-gold);
    margin-right: 0.5rem;
}

.input-with-currency input {
    border: none;
    padding: 0;
    flex: 1;
}

.input-with-currency input:focus {
    outline: none;
}

/* Radio Group */
.radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option:hover {
    border-color: var(--color-african-sky);
    background: #f8f9fa;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--color-african-sky);
    background: var(--color-african-sky);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

.radio-label {
    font-weight: 600;
    color: #2c3e50;
}

/* AI Suggestions */
.ai-suggestion {
    margin-top: 0.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--color-african-gold);
    display: none;
}

.ai-suggestion.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.suggestion-tag {
    padding: 0.5rem 1rem;
    background: var(--color-african-gold);
    color: #2c3e50;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.suggestion-tag:hover {
    background: var(--color-african-sunset);
    transform: translateY(-2px);
}

/* AI Assistant Panel */
.ai-assistant-panel {
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.ai-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: 4px solid var(--color-african-gold);
}

.ai-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ai-chat {
    width: 100%;
    max-width: 300px;
}

.ai-message {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    position: relative;
}

.ai-message::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.ai-message p {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    line-height: 1.5;
}

.ai-message p:last-child {
    margin-bottom: 0;
}

.ai-suggestions h4 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

/* Step Navigation */
.step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 3rem;
    background: white;
    border-top: 1px solid #dee2e6;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.prev-btn {
    background: #f8f9fa;
    color: #6c757d;
}

.next-btn {
    background: var(--color-african-sky);
    color: white;
}

.nav-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-center {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.save-draft-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--color-african-gold);
    color: #2c3e50;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-draft-btn:hover {
    background: var(--color-african-sunset);
    transform: translateY(-2px);
}

.save-status {
    font-size: 0.85rem;
    color: #6c757d;
}

.save-status.saving {
    color: var(--color-african-gold);
}

.save-status.saved {
    color: var(--color-african-nature);
}

/* Login Prompt */
.login-prompt-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.login-prompt-overlay.show {
    opacity: 1;
    visibility: visible;
}

.login-prompt {
    background: white;
    border-radius: 16px;
    padding: 3rem;
    max-width: 500px;
    width: 100%;
    text-align: center;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.login-prompt-overlay.show .login-prompt {
    transform: scale(1);
}

.login-prompt h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.login-prompt p {
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.login-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--color-african-sky);
    color: white;
}

.btn-secondary {
    background: #f8f9fa;
    color: var(--color-african-sky);
    border: 2px solid var(--color-african-sky);
}

.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.close-prompt {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.close-prompt:hover {
    color: var(--color-african-sky);
}

/* AI Chat Modal */
.ai-chat-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.ai-chat-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.ai-chat-modal {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 100%;
    height: 500px;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.ai-chat-modal-overlay.show .ai-chat-modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--color-african-sky);
}

.modal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.chat-input {
    display: flex;
    padding: 1.5rem;
    border-top: 1px solid #dee2e6;
    gap: 1rem;
}

.chat-input input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
}

.chat-input input:focus {
    outline: none;
    border-color: var(--color-african-sky);
}

.send-btn {
    padding: 0.75rem 1rem;
    background: var(--color-african-sky);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
    .step-content {
        grid-template-columns: 1fr;
    }

    .ai-assistant-panel {
        border-right: none;
        border-top: 1px solid #dee2e6;
    }

    .progress-steps {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .step-label {
        font-size: 0.75rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .radio-group {
        grid-template-columns: 1fr;
    }

    .step-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-center {
        order: -1;
    }

    .login-buttons {
        flex-direction: column;
    }
}
</style>
