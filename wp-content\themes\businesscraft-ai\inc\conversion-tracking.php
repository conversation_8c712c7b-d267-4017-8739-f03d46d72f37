<?php
/**
 * ChatGABI Conversion Tracking System
 * 
 * Implements Google Analytics 4, Facebook Pixel, and custom conversion tracking
 * for homepage optimization and African market analytics.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize conversion tracking system
 */
function chatgabi_init_conversion_tracking() {
    // Add tracking codes to head
    add_action('wp_head', 'chatgabi_add_analytics_tracking', 1);
    
    // Add conversion tracking scripts
    add_action('wp_footer', 'chatgabi_add_conversion_scripts', 20);
    
    // Track specific events
    add_action('wp_ajax_chatgabi_track_conversion', 'chatgabi_handle_conversion_tracking');
    add_action('wp_ajax_nopriv_chatgabi_track_conversion', 'chatgabi_handle_conversion_tracking');
    
    // Add admin settings
    add_action('admin_menu', 'chatgabi_add_analytics_admin_menu');
}
add_action('init', 'chatgabi_init_conversion_tracking');

/**
 * Add Google Analytics 4 and Facebook Pixel tracking
 */
function chatgabi_add_analytics_tracking() {
    $ga4_id = get_option('chatgabi_ga4_id', '');
    $fb_pixel_id = get_option('chatgabi_fb_pixel_id', '');
    
    // Google Analytics 4
    if (!empty($ga4_id)) {
        ?>
        <!-- Google Analytics 4 - ChatGABI -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo esc_attr($ga4_id); ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?php echo esc_js($ga4_id); ?>', {
                'custom_map': {
                    'custom_parameter_1': 'african_country',
                    'custom_parameter_2': 'user_language',
                    'custom_parameter_3': 'business_sector'
                }
            });
            
            // Enhanced ecommerce for credit purchases
            gtag('config', '<?php echo esc_js($ga4_id); ?>', {
                'currency': '<?php echo chatgabi_get_user_currency()["currency"]; ?>',
                'country': '<?php echo chatgabi_get_user_country(); ?>'
            });
        </script>
        <?php
    }
    
    // Facebook Pixel
    if (!empty($fb_pixel_id)) {
        ?>
        <!-- Facebook Pixel - ChatGABI -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            
            fbq('init', '<?php echo esc_js($fb_pixel_id); ?>');
            fbq('track', 'PageView');
            
            // Custom parameters for African market
            fbq('track', 'ViewContent', {
                content_category: 'ai_business_tools',
                content_name: 'chatgabi_homepage',
                country: '<?php echo chatgabi_get_user_country(); ?>',
                language: '<?php echo chatgabi_get_user_preferred_language(); ?>'
            });
        </script>
        <noscript>
            <img height="1" width="1" style="display:none" 
                 src="https://www.facebook.com/tr?id=<?php echo esc_attr($fb_pixel_id); ?>&ev=PageView&noscript=1"/>
        </noscript>
        <?php
    }
}

/**
 * Add conversion tracking scripts to footer
 */
function chatgabi_add_conversion_scripts() {
    ?>
    <script>
    // ChatGABI Conversion Tracking
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.ConversionTracker = {
        
        // Track homepage conversions
        trackConversion: function(event, data) {
            data = data || {};
            
            // Google Analytics tracking
            if (typeof gtag !== 'undefined') {
                gtag('event', event, {
                    'event_category': 'conversion',
                    'event_label': 'homepage',
                    'custom_parameter_1': data.country || '<?php echo chatgabi_get_user_country(); ?>',
                    'custom_parameter_2': data.language || '<?php echo chatgabi_get_user_preferred_language(); ?>',
                    'custom_parameter_3': data.sector || 'general',
                    'value': data.value || 0
                });
            }
            
            // Facebook Pixel tracking
            if (typeof fbq !== 'undefined') {
                fbq('track', event, {
                    content_category: 'conversion',
                    content_name: 'chatgabi_' + event.toLowerCase(),
                    country: data.country || '<?php echo chatgabi_get_user_country(); ?>',
                    value: data.value || 0,
                    currency: '<?php echo chatgabi_get_user_currency()["currency"]; ?>'
                });
            }
            
            // Custom tracking to WordPress
            this.sendToWordPress(event, data);
        },
        
        // Send conversion data to WordPress
        sendToWordPress: function(event, data) {
            jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'chatgabi_track_conversion',
                    event: event,
                    data: JSON.stringify(data),
                    nonce: '<?php echo wp_create_nonce('chatgabi_conversion_nonce'); ?>'
                }
            });
        },
        
        // Track specific homepage events
        trackSignup: function(method) {
            this.trackConversion('signup', {
                method: method,
                value: 50, // Free credits value
                page: 'homepage'
            });
        },
        
        trackCTAClick: function(ctaType, position) {
            this.trackConversion('cta_click', {
                cta_type: ctaType,
                position: position,
                page: 'homepage'
            });
        },
        
        trackDemoInteraction: function() {
            this.trackConversion('demo_interaction', {
                interaction_type: 'chat_demo',
                page: 'homepage'
            });
        },
        
        trackPricingView: function() {
            this.trackConversion('pricing_view', {
                section: 'homepage_pricing',
                page: 'homepage'
            });
        }
    };
    
    // Auto-track scroll depth
    let scrollDepthTracked = [];
    jQuery(window).on('scroll', function() {
        let scrollPercent = Math.round((jQuery(window).scrollTop() / (jQuery(document).height() - jQuery(window).height())) * 100);
        
        [25, 50, 75, 90].forEach(function(depth) {
            if (scrollPercent >= depth && scrollDepthTracked.indexOf(depth) === -1) {
                scrollDepthTracked.push(depth);
                window.ChatGABI.ConversionTracker.trackConversion('scroll_depth', {
                    depth: depth,
                    page: 'homepage'
                });
            }
        });
    });
    
    // Track time on page
    let timeOnPage = 0;
    setInterval(function() {
        timeOnPage += 10;
        if (timeOnPage === 30 || timeOnPage === 60 || timeOnPage === 120) {
            window.ChatGABI.ConversionTracker.trackConversion('time_on_page', {
                seconds: timeOnPage,
                page: 'homepage'
            });
        }
    }, 10000);
    
    </script>
    <?php
}

/**
 * Handle AJAX conversion tracking
 */
function chatgabi_handle_conversion_tracking() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_conversion_nonce')) {
        wp_die('Security check failed');
    }
    
    $event = sanitize_text_field($_POST['event']);
    $data = json_decode(stripslashes($_POST['data']), true);
    
    // Store conversion in database
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_conversions';
    
    $wpdb->insert(
        $table_name,
        array(
            'event_type' => $event,
            'event_data' => json_encode($data),
            'user_id' => get_current_user_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'page_url' => $data['page_url'] ?? '',
            'timestamp' => current_time('mysql')
        ),
        array('%s', '%s', '%d', '%s', '%s', '%s', '%s')
    );
    
    wp_send_json_success(array('tracked' => true));
}

/**
 * Create conversions table
 */
function chatgabi_create_conversions_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_conversions';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        event_type varchar(100) NOT NULL,
        event_data text,
        user_id bigint(20),
        ip_address varchar(45),
        user_agent text,
        page_url varchar(255),
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY event_type (event_type),
        KEY user_id (user_id),
        KEY timestamp (timestamp)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Add analytics admin menu
 */
function chatgabi_add_analytics_admin_menu() {
    add_submenu_page(
        'chatgabi-admin',
        __('Conversion Analytics', 'chatgabi'),
        __('Conversion Analytics', 'chatgabi'),
        'manage_options',
        'chatgabi-conversion-analytics',
        'chatgabi_conversion_analytics_page'
    );
}

/**
 * Conversion analytics admin page
 */
function chatgabi_conversion_analytics_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('ChatGABI Conversion Analytics', 'chatgabi'); ?></h1>
        
        <div class="chatgabi-analytics-dashboard">
            <div class="analytics-cards">
                <div class="analytics-card">
                    <h3><?php _e('Homepage Conversions', 'chatgabi'); ?></h3>
                    <div class="metric-value" id="homepage-conversions">-</div>
                    <div class="metric-change">+0% from last week</div>
                </div>
                
                <div class="analytics-card">
                    <h3><?php _e('Signup Rate', 'chatgabi'); ?></h3>
                    <div class="metric-value" id="signup-rate">-</div>
                    <div class="metric-change">+0% from last week</div>
                </div>
                
                <div class="analytics-card">
                    <h3><?php _e('Demo Interactions', 'chatgabi'); ?></h3>
                    <div class="metric-value" id="demo-interactions">-</div>
                    <div class="metric-change">+0% from last week</div>
                </div>
                
                <div class="analytics-card">
                    <h3><?php _e('African Markets', 'chatgabi'); ?></h3>
                    <div class="metric-value" id="african-markets">4</div>
                    <div class="metric-change">GH, KE, NG, ZA</div>
                </div>
            </div>
            
            <div class="analytics-settings">
                <h2><?php _e('Tracking Settings', 'chatgabi'); ?></h2>
                <form method="post" action="options.php">
                    <?php settings_fields('chatgabi_analytics_settings'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Google Analytics 4 ID', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="chatgabi_ga4_id" value="<?php echo esc_attr(get_option('chatgabi_ga4_id')); ?>" 
                                       placeholder="G-XXXXXXXXXX" class="regular-text" />
                                <p class="description"><?php _e('Enter your GA4 Measurement ID', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Facebook Pixel ID', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="chatgabi_fb_pixel_id" value="<?php echo esc_attr(get_option('chatgabi_fb_pixel_id')); ?>" 
                                       placeholder="123456789012345" class="regular-text" />
                                <p class="description"><?php _e('Enter your Facebook Pixel ID', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button(); ?>
                </form>
            </div>
        </div>
    </div>
    
    <style>
    .chatgabi-analytics-dashboard {
        margin-top: 20px;
    }
    
    .analytics-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .analytics-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #3D4E81;
    }
    
    .analytics-card h3 {
        margin: 0 0 10px 0;
        color: #3D4E81;
        font-size: 14px;
        font-weight: 600;
    }
    
    .metric-value {
        font-size: 32px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    
    .metric-change {
        font-size: 12px;
        color: #7f8c8d;
    }
    
    .analytics-settings {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    </style>
    <?php
}

// Register settings
add_action('admin_init', function() {
    register_setting('chatgabi_analytics_settings', 'chatgabi_ga4_id');
    register_setting('chatgabi_analytics_settings', 'chatgabi_fb_pixel_id');
});

// Create table on activation
register_activation_hook(__FILE__, 'chatgabi_create_conversions_table');
