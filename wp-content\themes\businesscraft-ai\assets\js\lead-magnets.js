/**
 * Lead Magnets JavaScript
 * 
 * Handles lead magnet downloads, email capture,
 * and modal interactions for conversion optimization.
 */

(function($) {
    'use strict';

    const LeadMagnets = {
        
        // Configuration
        config: {
            modalAnimationDuration: 300,
            recentDownloadsInterval: 10000, // 10 seconds
            maxRecentDownloads: 3
        },
        
        // State
        state: {
            currentMagnetId: null,
            isSubmitting: false,
            recentDownloads: []
        },
        
        // Initialize lead magnets system
        init: function() {
            this.bindEvents();
            this.initRecentDownloads();
            this.trackLeadMagnetViews();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Download button clicks
            $(document).on('click', '.download-btn', (e) => {
                this.handleDownloadClick(e);
            });
            
            // Preview button clicks
            $(document).on('click', '.preview-btn', (e) => {
                this.handlePreviewClick(e);
            });
            
            // View all resources button
            $('#view-all-resources').on('click', () => {
                this.showAllResources();
            });
            
            // Modal close events
            $('#download-modal-close, .download-modal-overlay').on('click', (e) => {
                if (e.target === e.currentTarget) {
                    this.closeModal();
                }
            });
            
            // Form submission
            $('#download-form').on('submit', (e) => {
                this.handleFormSubmission(e);
            });
            
            // Success close button
            $('#success-close-btn').on('click', () => {
                this.closeModal();
            });
            
            // Escape key to close modal
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape' && this.isModalOpen()) {
                    this.closeModal();
                }
            });
        },
        
        // Handle download button click
        handleDownloadClick: function(e) {
            e.preventDefault();
            const magnetId = $(e.currentTarget).data('magnet-id');
            this.openDownloadModal(magnetId);
            
            // Track download button click
            this.trackDownloadButtonClick(magnetId);
        },
        
        // Handle preview button click
        handlePreviewClick: function(e) {
            e.preventDefault();
            e.stopPropagation();
            const magnetId = $(e.currentTarget).data('magnet-id');
            
            // Track preview click
            this.trackPreviewClick(magnetId);
            
            // For now, open download modal
            // In a real implementation, you'd show a preview
            this.openDownloadModal(magnetId);
        },
        
        // Open download modal
        openDownloadModal: function(magnetId) {
            this.state.currentMagnetId = magnetId;
            
            // Get magnet data from DOM
            const $card = $(`.lead-magnet-card[data-magnet-id="${magnetId}"]`);
            const title = $card.find('.magnet-title').text();
            const description = $card.find('.magnet-description').text();
            const previewImage = $card.find('.magnet-preview-image').attr('src');
            const rating = $card.find('.stat-value').first().text();
            const downloads = $card.find('.stat-value').last().text();
            
            // Populate modal
            $('#download-modal-title').text(`Download: ${title}`);
            $('#download-modal-description').text(description);
            $('#download-modal-image').attr('src', previewImage);
            $('#download-modal-rating').text(rating);
            $('#download-modal-downloads').text(downloads);
            
            // Reset form
            this.resetForm();
            
            // Show modal
            $('#download-modal-overlay').addClass('show');
            
            // Focus on email input
            setTimeout(() => {
                $('#download-email').focus();
            }, this.config.modalAnimationDuration);
            
            // Track modal open
            this.trackModalOpen(magnetId);
        },
        
        // Close download modal
        closeModal: function() {
            $('#download-modal-overlay').removeClass('show');
            this.state.currentMagnetId = null;
            
            // Reset form after animation
            setTimeout(() => {
                this.resetForm();
            }, this.config.modalAnimationDuration);
        },
        
        // Check if modal is open
        isModalOpen: function() {
            return $('#download-modal-overlay').hasClass('show');
        },
        
        // Reset download form
        resetForm: function() {
            $('#download-form')[0].reset();
            $('#download-form').show();
            $('#download-success-message').hide();
            this.state.isSubmitting = false;
            $('#download-submit-btn').prop('disabled', false);
        },
        
        // Handle form submission
        handleFormSubmission: function(e) {
            e.preventDefault();
            
            if (this.state.isSubmitting) return;
            
            const formData = this.getFormData();
            
            // Validate form
            if (!this.validateForm(formData)) {
                return;
            }
            
            // Submit form
            this.submitDownloadRequest(formData);
        },
        
        // Get form data
        getFormData: function() {
            return {
                magnet_id: this.state.currentMagnetId,
                email: $('#download-email').val().trim(),
                name: $('#download-name').val().trim(),
                country: $('#download-country').val(),
                newsletter: $('#download-newsletter').is(':checked')
            };
        },
        
        // Validate form data
        validateForm: function(data) {
            // Email validation
            if (!data.email) {
                this.showFormError(chatgabiLeadMagnets.strings.email_required);
                $('#download-email').focus();
                return false;
            }
            
            if (!this.isValidEmail(data.email)) {
                this.showFormError(chatgabiLeadMagnets.strings.invalid_email);
                $('#download-email').focus();
                return false;
            }
            
            return true;
        },
        
        // Validate email format
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        // Show form error
        showFormError: function(message) {
            // Remove existing error
            $('.form-error').remove();
            
            // Add error message
            const $error = $(`<div class="form-error" style="color: #dc3545; font-size: 0.9rem; margin-bottom: 1rem; text-align: center;">${message}</div>`);
            $('#download-form').prepend($error);
            
            // Remove error after 5 seconds
            setTimeout(() => {
                $error.fadeOut(() => $error.remove());
            }, 5000);
        },
        
        // Submit download request
        submitDownloadRequest: function(formData) {
            this.state.isSubmitting = true;
            
            // Update button state
            const $submitBtn = $('#download-submit-btn');
            const originalText = $submitBtn.find('.btn-text').text();
            $submitBtn.find('.btn-text').text(chatgabiLeadMagnets.strings.downloading);
            $submitBtn.prop('disabled', true);
            
            // Make AJAX request
            $.ajax({
                url: chatgabiLeadMagnets.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_download_lead_magnet',
                    nonce: chatgabiLeadMagnets.nonce,
                    ...formData
                },
                success: (response) => {
                    if (response.success) {
                        this.showSuccessMessage();
                        this.trackDownloadSuccess(formData.magnet_id, formData.email);
                        this.addRecentDownload(formData.name || 'Someone', formData.country);
                    } else {
                        this.showFormError(response.data.message || chatgabiLeadMagnets.strings.download_error);
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Download request failed:', error);
                    this.showFormError(chatgabiLeadMagnets.strings.download_error);
                },
                complete: () => {
                    this.state.isSubmitting = false;
                    $submitBtn.find('.btn-text').text(originalText);
                    $submitBtn.prop('disabled', false);
                }
            });
        },
        
        // Show success message
        showSuccessMessage: function() {
            $('#download-form').hide();
            $('#download-success-message').show();
        },
        
        // Show all resources
        showAllResources: function() {
            // Track view all click
            this.trackViewAllClick();
            
            // For now, just scroll to the section
            // In a real implementation, you'd load more resources
            $('html, body').animate({
                scrollTop: $('.lead-magnets-section').offset().top - 100
            }, 500);
            
            // Could also open a modal with all resources
            alert('All resources view would be implemented here. This could show a modal with all 6 resources or navigate to a dedicated resources page.');
        },
        
        // Initialize recent downloads simulation
        initRecentDownloads: function() {
            // Initial recent downloads
            this.state.recentDownloads = [
                { name: 'Kwame', country: 'Ghana', resource: 'Business Plan Template', time: '2 minutes ago' },
                { name: 'Aisha', country: 'Nigeria', resource: 'Marketing Playbook', time: '5 minutes ago' },
                { name: 'Grace', country: 'Kenya', resource: 'Financial Toolkit', time: '8 minutes ago' }
            ];
            
            this.updateRecentDownloads();
            
            // Start rotation
            setInterval(() => {
                this.rotateRecentDownloads();
            }, this.config.recentDownloadsInterval);
        },
        
        // Add new recent download
        addRecentDownload: function(name, country) {
            const countryNames = {
                'GH': 'Ghana',
                'KE': 'Kenya',
                'NG': 'Nigeria',
                'ZA': 'South Africa',
                'CI': 'Côte d\'Ivoire'
            };
            
            const newDownload = {
                name: name,
                country: countryNames[country] || country || 'Africa',
                resource: this.getCurrentMagnetTitle(),
                time: 'Just now'
            };
            
            this.state.recentDownloads.unshift(newDownload);
            
            // Keep only max downloads
            if (this.state.recentDownloads.length > this.config.maxRecentDownloads) {
                this.state.recentDownloads = this.state.recentDownloads.slice(0, this.config.maxRecentDownloads);
            }
            
            this.updateRecentDownloads();
        },
        
        // Get current magnet title
        getCurrentMagnetTitle: function() {
            if (this.state.currentMagnetId) {
                const $card = $(`.lead-magnet-card[data-magnet-id="${this.state.currentMagnetId}"]`);
                return $card.find('.magnet-title').text();
            }
            return 'Resource';
        },
        
        // Rotate recent downloads
        rotateRecentDownloads: function() {
            const sampleDownloads = [
                { name: 'Kofi', country: 'Ghana', resource: 'Business Plan Template', time: '1 minute ago' },
                { name: 'Fatima', country: 'Nigeria', resource: 'Funding Database', time: '3 minutes ago' },
                { name: 'James', country: 'Kenya', resource: 'Legal Checklist', time: '6 minutes ago' },
                { name: 'Nomsa', country: 'South Africa', resource: 'Market Guide', time: '9 minutes ago' },
                { name: 'Samuel', country: 'Ghana', resource: 'Financial Toolkit', time: '12 minutes ago' }
            ];
            
            // Add random download
            const randomDownload = sampleDownloads[Math.floor(Math.random() * sampleDownloads.length)];
            this.state.recentDownloads.unshift(randomDownload);
            
            // Keep only max downloads
            if (this.state.recentDownloads.length > this.config.maxRecentDownloads) {
                this.state.recentDownloads.pop();
            }
            
            this.updateRecentDownloads();
        },
        
        // Update recent downloads display
        updateRecentDownloads: function() {
            const $list = $('#recent-downloads-list');
            $list.empty();
            
            this.state.recentDownloads.forEach((download, index) => {
                const $item = $(`
                    <div class="recent-download-item" style="animation-delay: ${index * 0.1}s;">
                        <span class="download-text">${download.name} from ${download.country} downloaded ${download.resource}</span>
                        <span class="download-time">${download.time}</span>
                    </div>
                `);
                $list.append($item);
            });
        },
        
        // Track lead magnet views
        trackLeadMagnetViews: function() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const $card = $(entry.target);
                        const magnetId = $card.data('magnet-id');
                        this.trackMagnetView(magnetId);
                    }
                });
            }, { threshold: 0.5 });
            
            $('.lead-magnet-card').each(function() {
                observer.observe(this);
            });
        },
        
        // Tracking functions
        trackDownloadButtonClick: function(magnetId) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnet_download_clicked', {
                    magnet_id: magnetId,
                    section: 'lead_magnets'
                });
            }
        },
        
        trackPreviewClick: function(magnetId) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnet_preview_clicked', {
                    magnet_id: magnetId,
                    section: 'lead_magnets'
                });
            }
        },
        
        trackModalOpen: function(magnetId) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnet_modal_opened', {
                    magnet_id: magnetId,
                    section: 'lead_magnets'
                });
            }
        },
        
        trackDownloadSuccess: function(magnetId, email) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnet_downloaded', {
                    magnet_id: magnetId,
                    email: email,
                    section: 'lead_magnets'
                });
            }
        },
        
        trackViewAllClick: function() {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnets_view_all_clicked', {
                    section: 'lead_magnets'
                });
            }
        },
        
        trackMagnetView: function(magnetId) {
            if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                window.ChatGABI.ConversionTracker.trackConversion('lead_magnet_viewed', {
                    magnet_id: magnetId,
                    section: 'lead_magnets'
                });
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize lead magnets
        if ($('.lead-magnets-section').length) {
            LeadMagnets.init();
        }
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.LeadMagnets = LeadMagnets;

})(jQuery);
