<?php
/**
 * ChatGABI Trust Indicators System
 * 
 * Displays dynamic trust indicators, user counts, security badges,
 * and social proof elements for homepage conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize trust indicators system
 */
function chatgabi_init_trust_indicators() {
    // Add AJAX handlers for dynamic trust data
    add_action('wp_ajax_chatgabi_get_trust_data', 'chatgabi_get_trust_data');
    add_action('wp_ajax_nopriv_chatgabi_get_trust_data', 'chatgabi_get_trust_data');
    
    // Enqueue trust indicators scripts
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_trust_scripts');
    
    // Add trust indicators to homepage
    add_action('chatgabi_after_hero', 'chatgabi_display_trust_bar');
}
add_action('init', 'chatgabi_init_trust_indicators');

/**
 * Enqueue trust indicators scripts and styles
 */
function chatgabi_enqueue_trust_scripts() {
    if (is_front_page()) {
        wp_enqueue_script(
            'chatgabi-trust-indicators',
            CHATGABI_THEME_URL . '/assets/js/trust-indicators.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );
        
        wp_localize_script('chatgabi-trust-indicators', 'chatgabiTrust', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_trust_nonce'),
            'strings' => array(
                'users_online' => __('%d entrepreneurs online now', 'chatgabi'),
                'recent_signup' => __('%s from %s just joined', 'chatgabi'),
                'businesses_created' => __('%d+ businesses launched this month', 'chatgabi'),
                'loading' => __('Loading...', 'chatgabi')
            )
        ));
    }
}

/**
 * Display trust indicators bar
 */
function chatgabi_display_trust_bar() {
    ?>
    <section class="trust-indicators-section">
        <div class="container">
            <div class="trust-indicators-grid">
                
                <!-- User Count Display -->
                <div class="trust-indicator user-count">
                    <div class="trust-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="trust-content">
                        <div class="trust-number" id="user-count">10,000+</div>
                        <div class="trust-label"><?php _e('African Entrepreneurs', 'chatgabi'); ?></div>
                    </div>
                </div>
                
                <!-- Security Badge -->
                <div class="trust-indicator security-badge">
                    <div class="trust-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="trust-content">
                        <div class="trust-number"><?php _e('SSL Secured', 'chatgabi'); ?></div>
                        <div class="trust-label"><?php _e('Bank-Level Security', 'chatgabi'); ?></div>
                    </div>
                </div>
                
                <!-- Payment Security -->
                <div class="trust-indicator payment-security">
                    <div class="trust-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect x="1" y="4" width="22" height="16" rx="2" stroke="currentColor" stroke-width="2"/>
                            <path d="M1 10H23" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="trust-content">
                        <div class="trust-number"><?php _e('Paystack Secured', 'chatgabi'); ?></div>
                        <div class="trust-label"><?php _e('Mobile Money & Cards', 'chatgabi'); ?></div>
                    </div>
                </div>
                
                <!-- Live Activity -->
                <div class="trust-indicator live-activity">
                    <div class="trust-icon">
                        <div class="pulse-dot"></div>
                    </div>
                    <div class="trust-content">
                        <div class="trust-number" id="live-activity">Loading...</div>
                        <div class="trust-label"><?php _e('Live Activity', 'chatgabi'); ?></div>
                    </div>
                </div>
                
            </div>
            
            <!-- Recent Activity Feed -->
            <div class="recent-activity-feed" id="recent-activity">
                <div class="activity-item">
                    <span class="activity-text"><?php _e('Kwame from Ghana just created a business plan', 'chatgabi'); ?></span>
                    <span class="activity-time"><?php _e('2 minutes ago', 'chatgabi'); ?></span>
                </div>
            </div>
            
        </div>
    </section>
    
    <style>
    .trust-indicators-section {
        background: var(--color-surface);
        padding: 2rem 0;
        border-bottom: 1px solid var(--color-borders);
        margin-bottom: 2rem;
    }
    
    .trust-indicators-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .trust-indicator {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: var(--color-background);
        border-radius: var(--border-radius-md);
        box-shadow: 0 2px 8px var(--color-shadow-light);
        transition: transform 0.3s ease;
    }
    
    .trust-indicator:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--color-shadow-medium);
    }
    
    .trust-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--color-primary-accent), var(--color-secondary-accent));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
    }
    
    .trust-icon svg {
        width: 24px;
        height: 24px;
    }
    
    .pulse-dot {
        width: 12px;
        height: 12px;
        background: #27AE60;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .trust-content {
        flex: 1;
    }
    
    .trust-number {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--color-text-primary);
        margin-bottom: 0.25rem;
    }
    
    .trust-label {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
    }
    
    .recent-activity-feed {
        background: var(--color-background);
        border-radius: var(--border-radius-md);
        padding: 1rem;
        border-left: 4px solid var(--color-nature-green);
    }
    
    .activity-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--color-borders);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-text {
        color: var(--color-text-primary);
        font-weight: 500;
    }
    
    .activity-time {
        color: var(--color-text-secondary);
        font-size: 0.875rem;
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .trust-indicators-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .trust-indicator {
            padding: 0.75rem;
        }
        
        .trust-icon {
            width: 40px;
            height: 40px;
        }
        
        .trust-number {
            font-size: 1rem;
        }
        
        .trust-label {
            font-size: 0.8rem;
        }
        
        .activity-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }
    }
    
    @media (max-width: 480px) {
        .trust-indicators-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>
    <?php
}

/**
 * Get dynamic trust data via AJAX
 */
function chatgabi_get_trust_data() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_trust_nonce')) {
        wp_die('Security check failed');
    }
    
    // Get real or simulated trust metrics
    $trust_data = array(
        'user_count' => chatgabi_get_user_count(),
        'online_users' => chatgabi_get_online_users(),
        'recent_signups' => chatgabi_get_recent_signups(),
        'businesses_created' => chatgabi_get_businesses_created_count(),
        'recent_activity' => chatgabi_get_recent_activity()
    );
    
    wp_send_json_success($trust_data);
}

/**
 * Get total user count
 */
function chatgabi_get_user_count() {
    $cached_count = get_transient('chatgabi_user_count');
    
    if (false === $cached_count) {
        $user_count = count_users();
        $total_users = $user_count['total_users'];
        
        // Add some realistic padding for marketing purposes
        $display_count = max(10000, $total_users + 9500);
        
        set_transient('chatgabi_user_count', $display_count, HOUR_IN_SECONDS);
        return $display_count;
    }
    
    return $cached_count;
}

/**
 * Get simulated online users count
 */
function chatgabi_get_online_users() {
    // Simulate realistic online user count based on time of day
    $hour = date('H');
    $base_count = 50;
    
    // Peak hours adjustment (African business hours)
    if ($hour >= 8 && $hour <= 18) {
        $base_count += rand(20, 80);
    } else {
        $base_count += rand(5, 25);
    }
    
    return $base_count;
}

/**
 * Get recent signups for display
 */
function chatgabi_get_recent_signups() {
    $african_names = array(
        array('name' => 'Kwame', 'country' => 'Ghana'),
        array('name' => 'Aisha', 'country' => 'Nigeria'),
        array('name' => 'Kofi', 'country' => 'Ghana'),
        array('name' => 'Amara', 'country' => 'Kenya'),
        array('name' => 'Thabo', 'country' => 'South Africa'),
        array('name' => 'Fatima', 'country' => 'Nigeria'),
        array('name' => 'Jengo', 'country' => 'Kenya'),
        array('name' => 'Nomsa', 'country' => 'South Africa')
    );
    
    return $african_names[array_rand($african_names)];
}

/**
 * Get businesses created count
 */
function chatgabi_get_businesses_created_count() {
    $cached_count = get_transient('chatgabi_businesses_count');
    
    if (false === $cached_count) {
        // Simulate realistic business creation count
        $base_count = 2500;
        $monthly_growth = rand(200, 500);
        $display_count = $base_count + $monthly_growth;
        
        set_transient('chatgabi_businesses_count', $display_count, DAY_IN_SECONDS);
        return $display_count;
    }
    
    return $cached_count;
}

/**
 * Get recent activity for feed
 */
function chatgabi_get_recent_activity() {
    $activities = array(
        array(
            'text' => __('Kwame from Ghana just created a business plan', 'chatgabi'),
            'time' => __('2 minutes ago', 'chatgabi')
        ),
        array(
            'text' => __('Aisha from Nigeria purchased Growth credits', 'chatgabi'),
            'time' => __('5 minutes ago', 'chatgabi')
        ),
        array(
            'text' => __('Jengo from Kenya completed financial forecast', 'chatgabi'),
            'time' => __('8 minutes ago', 'chatgabi')
        ),
        array(
            'text' => __('Nomsa from South Africa joined ChatGABI', 'chatgabi'),
            'time' => __('12 minutes ago', 'chatgabi')
        )
    );
    
    return $activities[array_rand($activities)];
}
