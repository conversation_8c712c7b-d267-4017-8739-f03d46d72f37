<?php
/**
 * Template Name: Contact
 * 
 * Contact page for ChatGABI
 * Secure contact form with CSRF protection
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
$form_message = '';
$form_status = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['chatgabi_contact_submit'])) {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['chatgabi_contact_nonce'], 'chatgabi_contact_form')) {
        $form_message = __('Security check failed. Please try again.', 'chatgabi');
        $form_status = 'error';
    } else {
        // Sanitize form data
        $name = sanitize_text_field($_POST['contact_name']);
        $email = sanitize_email($_POST['contact_email']);
        $subject = sanitize_text_field($_POST['contact_subject']);
        $message = sanitize_textarea_field($_POST['contact_message']);
        $country = sanitize_text_field($_POST['contact_country']);
        $business_type = sanitize_text_field($_POST['contact_business_type']);
        
        // Validate required fields
        if (empty($name) || empty($email) || empty($subject) || empty($message)) {
            $form_message = __('Please fill in all required fields.', 'chatgabi');
            $form_status = 'error';
        } elseif (!is_email($email)) {
            $form_message = __('Please enter a valid email address.', 'chatgabi');
            $form_status = 'error';
        } else {
            // Prepare email
            $to = '<EMAIL>';
            $email_subject = '[ChatGABI Contact] ' . $subject;
            $email_message = "Name: $name\n";
            $email_message .= "Email: $email\n";
            $email_message .= "Country: $country\n";
            $email_message .= "Business Type: $business_type\n\n";
            $email_message .= "Message:\n$message\n\n";
            $email_message .= "---\n";
            $email_message .= "Sent from ChatGABI Contact Form\n";
            $email_message .= "Time: " . current_time('Y-m-d H:i:s') . "\n";
            $email_message .= "IP: " . $_SERVER['REMOTE_ADDR'] . "\n";
            
            $headers = array(
                'Content-Type: text/plain; charset=UTF-8',
                'From: ChatGABI <<EMAIL>>',
                'Reply-To: ' . $name . ' <' . $email . '>'
            );
            
            // Send email
            if (wp_mail($to, $email_subject, $email_message, $headers)) {
                $form_message = __('Thank you for your message! We will get back to you within 24 hours.', 'chatgabi');
                $form_status = 'success';
                
                // Clear form data on success
                $name = $email = $subject = $message = $country = $business_type = '';
            } else {
                $form_message = __('Sorry, there was an error sending your message. Please try again or email us <NAME_EMAIL>.', 'chatgabi');
                $form_status = 'error';
            }
        }
    }
}

get_header();
?>

<div class="chatgabi-contact-page">
    <div class="container">
        <!-- Contact Hero Section -->
        <div class="contact-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">📞</span>
                    <?php _e('Contact Us', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Get in touch with the ChatGABI team. We\'re here to help you succeed with AI-powered business intelligence across Africa.', 'chatgabi'); ?>
                </p>
            </div>
        </div>

        <div class="contact-content">
            <!-- Contact Form Section -->
            <div class="contact-form-section glassmorphism-card">
                <div class="section-header">
                    <h2><?php _e('Send us a Message', 'chatgabi'); ?></h2>
                    <span class="section-icon">✉️</span>
                </div>
                
                <?php if ($form_message): ?>
                    <div class="form-message <?php echo esc_attr($form_status); ?>">
                        <p><?php echo esc_html($form_message); ?></p>
                    </div>
                <?php endif; ?>
                
                <form method="post" class="contact-form" novalidate>
                    <?php wp_nonce_field('chatgabi_contact_form', 'chatgabi_contact_nonce'); ?>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="contact_name"><?php _e('Full Name', 'chatgabi'); ?> <span class="required">*</span></label>
                            <input type="text" 
                                   id="contact_name" 
                                   name="contact_name" 
                                   value="<?php echo esc_attr($name ?? ''); ?>"
                                   required 
                                   placeholder="<?php esc_attr_e('Enter your full name', 'chatgabi'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_email"><?php _e('Email Address', 'chatgabi'); ?> <span class="required">*</span></label>
                            <input type="email" 
                                   id="contact_email" 
                                   name="contact_email" 
                                   value="<?php echo esc_attr($email ?? ''); ?>"
                                   required 
                                   placeholder="<?php esc_attr_e('Enter your email address', 'chatgabi'); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="contact_country"><?php _e('Country', 'chatgabi'); ?></label>
                            <select id="contact_country" name="contact_country">
                                <option value=""><?php _e('Select your country', 'chatgabi'); ?></option>
                                <option value="Ghana" <?php selected($country ?? '', 'Ghana'); ?>><?php _e('Ghana', 'chatgabi'); ?></option>
                                <option value="Kenya" <?php selected($country ?? '', 'Kenya'); ?>><?php _e('Kenya', 'chatgabi'); ?></option>
                                <option value="Nigeria" <?php selected($country ?? '', 'Nigeria'); ?>><?php _e('Nigeria', 'chatgabi'); ?></option>
                                <option value="South Africa" <?php selected($country ?? '', 'South Africa'); ?>><?php _e('South Africa', 'chatgabi'); ?></option>
                                <option value="Other" <?php selected($country ?? '', 'Other'); ?>><?php _e('Other', 'chatgabi'); ?></option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_business_type"><?php _e('Business Type', 'chatgabi'); ?></label>
                            <select id="contact_business_type" name="contact_business_type">
                                <option value=""><?php _e('Select business type', 'chatgabi'); ?></option>
                                <option value="Startup" <?php selected($business_type ?? '', 'Startup'); ?>><?php _e('Startup', 'chatgabi'); ?></option>
                                <option value="Small Business" <?php selected($business_type ?? '', 'Small Business'); ?>><?php _e('Small Business', 'chatgabi'); ?></option>
                                <option value="Medium Enterprise" <?php selected($business_type ?? '', 'Medium Enterprise'); ?>><?php _e('Medium Enterprise', 'chatgabi'); ?></option>
                                <option value="Large Corporation" <?php selected($business_type ?? '', 'Large Corporation'); ?>><?php _e('Large Corporation', 'chatgabi'); ?></option>
                                <option value="Consultant" <?php selected($business_type ?? '', 'Consultant'); ?>><?php _e('Consultant', 'chatgabi'); ?></option>
                                <option value="Student/Researcher" <?php selected($business_type ?? '', 'Student/Researcher'); ?>><?php _e('Student/Researcher', 'chatgabi'); ?></option>
                                <option value="Other" <?php selected($business_type ?? '', 'Other'); ?>><?php _e('Other', 'chatgabi'); ?></option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_subject"><?php _e('Subject', 'chatgabi'); ?> <span class="required">*</span></label>
                        <input type="text" 
                               id="contact_subject" 
                               name="contact_subject" 
                               value="<?php echo esc_attr($subject ?? ''); ?>"
                               required 
                               placeholder="<?php esc_attr_e('What is your message about?', 'chatgabi'); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_message"><?php _e('Message', 'chatgabi'); ?> <span class="required">*</span></label>
                        <textarea id="contact_message" 
                                  name="contact_message" 
                                  rows="6" 
                                  required 
                                  placeholder="<?php esc_attr_e('Tell us how we can help you...', 'chatgabi'); ?>"><?php echo esc_textarea($message ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-submit">
                        <button type="submit" name="chatgabi_contact_submit" class="btn btn-primary">
                            <span class="btn-icon">📤</span>
                            <?php _e('Send Message', 'chatgabi'); ?>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Contact Information Section -->
            <div class="contact-info-section glassmorphism-card">
                <div class="section-header">
                    <h2><?php _e('Get in Touch', 'chatgabi'); ?></h2>
                    <span class="section-icon">🌍</span>
                </div>
                
                <div class="contact-methods">
                    <div class="contact-method">
                        <span class="method-icon">📧</span>
                        <div class="method-content">
                            <h4><?php _e('Email Support', 'chatgabi'); ?></h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <small><?php _e('Response within 24 hours', 'chatgabi'); ?></small>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <span class="method-icon">🏢</span>
                        <div class="method-content">
                            <h4><?php _e('Headquarters', 'chatgabi'); ?></h4>
                            <p><?php _e('Accra, Ghana', 'chatgabi'); ?></p>
                            <small><?php _e('West Africa Hub', 'chatgabi'); ?></small>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <span class="method-icon">🕒</span>
                        <div class="method-content">
                            <h4><?php _e('Business Hours', 'chatgabi'); ?></h4>
                            <p><?php _e('Monday - Friday: 9 AM - 6 PM GMT', 'chatgabi'); ?></p>
                            <small><?php _e('Weekend support available', 'chatgabi'); ?></small>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <span class="method-icon">💬</span>
                        <div class="method-content">
                            <h4><?php _e('Live Chat', 'chatgabi'); ?></h4>
                            <p><?php _e('Available on our platform', 'chatgabi'); ?></p>
                            <small><?php _e('AI-powered instant support', 'chatgabi'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="contact-faq-section glassmorphism-card">
                <div class="section-header">
                    <h2><?php _e('Frequently Asked Questions', 'chatgabi'); ?></h2>
                    <span class="section-icon">❓</span>
                </div>
                
                <div class="faq-items">
                    <div class="faq-item">
                        <h4><?php _e('How quickly do you respond to inquiries?', 'chatgabi'); ?></h4>
                        <p><?php _e('We typically respond to all inquiries within 24 hours during business days. For urgent technical issues, we aim to respond within 4-6 hours.', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="faq-item">
                        <h4><?php _e('Do you provide support in local African languages?', 'chatgabi'); ?></h4>
                        <p><?php _e('Yes! We provide support in English, Twi, Swahili, Yoruba, and Zulu to better serve our African entrepreneur community.', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="faq-item">
                        <h4><?php _e('Can I schedule a demo of ChatGABI?', 'chatgabi'); ?></h4>
                        <p><?php _e('Absolutely! Contact us to schedule a personalized demo where we\'ll show you how ChatGABI can transform your business intelligence needs.', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="faq-item">
                        <h4><?php _e('Do you offer training and onboarding?', 'chatgabi'); ?></h4>
                        <p><?php _e('Yes, we provide comprehensive onboarding and training to help you maximize the value of ChatGABI for your business needs.', 'chatgabi'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Support Resources Section -->
            <div class="contact-resources-section glassmorphism-card">
                <div class="section-header">
                    <h2><?php _e('Support Resources', 'chatgabi'); ?></h2>
                    <span class="section-icon">📚</span>
                </div>
                
                <div class="resource-links">
                    <div class="resource-category">
                        <h4><?php _e('📖 Documentation', 'chatgabi'); ?></h4>
                        <ul>
                            <li><a href="#user-guide"><?php _e('User Guide', 'chatgabi'); ?></a></li>
                            <li><a href="#api-docs"><?php _e('API Documentation', 'chatgabi'); ?></a></li>
                            <li><a href="#troubleshooting"><?php _e('Troubleshooting', 'chatgabi'); ?></a></li>
                        </ul>
                    </div>
                    
                    <div class="resource-category">
                        <h4><?php _e('🎓 Learning', 'chatgabi'); ?></h4>
                        <ul>
                            <li><a href="#tutorials"><?php _e('Video Tutorials', 'chatgabi'); ?></a></li>
                            <li><a href="#webinars"><?php _e('Webinars', 'chatgabi'); ?></a></li>
                            <li><a href="#best-practices"><?php _e('Best Practices', 'chatgabi'); ?></a></li>
                        </ul>
                    </div>
                    
                    <div class="resource-category">
                        <h4><?php _e('🤝 Community', 'chatgabi'); ?></h4>
                        <ul>
                            <li><a href="#forum"><?php _e('User Forum', 'chatgabi'); ?></a></li>
                            <li><a href="#success-stories"><?php _e('Success Stories', 'chatgabi'); ?></a></li>
                            <li><a href="#feedback"><?php _e('Feature Requests', 'chatgabi'); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Contact Page Specific Styles */
.chatgabi-contact-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 30px;
}

/* Hero Section */
.contact-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    max-width: 700px;
    margin: 0 auto;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--color-secondary-accent);
}

.section-header h2 {
    font-size: 2rem;
    color: var(--color-primary-accent);
    margin: 0;
}

.section-icon {
    font-size: 2rem;
}

/* Contact Content Layout */
.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
}

/* Form Styles */
.form-message {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    font-weight: 600;
}

.form-message.success {
    background: rgba(39, 174, 96, 0.1);
    border: 2px solid #27ae60;
    color: #27ae60;
}

.form-message.error {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid #e74c3c;
    color: #e74c3c;
}

.contact-form {
    max-width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-text-primary);
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--color-borders);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary-accent);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-submit {
    text-align: center;
    margin-top: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1.2rem;
}

/* Contact Methods */
.contact-methods {
    display: grid;
    gap: 25px;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.method-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.method-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.method-content p {
    color: var(--color-text-primary);
    margin-bottom: 5px;
    font-weight: 600;
}

.method-content a {
    color: var(--color-primary-accent);
    text-decoration: none;
}

.method-content a:hover {
    text-decoration: underline;
}

.method-content small {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* FAQ Styles */
.faq-items {
    display: grid;
    gap: 20px;
}

.faq-item {
    padding: 20px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.faq-item h4 {
    color: var(--color-primary-accent);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.faq-item p {
    color: var(--color-text-secondary);
    line-height: 1.6;
}

/* Resource Links */
.resource-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
}

.resource-category h4 {
    color: var(--color-tertiary-accent);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.resource-category ul {
    list-style: none;
    padding: 0;
}

.resource-category li {
    margin-bottom: 8px;
}

.resource-category a {
    color: var(--color-text-primary);
    text-decoration: none;
    padding: 5px 0;
    display: block;
    transition: color 0.3s ease;
}

.resource-category a:hover {
    color: var(--color-primary-accent);
    text-decoration: underline;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-contact-page {
        padding: 20px 0;
    }
    
    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 20px;
    }
    
    .contact-hero {
        padding: 40px 20px;
    }
    
    .hero-title {
        font-size: 2.2rem;
        flex-direction: column;
        gap: 15px;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .contact-method {
        flex-direction: column;
        text-align: center;
    }
    
    .resource-links {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .form-group input,
body.theme-dark .form-group select,
body.theme-dark .form-group textarea {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .contact-method {
    background: rgba(39, 174, 96, 0.2);
}

body.theme-dark .faq-item {
    background: rgba(110, 127, 243, 0.1);
}
</style>

<?php
// Add SEO meta tags
function chatgabi_contact_meta() {
    $title = __('Contact ChatGABI Support - Get Help with AI Business Intelligence', 'chatgabi');
    $description = __('Contact the ChatGABI team for support, demos, and inquiries. Get help with AI-powered business intelligence for African entrepreneurs. Response within 24 hours.', 'chatgabi');
    
    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
}
add_action('wp_head', 'chatgabi_contact_meta');

get_footer();
?>
