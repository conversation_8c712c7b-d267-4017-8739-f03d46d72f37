-- ChatGABI Test Database Setup
-- Run this in phpMyAdmin after starting XAMPP

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS chatgabi_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE chatgabi_test;

-- WordPress core tables (minimal setup)
CREATE TABLE IF NOT EXISTS wp_users (
    ID bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    user_login varchar(60) NOT NULL DEFAULT '',
    user_pass varchar(255) NOT NULL DEFAULT '',
    user_nicename varchar(50) NOT NULL DEFAULT '',
    user_email varchar(100) NOT NULL DEFAULT '',
    user_url varchar(100) NOT NULL DEFAULT '',
    user_registered datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    user_activation_key varchar(255) NOT NULL DEFAULT '',
    user_status int(11) NOT NULL DEFAULT '0',
    display_name varchar(250) NOT NULL DEFAULT '',
    <PERSON>IMAR<PERSON>EY (ID),
    <PERSON><PERSON>Y user_login_key (user_login),
    KEY user_nicename (user_nicename),
    KEY user_email (user_email)
);

CREATE TABLE IF NOT EXISTS wp_usermeta (
    umeta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    user_id bigint(20) unsigned NOT NULL DEFAULT '0',
    meta_key varchar(255) DEFAULT NULL,
    meta_value longtext,
    PRIMARY KEY (umeta_id),
    KEY user_id (user_id),
    KEY meta_key (meta_key(191))
);

CREATE TABLE IF NOT EXISTS wp_posts (
    ID bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    post_author bigint(20) unsigned NOT NULL DEFAULT '0',
    post_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    post_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    post_content longtext NOT NULL,
    post_title text NOT NULL,
    post_excerpt text NOT NULL,
    post_status varchar(20) NOT NULL DEFAULT 'publish',
    comment_status varchar(20) NOT NULL DEFAULT 'open',
    ping_status varchar(20) NOT NULL DEFAULT 'open',
    post_password varchar(255) NOT NULL DEFAULT '',
    post_name varchar(200) NOT NULL DEFAULT '',
    to_ping text NOT NULL,
    pinged text NOT NULL,
    post_modified datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    post_modified_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    post_content_filtered longtext NOT NULL,
    post_parent bigint(20) unsigned NOT NULL DEFAULT '0',
    guid varchar(255) NOT NULL DEFAULT '',
    menu_order int(11) NOT NULL DEFAULT '0',
    post_type varchar(20) NOT NULL DEFAULT 'post',
    post_mime_type varchar(100) NOT NULL DEFAULT '',
    comment_count bigint(20) NOT NULL DEFAULT '0',
    PRIMARY KEY (ID),
    KEY post_name (post_name(191)),
    KEY type_status_date (post_type,post_status,post_date,ID),
    KEY post_parent (post_parent),
    KEY post_author (post_author)
);

CREATE TABLE IF NOT EXISTS wp_postmeta (
    meta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    post_id bigint(20) unsigned NOT NULL DEFAULT '0',
    meta_key varchar(255) DEFAULT NULL,
    meta_value longtext,
    PRIMARY KEY (meta_id),
    KEY post_id (post_id),
    KEY meta_key (meta_key(191))
);

-- ChatGABI specific tables
CREATE TABLE IF NOT EXISTS wp_chatgabi_conversions (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    event_type varchar(100) NOT NULL,
    event_data longtext,
    user_id bigint(20) DEFAULT 0,
    session_id varchar(100),
    page_url text,
    referrer_url text,
    ip_address varchar(45),
    user_agent text,
    timestamp datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY event_type (event_type),
    KEY user_id (user_id),
    KEY timestamp (timestamp),
    KEY session_id (session_id)
);

-- A/B Testing tables
CREATE TABLE IF NOT EXISTS wp_chatgabi_ab_assignments (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_identifier varchar(100) NOT NULL,
    test_name varchar(100) NOT NULL,
    variant varchar(50) NOT NULL,
    assigned_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_test (user_identifier, test_name),
    KEY test_name (test_name),
    KEY assigned_at (assigned_at)
);

CREATE TABLE IF NOT EXISTS wp_chatgabi_ab_exposures (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    test_name varchar(100) NOT NULL,
    variant varchar(50) NOT NULL,
    user_identifier varchar(100) NOT NULL,
    ip_address varchar(45),
    user_agent text,
    timestamp datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY test_variant (test_name, variant),
    KEY timestamp (timestamp)
);

CREATE TABLE IF NOT EXISTS wp_chatgabi_ab_conversions (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    test_name varchar(100) NOT NULL,
    variant varchar(50) NOT NULL,
    user_identifier varchar(100) NOT NULL,
    conversion_type varchar(100) DEFAULT 'default',
    conversion_value decimal(10,2) DEFAULT 0,
    ip_address varchar(45),
    user_agent text,
    timestamp datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY test_variant (test_name, variant),
    KEY conversion_type (conversion_type),
    KEY timestamp (timestamp)
);

-- Lead Magnets table
CREATE TABLE IF NOT EXISTS wp_chatgabi_leads (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    email varchar(255) NOT NULL,
    name varchar(255),
    country varchar(10),
    magnet_id varchar(100),
    downloads text,
    first_download datetime,
    last_download datetime,
    download_count int DEFAULT 1,
    ip_address varchar(45),
    user_agent text,
    subscribed tinyint(1) DEFAULT 1,
    unsubscribed_at datetime NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY email (email),
    KEY country (country),
    KEY magnet_id (magnet_id),
    KEY created_at (created_at)
);

-- Insert test data
INSERT INTO wp_chatgabi_conversions (event_type, event_data, user_id, page_url, ip_address, timestamp) VALUES
('homepage_view', '{"country":"GH","language":"en"}', 0, 'http://localhost/chatgabi/', '127.0.0.1', NOW()),
('signup_started', '{"source":"hero_cta"}', 0, 'http://localhost/chatgabi/', '127.0.0.1', NOW()),
('roi_calculator_used', '{"business_type":"startup","industry":"technology","country":"GH"}', 0, 'http://localhost/chatgabi/', '127.0.0.1', NOW()),
('lead_magnet_download', '{"magnet_id":"business-plan-template","email":"<EMAIL>"}', 0, 'http://localhost/chatgabi/', '127.0.0.1', NOW());

-- Insert test A/B assignments
INSERT INTO wp_chatgabi_ab_assignments (user_identifier, test_name, variant) VALUES
('anon_test123', 'hero_cta_test', 'variant_a'),
('anon_test456', 'pricing_display_test', 'control'),
('anon_test789', 'testimonial_layout_test', 'variant_b');

-- Insert test exposures
INSERT INTO wp_chatgabi_ab_exposures (test_name, variant, user_identifier, ip_address) VALUES
('hero_cta_test', 'variant_a', 'anon_test123', '127.0.0.1'),
('pricing_display_test', 'control', 'anon_test456', '127.0.0.1'),
('testimonial_layout_test', 'variant_b', 'anon_test789', '127.0.0.1');

-- Insert test leads
INSERT INTO wp_chatgabi_leads (email, name, country, magnet_id, downloads, first_download, last_download) VALUES
('<EMAIL>', 'John Doe', 'GH', 'business-plan-template', 'business-plan-template', NOW(), NOW()),
('<EMAIL>', 'Jane Smith', 'KE', 'financial-planning-toolkit', 'financial-planning-toolkit', NOW(), NOW()),
('<EMAIL>', 'Mike Johnson', 'NG', 'african-market-guide', 'african-market-guide', NOW(), NOW());

SELECT 'Database setup complete!' as status;
