<?php
/**
 * Enhanced Testimonials System
 * 
 * Manages expanded testimonials with business verification,
 * case studies, and rotating testimonial displays for conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize enhanced testimonials system
 */
function chatgabi_init_enhanced_testimonials() {
    // Add AJAX handlers for testimonial rotation
    add_action('wp_ajax_chatgabi_get_testimonials', 'chatgabi_get_testimonials_ajax');
    add_action('wp_ajax_nopriv_chatgabi_get_testimonials', 'chatgabi_get_testimonials_ajax');
    
    // Enqueue testimonials scripts
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_testimonials_scripts');
}
add_action('init', 'chatgabi_init_enhanced_testimonials');

/**
 * Enqueue testimonials scripts and styles
 */
function chatgabi_enqueue_testimonials_scripts() {
    if (is_front_page()) {
        wp_enqueue_script(
            'chatgabi-enhanced-testimonials',
            CHATGABI_THEME_URL . '/assets/js/enhanced-testimonials.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );
        
        wp_localize_script('chatgabi-enhanced-testimonials', 'chatgabiTestimonials', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_testimonials_nonce'),
            'rotationInterval' => 8000, // 8 seconds
            'strings' => array(
                'loading' => __('Loading testimonials...', 'chatgabi'),
                'verified_business' => __('Verified Business', 'chatgabi'),
                'case_study' => __('View Case Study', 'chatgabi')
            )
        ));
    }
}

/**
 * Get comprehensive testimonials data
 */
function chatgabi_get_testimonials_data() {
    return array(
        array(
            'id' => 1,
            'name' => 'Akosua Mensah',
            'title' => 'Founder, Kente Kreations',
            'country' => 'Ghana',
            'country_code' => 'GH',
            'business_type' => 'Fashion & Textiles',
            'quote' => __('ChatGABI helped me create a comprehensive business plan in Twi. The AI understood my local market needs perfectly and helped me secure $15,000 in funding.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://kentekreations.com.gh',
            'case_study_url' => '#case-study-akosua',
            'results' => array(
                'funding_secured' => '$15,000',
                'revenue_increase' => '250%',
                'time_saved' => '3 weeks'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/akosua-mensah.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/kente-kreations-logo.png'
        ),
        array(
            'id' => 2,
            'name' => 'James Ochieng',
            'title' => 'CEO, Nairobi Tech Solutions',
            'country' => 'Kenya',
            'country_code' => 'KE',
            'business_type' => 'Technology',
            'quote' => __('The financial forecasting feature saved me weeks of work. I got investor-ready projections in minutes and closed a $50,000 Series A round.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://nairobitecsolutions.co.ke',
            'case_study_url' => '#case-study-james',
            'results' => array(
                'funding_secured' => '$50,000',
                'revenue_increase' => '180%',
                'time_saved' => '2 weeks'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/james-ochieng.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/nairobi-tech-logo.png'
        ),
        array(
            'id' => 3,
            'name' => 'Adunni Adebayo',
            'title' => 'Managing Director, Lagos Beauty Hub',
            'country' => 'Nigeria',
            'country_code' => 'NG',
            'business_type' => 'Beauty & Cosmetics',
            'quote' => __('Getting marketing strategies in Yoruba made it easier to implement locally. My sales increased by 40% in just 3 months using ChatGABI\'s recommendations.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://lagosbeautyhub.ng',
            'case_study_url' => '#case-study-adunni',
            'results' => array(
                'sales_increase' => '40%',
                'customer_growth' => '300%',
                'time_saved' => '1 week'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/adunni-adebayo.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/lagos-beauty-logo.png'
        ),
        array(
            'id' => 4,
            'name' => 'Thabo Mthembu',
            'title' => 'Founder, Cape Town Logistics',
            'country' => 'South Africa',
            'country_code' => 'ZA',
            'business_type' => 'Logistics & Transport',
            'quote' => __('ChatGABI\'s business plan helped me understand the South African logistics market better. I expanded to 3 cities and doubled my fleet in 6 months.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://capetownlogistics.co.za',
            'case_study_url' => '#case-study-thabo',
            'results' => array(
                'expansion' => '3 cities',
                'fleet_growth' => '100%',
                'revenue_increase' => '220%'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/thabo-mthembu.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/cape-town-logistics-logo.png'
        ),
        array(
            'id' => 5,
            'name' => 'Fatima Al-Hassan',
            'title' => 'CEO, Kano Agricultural Solutions',
            'country' => 'Nigeria',
            'country_code' => 'NG',
            'business_type' => 'Agriculture',
            'quote' => __('The market analysis for agricultural products in Northern Nigeria was incredibly detailed. ChatGABI helped me identify untapped opportunities worth ₦5M.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://kanoagrisolutions.ng',
            'case_study_url' => '#case-study-fatima',
            'results' => array(
                'opportunities_identified' => '₦5M',
                'market_expansion' => '5 states',
                'profit_increase' => '160%'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/fatima-al-hassan.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/kano-agri-logo.png'
        ),
        array(
            'id' => 6,
            'name' => 'Samuel Kwame',
            'title' => 'Director, Accra FinTech Hub',
            'country' => 'Ghana',
            'country_code' => 'GH',
            'business_type' => 'Financial Technology',
            'quote' => __('ChatGABI\'s financial projections were so accurate, our investors were impressed. We raised $100,000 seed funding in record time.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://accrafintechhub.com',
            'case_study_url' => '#case-study-samuel',
            'results' => array(
                'funding_secured' => '$100,000',
                'investor_meetings' => '12',
                'time_to_funding' => '6 weeks'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/samuel-kwame.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/accra-fintech-logo.png'
        ),
        array(
            'id' => 7,
            'name' => 'Grace Wanjiku',
            'title' => 'Founder, Mombasa Eco Tours',
            'country' => 'Kenya',
            'country_code' => 'KE',
            'business_type' => 'Tourism & Hospitality',
            'quote' => __('The tourism business plan in Swahili helped me connect with local communities. My eco-tourism business now serves 500+ visitors monthly.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://mombasaecotours.co.ke',
            'case_study_url' => '#case-study-grace',
            'results' => array(
                'monthly_visitors' => '500+',
                'community_partnerships' => '8',
                'revenue_growth' => '300%'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/grace-wanjiku.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/mombasa-eco-logo.png'
        ),
        array(
            'id' => 8,
            'name' => 'Olumide Fashola',
            'title' => 'CEO, Ibadan Food Processing',
            'country' => 'Nigeria',
            'country_code' => 'NG',
            'business_type' => 'Food Processing',
            'quote' => __('ChatGABI helped me scale from a small local operation to supplying 50+ supermarkets across Southwest Nigeria. The growth strategy was perfect.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://ibadanfoodprocessing.ng',
            'case_study_url' => '#case-study-olumide',
            'results' => array(
                'supermarket_clients' => '50+',
                'production_increase' => '400%',
                'staff_growth' => '25 employees'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/olumide-fashola.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/ibadan-food-logo.png'
        ),
        array(
            'id' => 9,
            'name' => 'Nomsa Dlamini',
            'title' => 'Managing Director, Durban Health Services',
            'country' => 'South Africa',
            'country_code' => 'ZA',
            'business_type' => 'Healthcare',
            'quote' => __('The healthcare business model in Zulu helped me understand community needs better. We now serve 10,000+ patients across KwaZulu-Natal.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://durbanhealthservices.co.za',
            'case_study_url' => '#case-study-nomsa',
            'results' => array(
                'patients_served' => '10,000+',
                'clinics_opened' => '5',
                'community_impact' => '95% satisfaction'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/nomsa-dlamini.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/durban-health-logo.png'
        ),
        array(
            'id' => 10,
            'name' => 'Kofi Asante',
            'title' => 'Founder, Kumasi Solar Solutions',
            'country' => 'Ghana',
            'country_code' => 'GH',
            'business_type' => 'Renewable Energy',
            'quote' => __('ChatGABI\'s renewable energy business plan helped me secure government contracts worth $200,000. The environmental impact projections were crucial.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://kumasisolarsolutions.com',
            'case_study_url' => '#case-study-kofi',
            'results' => array(
                'government_contracts' => '$200,000',
                'solar_installations' => '150+',
                'co2_reduction' => '500 tons/year'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/kofi-asante.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/kumasi-solar-logo.png'
        ),
        array(
            'id' => 11,
            'name' => 'Amina Kone',
            'title' => 'CEO, Abidjan Fashion House',
            'country' => 'Côte d\'Ivoire',
            'country_code' => 'CI',
            'business_type' => 'Fashion Design',
            'quote' => __('Even though I\'m in Côte d\'Ivoire, ChatGABI\'s West African market insights helped me expand to Ghana and Nigeria. Revenue increased 180%.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://abidjanfashionhouse.ci',
            'case_study_url' => '#case-study-amina',
            'results' => array(
                'market_expansion' => '3 countries',
                'revenue_increase' => '180%',
                'fashion_shows' => '12'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/amina-kone.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/abidjan-fashion-logo.png'
        ),
        array(
            'id' => 12,
            'name' => 'David Mwangi',
            'title' => 'Director, Kisumu Transport Co.',
            'country' => 'Kenya',
            'country_code' => 'KE',
            'business_type' => 'Transportation',
            'quote' => __('ChatGABI\'s logistics optimization saved me 30% on fuel costs and improved delivery times by 40%. My customers are much happier now.', 'chatgabi'),
            'rating' => 5,
            'verified' => true,
            'business_url' => 'https://kisumutransport.co.ke',
            'case_study_url' => '#case-study-david',
            'results' => array(
                'fuel_savings' => '30%',
                'delivery_improvement' => '40%',
                'customer_satisfaction' => '98%'
            ),
            'avatar' => CHATGABI_THEME_URL . '/assets/images/testimonials/david-mwangi.jpg',
            'business_logo' => CHATGABI_THEME_URL . '/assets/images/testimonials/kisumu-transport-logo.png'
        )
    );
}

/**
 * AJAX handler for getting testimonials
 */
function chatgabi_get_testimonials_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_testimonials_nonce')) {
        wp_die('Security check failed');
    }
    
    $testimonials = chatgabi_get_testimonials_data();
    
    // Get random subset for rotation
    $count = isset($_POST['count']) ? intval($_POST['count']) : 3;
    $random_testimonials = array_rand($testimonials, min($count, count($testimonials)));
    
    if (!is_array($random_testimonials)) {
        $random_testimonials = array($random_testimonials);
    }
    
    $selected_testimonials = array();
    foreach ($random_testimonials as $index) {
        $selected_testimonials[] = $testimonials[$index];
    }
    
    wp_send_json_success($selected_testimonials);
}

/**
 * Get testimonials by country
 */
function chatgabi_get_testimonials_by_country($country_code) {
    $all_testimonials = chatgabi_get_testimonials_data();
    return array_filter($all_testimonials, function($testimonial) use ($country_code) {
        return $testimonial['country_code'] === $country_code;
    });
}

/**
 * Get testimonials by business type
 */
function chatgabi_get_testimonials_by_business_type($business_type) {
    $all_testimonials = chatgabi_get_testimonials_data();
    return array_filter($all_testimonials, function($testimonial) use ($business_type) {
        return stripos($testimonial['business_type'], $business_type) !== false;
    });
}
