<?php
/**
 * Enhanced Hero Section Template Part
 * 
 * Optimized hero section with improved value proposition,
 * trust indicators, and conversion-focused CTAs for African market.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user data for personalization
$user_country = chatgabi_get_user_country();
$user_language = chatgabi_get_user_preferred_language();
$is_logged_in = is_user_logged_in();
$user_credits = 0;

if ($is_logged_in) {
    $user_credits = get_user_meta(get_current_user_id(), 'businesscraft_credits', true) ?: 0;
}

// Country-specific messaging
$country_messages = array(
    'GH' => array(
        'headline' => __('Generate Professional Business Plans in 5 Minutes - In Twi or English', 'chatgabi'),
        'benefit' => __('Join 3,000+ Ghanaian entrepreneurs building successful businesses with AI', 'chatgabi'),
        'cta_primary' => __('Start Free - Get 50 Credits', 'chatgabi'),
        'cta_secondary' => __('See Live Demo', 'chatgabi'),
        'urgency' => __('🔥 Limited Time: Double credits for new Ghanaian users', 'chatgabi')
    ),
    'KE' => array(
        'headline' => __('Create Investor-Ready Business Plans in Minutes - Swahili & English', 'chatgabi'),
        'benefit' => __('Join 2,500+ Kenyan entrepreneurs securing funding with AI-powered plans', 'chatgabi'),
        'cta_primary' => __('Anza Bure - Pata Credits 50', 'chatgabi'),
        'cta_secondary' => __('Ona Demo', 'chatgabi'),
        'urgency' => __('🚀 Special: Free market analysis for Kenyan startups', 'chatgabi')
    ),
    'NG' => array(
        'headline' => __('Build Million-Naira Business Plans in 5 Minutes - Yoruba & English', 'chatgabi'),
        'benefit' => __('Join 4,200+ Nigerian entrepreneurs scaling with AI business intelligence', 'chatgabi'),
        'cta_primary' => __('Start Free - Get 50 Credits', 'chatgabi'),
        'cta_secondary' => __('Watch Demo', 'chatgabi'),
        'urgency' => __('💰 Limited: Free financial projections for Nigerian businesses', 'chatgabi')
    ),
    'ZA' => array(
        'headline' => __('Generate Professional Business Plans in Minutes - Zulu & English', 'chatgabi'),
        'benefit' => __('Join 1,800+ South African entrepreneurs building with AI assistance', 'chatgabi'),
        'cta_primary' => __('Start Free - Get 50 Credits', 'chatgabi'),
        'cta_secondary' => __('View Demo', 'chatgabi'),
        'urgency' => __('🎯 Special: Free BEE compliance guidance included', 'chatgabi')
    )
);

$current_message = $country_messages[$user_country] ?? $country_messages['GH'];
?>

<section class="enhanced-hero-section">
    <div class="container">
        <div class="hero-content-wrapper">
            
            <!-- Hero Content -->
            <div class="hero-content">
                
                <!-- Urgency Banner -->
                <div class="urgency-banner">
                    <span class="urgency-icon">⚡</span>
                    <span class="urgency-text"><?php echo esc_html($current_message['urgency']); ?></span>
                </div>
                
                <!-- Main Headline -->
                <h1 class="hero-headline">
                    <?php echo esc_html($current_message['headline']); ?>
                </h1>
                
                <!-- Benefit Statement -->
                <p class="hero-benefit">
                    <?php echo esc_html($current_message['benefit']); ?>
                </p>
                
                <!-- Key Benefits List -->
                <div class="hero-benefits-list">
                    <div class="benefit-item">
                        <span class="benefit-icon">⚡</span>
                        <span class="benefit-text"><?php _e('5-minute business plans', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">💰</span>
                        <span class="benefit-text"><?php _e('Save $2,000+ in consulting fees', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🌍</span>
                        <span class="benefit-text"><?php _e('African market expertise built-in', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🗣️</span>
                        <span class="benefit-text"><?php _e('Available in 5 African languages', 'chatgabi'); ?></span>
                    </div>
                </div>
                
                <!-- CTA Buttons -->
                <div class="hero-cta-section">
                    <div class="cta-buttons">
                        <?php if ($is_logged_in): ?>
                            <a href="#chat-demo" class="cta-primary" id="hero-cta-primary">
                                <span class="cta-text"><?php _e('Start Creating Now', 'chatgabi'); ?></span>
                                <span class="cta-subtext"><?php printf(__('You have %d credits', 'chatgabi'), $user_credits); ?></span>
                            </a>
                            <a href="<?php echo home_url('/templates'); ?>" class="cta-secondary" id="hero-cta-secondary">
                                <span class="cta-text"><?php _e('Browse Templates', 'chatgabi'); ?></span>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo wp_registration_url(); ?>" class="cta-primary" id="hero-cta-primary">
                                <span class="cta-text"><?php echo esc_html($current_message['cta_primary']); ?></span>
                                <span class="cta-subtext"><?php _e('No credit card required', 'chatgabi'); ?></span>
                            </a>
                            <a href="#chat-demo" class="cta-secondary" id="hero-cta-secondary">
                                <span class="cta-text"><?php echo esc_html($current_message['cta_secondary']); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Trust Indicators -->
                    <div class="hero-trust-indicators">
                        <div class="trust-item">
                            <span class="trust-icon">✅</span>
                            <span class="trust-text"><?php _e('No credit card required', 'chatgabi'); ?></span>
                        </div>
                        <div class="trust-item">
                            <span class="trust-icon">🔒</span>
                            <span class="trust-text"><?php _e('Bank-level security', 'chatgabi'); ?></span>
                        </div>
                        <div class="trust-item">
                            <span class="trust-icon">⭐</span>
                            <span class="trust-text"><?php _e('4.9/5 user rating', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
                
            </div>
            
            <!-- Hero Visual -->
            <div class="hero-visual">
                
                <!-- Success Metrics -->
                <div class="hero-metrics">
                    <div class="metric-card">
                        <div class="metric-number" data-count="10000">0+</div>
                        <div class="metric-label"><?php _e('Entrepreneurs', 'chatgabi'); ?></div>
                        <div class="metric-flag">🌍</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" data-count="5">0</div>
                        <div class="metric-label"><?php _e('Languages', 'chatgabi'); ?></div>
                        <div class="metric-flag">🗣️</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" data-count="4">0</div>
                        <div class="metric-label"><?php _e('Countries', 'chatgabi'); ?></div>
                        <div class="metric-flag">🏛️</div>
                    </div>
                </div>
                
                <!-- Visual Illustration -->
                <div class="hero-illustration">
                    <div class="illustration-container">
                        <!-- African Business Success Visual -->
                        <div class="business-visual">
                            <div class="business-icon">📊</div>
                            <div class="success-indicators">
                                <div class="indicator active">📈</div>
                                <div class="indicator active">💰</div>
                                <div class="indicator active">🎯</div>
                            </div>
                        </div>
                        
                        <!-- Floating Elements -->
                        <div class="floating-element" style="--delay: 0s;">
                            <span class="element-icon">🚀</span>
                            <span class="element-text"><?php _e('Business Plan', 'chatgabi'); ?></span>
                        </div>
                        <div class="floating-element" style="--delay: 1s;">
                            <span class="element-icon">📊</span>
                            <span class="element-text"><?php _e('Market Analysis', 'chatgabi'); ?></span>
                        </div>
                        <div class="floating-element" style="--delay: 2s;">
                            <span class="element-icon">💰</span>
                            <span class="element-text"><?php _e('Financial Forecast', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>
    </div>
    
    <!-- Background Elements -->
    <div class="hero-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
    </div>
</section>

<!-- Mobile Sticky CTA -->
<div class="mobile-sticky-cta" id="mobile-sticky-cta">
    <div class="sticky-cta-content">
        <div class="sticky-cta-text">
            <span class="sticky-cta-title"><?php _e('Ready to start?', 'chatgabi'); ?></span>
            <span class="sticky-cta-subtitle"><?php _e('50 free credits waiting', 'chatgabi'); ?></span>
        </div>
        <a href="<?php echo $is_logged_in ? '#chat-demo' : wp_registration_url(); ?>" class="sticky-cta-button">
            <?php echo $is_logged_in ? __('Start Now', 'chatgabi') : __('Get Started', 'chatgabi'); ?>
        </a>
    </div>
</div>

<style>
/* Enhanced Hero Section Styles */
.enhanced-hero-section {
    background: linear-gradient(135deg, #3D4E81 0%, #5753C9 50%, #6E7FF3 100%);
    color: white;
    padding: 4rem 0 6rem 0;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

/* Urgency Banner */
.urgency-banner {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.4);
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
}

.urgency-icon {
    font-size: 1.2rem;
}

.urgency-text {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Hero Headline */
.hero-headline {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #FFFFFF, #FFD700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-benefit {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    line-height: 1.5;
}

/* Benefits List */
.hero-benefits-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.benefit-icon {
    font-size: 1.5rem;
}

.benefit-text {
    font-weight: 600;
    font-size: 0.95rem;
}

/* CTA Section */
.hero-cta-section {
    margin-top: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.cta-primary, .cta-secondary {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.cta-primary {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #2c3e50;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
    color: #2c3e50;
}

.cta-secondary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    color: white;
}

.cta-text {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.cta-subtext {
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Trust Indicators */
.hero-trust-indicators {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.trust-icon {
    font-size: 1rem;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.metric-number {
    font-size: 2rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    color: #FFD700;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.metric-flag {
    font-size: 1.2rem;
}

/* Hero Illustration */
.hero-illustration {
    position: relative;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.illustration-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.business-visual {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.business-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.success-indicators {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.indicator {
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

.indicator:nth-child(2) { animation-delay: 0.5s; }
.indicator:nth-child(3) { animation-delay: 1s; }

@keyframes bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    animation: float-around 6s ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-element:nth-child(1) { top: 20%; right: 10%; }
.floating-element:nth-child(2) { bottom: 30%; left: 5%; }
.floating-element:nth-child(3) { top: 60%; right: 20%; }

@keyframes float-around {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-15px) rotate(2deg); }
    66% { transform: translateY(10px) rotate(-2deg); }
}

/* Background Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: pattern-move 20s linear infinite;
}

@keyframes pattern-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 215, 0, 0.1) 50%, transparent 100%);
}

/* Mobile Sticky CTA */
.mobile-sticky-cta {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(45deg, #3D4E81, #5753C9);
    padding: 1rem;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    display: none;
}

.mobile-sticky-cta.show {
    transform: translateY(0);
}

.sticky-cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 400px;
    margin: 0 auto;
}

.sticky-cta-text {
    display: flex;
    flex-direction: column;
    color: white;
}

.sticky-cta-title {
    font-weight: 700;
    font-size: 1rem;
}

.sticky-cta-subtitle {
    font-size: 0.85rem;
    opacity: 0.9;
}

.sticky-cta-button {
    background: #FFD700;
    color: #2c3e50;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-hero-section {
        padding: 3rem 0 4rem 0;
        min-height: 70vh;
    }
    
    .hero-content-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-benefits-list {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-primary, .cta-secondary {
        width: 100%;
        max-width: 300px;
    }
    
    .hero-trust-indicators {
        justify-content: center;
        gap: 1rem;
    }
    
    .hero-metrics {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
    
    .metric-card {
        padding: 1rem 0.5rem;
    }
    
    .metric-number {
        font-size: 1.5rem;
    }
    
    .mobile-sticky-cta {
        display: block;
    }
}

@media (max-width: 480px) {
    .hero-headline {
        font-size: 2rem;
    }
    
    .hero-benefit {
        font-size: 1.1rem;
    }
    
    .benefit-item {
        padding: 0.5rem;
    }
    
    .hero-metrics {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}
</style>

<script>
// Enhanced Hero Section JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Animate metric counters
    const metricNumbers = document.querySelectorAll('.metric-number[data-count]');
    metricNumbers.forEach(function(element) {
        const target = parseInt(element.getAttribute('data-count'));
        const suffix = element.textContent.includes('+') ? '+' : '';
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                element.textContent = target + suffix;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current) + suffix;
            }
        }, 40);
    });
    
    // Track CTA clicks
    document.getElementById('hero-cta-primary')?.addEventListener('click', function() {
        if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
            window.ChatGABI.ConversionTracker.trackCTAClick('primary', 'hero');
        }
    });
    
    document.getElementById('hero-cta-secondary')?.addEventListener('click', function() {
        if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
            window.ChatGABI.ConversionTracker.trackCTAClick('secondary', 'hero');
        }
    });
    
    // Mobile sticky CTA
    const stickyCTA = document.getElementById('mobile-sticky-cta');
    if (stickyCTA && window.innerWidth <= 768) {
        let hasShown = false;
        window.addEventListener('scroll', function() {
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
            
            if (scrollPercent > 20 && !hasShown) {
                stickyCTA.classList.add('show');
                hasShown = true;
            } else if (scrollPercent < 10 && hasShown) {
                stickyCTA.classList.remove('show');
                hasShown = false;
            }
        });
    }
});
</script>
