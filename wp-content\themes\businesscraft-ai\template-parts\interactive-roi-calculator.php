<?php
/**
 * Interactive ROI Calculator Template Part
 * 
 * Advanced ROI calculator with African market customization,
 * real-time calculations, and lead generation integration.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user country and currency
$user_country = chatgabi_get_user_country();
$user_currency = chatgabi_get_user_currency();
?>

<section class="interactive-roi-calculator-section">
    <div class="container">
        
        <!-- Section Header -->
        <div class="roi-calculator-header">
            <h2><?php _e('Calculate Your ROI with ChatGABI', 'chatgabi'); ?></h2>
            <p class="roi-subtitle">
                <?php _e('See how much time and money ChatGABI can save your business. Get personalized calculations based on your industry and location.', 'chatgabi'); ?>
            </p>
        </div>
        
        <!-- ROI Calculator Widget -->
        <div class="roi-calculator-widget">
            <div class="calculator-container">
                
                <!-- Input Section -->
                <div class="calculator-inputs">
                    <h3><?php _e('Tell us about your business', 'chatgabi'); ?></h3>
                    
                    <!-- Business Type -->
                    <div class="input-group">
                        <label for="business-type"><?php _e('Business Type', 'chatgabi'); ?></label>
                        <select id="business-type" class="calculator-input">
                            <option value="startup"><?php _e('Startup (0-2 years)', 'chatgabi'); ?></option>
                            <option value="small"><?php _e('Small Business (3-10 employees)', 'chatgabi'); ?></option>
                            <option value="medium"><?php _e('Medium Business (11-50 employees)', 'chatgabi'); ?></option>
                            <option value="large"><?php _e('Large Business (50+ employees)', 'chatgabi'); ?></option>
                        </select>
                    </div>
                    
                    <!-- Industry -->
                    <div class="input-group">
                        <label for="industry"><?php _e('Industry', 'chatgabi'); ?></label>
                        <select id="industry" class="calculator-input">
                            <option value="technology"><?php _e('Technology', 'chatgabi'); ?></option>
                            <option value="agriculture"><?php _e('Agriculture', 'chatgabi'); ?></option>
                            <option value="retail"><?php _e('Retail & E-commerce', 'chatgabi'); ?></option>
                            <option value="manufacturing"><?php _e('Manufacturing', 'chatgabi'); ?></option>
                            <option value="services"><?php _e('Professional Services', 'chatgabi'); ?></option>
                            <option value="healthcare"><?php _e('Healthcare', 'chatgabi'); ?></option>
                            <option value="education"><?php _e('Education', 'chatgabi'); ?></option>
                            <option value="finance"><?php _e('Finance', 'chatgabi'); ?></option>
                            <option value="tourism"><?php _e('Tourism & Hospitality', 'chatgabi'); ?></option>
                            <option value="other"><?php _e('Other', 'chatgabi'); ?></option>
                        </select>
                    </div>
                    
                    <!-- Country -->
                    <div class="input-group">
                        <label for="country"><?php _e('Country', 'chatgabi'); ?></label>
                        <select id="country" class="calculator-input">
                            <option value="GH" <?php selected($user_country, 'GH'); ?>>🇬🇭 <?php _e('Ghana', 'chatgabi'); ?></option>
                            <option value="KE" <?php selected($user_country, 'KE'); ?>>🇰🇪 <?php _e('Kenya', 'chatgabi'); ?></option>
                            <option value="NG" <?php selected($user_country, 'NG'); ?>>🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?></option>
                            <option value="ZA" <?php selected($user_country, 'ZA'); ?>>🇿🇦 <?php _e('South Africa', 'chatgabi'); ?></option>
                            <option value="CI">🇨🇮 <?php _e('Côte d\'Ivoire', 'chatgabi'); ?></option>
                            <option value="SN">🇸🇳 <?php _e('Senegal', 'chatgabi'); ?></option>
                            <option value="OTHER"><?php _e('Other African Country', 'chatgabi'); ?></option>
                        </select>
                    </div>
                    
                    <!-- Current Consultant Costs -->
                    <div class="input-group">
                        <label for="consultant-cost"><?php _e('Monthly Consultant/Advisory Costs', 'chatgabi'); ?></label>
                        <div class="input-with-currency">
                            <span class="currency-symbol" id="currency-symbol"><?php echo esc_html($user_currency['symbol']); ?></span>
                            <input type="range" 
                                   id="consultant-cost" 
                                   class="calculator-slider" 
                                   min="0" 
                                   max="5000" 
                                   value="500" 
                                   step="50">
                            <span class="slider-value" id="consultant-cost-value">500</span>
                        </div>
                        <div class="input-help"><?php _e('How much do you currently spend on business consultants or advisors per month?', 'chatgabi'); ?></div>
                    </div>
                    
                    <!-- Time Spent on Planning -->
                    <div class="input-group">
                        <label for="planning-hours"><?php _e('Hours Spent on Business Planning (Monthly)', 'chatgabi'); ?></label>
                        <div class="input-with-unit">
                            <input type="range" 
                                   id="planning-hours" 
                                   class="calculator-slider" 
                                   min="5" 
                                   max="100" 
                                   value="20" 
                                   step="5">
                            <span class="slider-value" id="planning-hours-value">20</span>
                            <span class="unit-label"><?php _e('hours', 'chatgabi'); ?></span>
                        </div>
                        <div class="input-help"><?php _e('Time you and your team spend on business planning, strategy, and analysis', 'chatgabi'); ?></div>
                    </div>
                    
                    <!-- Hourly Rate -->
                    <div class="input-group">
                        <label for="hourly-rate"><?php _e('Your Hourly Rate', 'chatgabi'); ?></label>
                        <div class="input-with-currency">
                            <span class="currency-symbol"><?php echo esc_html($user_currency['symbol']); ?></span>
                            <input type="range" 
                                   id="hourly-rate" 
                                   class="calculator-slider" 
                                   min="5" 
                                   max="200" 
                                   value="25" 
                                   step="5">
                            <span class="slider-value" id="hourly-rate-value">25</span>
                        </div>
                        <div class="input-help"><?php _e('What you value your time at per hour', 'chatgabi'); ?></div>
                    </div>
                    
                </div>
                
                <!-- Results Section -->
                <div class="calculator-results">
                    <h3><?php _e('Your Potential Savings', 'chatgabi'); ?></h3>
                    
                    <!-- Current Costs -->
                    <div class="cost-breakdown">
                        <h4><?php _e('Current Monthly Costs', 'chatgabi'); ?></h4>
                        <div class="cost-item">
                            <span class="cost-label"><?php _e('Consultant Fees', 'chatgabi'); ?></span>
                            <span class="cost-value" id="current-consultant-cost"><?php echo esc_html($user_currency['symbol']); ?>500</span>
                        </div>
                        <div class="cost-item">
                            <span class="cost-label"><?php _e('Time Investment', 'chatgabi'); ?></span>
                            <span class="cost-value" id="current-time-cost"><?php echo esc_html($user_currency['symbol']); ?>500</span>
                        </div>
                        <div class="cost-item total">
                            <span class="cost-label"><?php _e('Total Monthly Cost', 'chatgabi'); ?></span>
                            <span class="cost-value" id="total-current-cost"><?php echo esc_html($user_currency['symbol']); ?>1,000</span>
                        </div>
                    </div>
                    
                    <!-- ChatGABI Costs -->
                    <div class="chatgabi-costs">
                        <h4><?php _e('With ChatGABI', 'chatgabi'); ?></h4>
                        <div class="cost-item">
                            <span class="cost-label"><?php _e('ChatGABI Subscription', 'chatgabi'); ?></span>
                            <span class="cost-value" id="chatgabi-cost"><?php echo esc_html($user_currency['symbol']); ?>50</span>
                        </div>
                        <div class="cost-item">
                            <span class="cost-label"><?php _e('Reduced Time (90% savings)', 'chatgabi'); ?></span>
                            <span class="cost-value" id="reduced-time-cost"><?php echo esc_html($user_currency['symbol']); ?>50</span>
                        </div>
                        <div class="cost-item total">
                            <span class="cost-label"><?php _e('Total Monthly Cost', 'chatgabi'); ?></span>
                            <span class="cost-value" id="total-chatgabi-cost"><?php echo esc_html($user_currency['symbol']); ?>100</span>
                        </div>
                    </div>
                    
                    <!-- Savings Summary -->
                    <div class="savings-summary">
                        <div class="savings-item monthly">
                            <div class="savings-amount" id="monthly-savings"><?php echo esc_html($user_currency['symbol']); ?>900</div>
                            <div class="savings-label"><?php _e('Monthly Savings', 'chatgabi'); ?></div>
                        </div>
                        <div class="savings-item annual">
                            <div class="savings-amount" id="annual-savings"><?php echo esc_html($user_currency['symbol']); ?>10,800</div>
                            <div class="savings-label"><?php _e('Annual Savings', 'chatgabi'); ?></div>
                        </div>
                        <div class="savings-item roi">
                            <div class="savings-amount" id="roi-percentage">900%</div>
                            <div class="savings-label"><?php _e('Annual ROI', 'chatgabi'); ?></div>
                        </div>
                    </div>
                    
                    <!-- Time Savings -->
                    <div class="time-savings">
                        <h4><?php _e('Time Savings', 'chatgabi'); ?></h4>
                        <div class="time-item">
                            <span class="time-amount" id="hours-saved">18</span>
                            <span class="time-label"><?php _e('hours saved monthly', 'chatgabi'); ?></span>
                        </div>
                        <div class="time-item">
                            <span class="time-amount" id="days-saved">2.25</span>
                            <span class="time-label"><?php _e('work days saved monthly', 'chatgabi'); ?></span>
                        </div>
                    </div>
                    
                    <!-- Industry Insights -->
                    <div class="industry-insights">
                        <h4><?php _e('Industry Insights', 'chatgabi'); ?></h4>
                        <div class="insight-item" id="industry-insight">
                            <span class="insight-icon">💡</span>
                            <span class="insight-text"><?php _e('Technology businesses typically save 85% on planning time with ChatGABI', 'chatgabi'); ?></span>
                        </div>
                        <div class="insight-item" id="country-insight">
                            <span class="insight-icon">🌍</span>
                            <span class="insight-text"><?php _e('Ghanaian entrepreneurs report 40% faster business plan completion', 'chatgabi'); ?></span>
                        </div>
                    </div>
                    
                </div>
                
            </div>
            
            <!-- CTA Section -->
            <div class="calculator-cta">
                <div class="cta-content">
                    <h3><?php _e('Ready to Start Saving?', 'chatgabi'); ?></h3>
                    <p><?php _e('Join thousands of African entrepreneurs who are already saving time and money with ChatGABI.', 'chatgabi'); ?></p>
                    <div class="cta-buttons">
                        <a href="<?php echo wp_registration_url(); ?>" class="cta-primary" id="roi-calculator-signup">
                            <span class="cta-text"><?php _e('Start Free Trial', 'chatgabi'); ?></span>
                            <span class="cta-subtext"><?php _e('50 free credits • No credit card required', 'chatgabi'); ?></span>
                        </a>
                        <button class="cta-secondary" id="email-roi-report">
                            <span class="cta-text"><?php _e('Email My ROI Report', 'chatgabi'); ?></span>
                            <span class="cta-subtext"><?php _e('Get detailed analysis', 'chatgabi'); ?></span>
                        </button>
                    </div>
                </div>
                
                <!-- Social Proof -->
                <div class="calculator-social-proof">
                    <div class="proof-stats">
                        <div class="proof-stat">
                            <span class="proof-number">85%</span>
                            <span class="proof-label"><?php _e('Average Time Savings', 'chatgabi'); ?></span>
                        </div>
                        <div class="proof-stat">
                            <span class="proof-number">$2,500</span>
                            <span class="proof-label"><?php _e('Average Monthly Savings', 'chatgabi'); ?></span>
                        </div>
                        <div class="proof-stat">
                            <span class="proof-number">10,000+</span>
                            <span class="proof-label"><?php _e('Businesses Using ChatGABI', 'chatgabi'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
    </div>
</section>

<!-- Email ROI Report Modal -->
<div class="roi-email-modal-overlay" id="roi-email-modal">
    <div class="roi-email-modal">
        <div class="modal-header">
            <h3><?php _e('Get Your Detailed ROI Report', 'chatgabi'); ?></h3>
            <button class="modal-close" id="roi-modal-close">&times;</button>
        </div>
        <div class="modal-content">
            <p><?php _e('We\'ll send you a comprehensive ROI analysis with industry benchmarks and implementation recommendations.', 'chatgabi'); ?></p>
            <form id="roi-email-form">
                <div class="form-group">
                    <label for="roi-email"><?php _e('Email Address', 'chatgabi'); ?></label>
                    <input type="email" id="roi-email" name="email" required placeholder="<?php _e('Enter your email', 'chatgabi'); ?>">
                </div>
                <div class="form-group">
                    <label for="roi-name"><?php _e('Name', 'chatgabi'); ?></label>
                    <input type="text" id="roi-name" name="name" placeholder="<?php _e('Your name', 'chatgabi'); ?>">
                </div>
                <div class="form-group">
                    <label for="roi-company"><?php _e('Company', 'chatgabi'); ?></label>
                    <input type="text" id="roi-company" name="company" placeholder="<?php _e('Company name (optional)', 'chatgabi'); ?>">
                </div>
                <button type="submit" class="submit-btn">
                    <span class="btn-text"><?php _e('Send My ROI Report', 'chatgabi'); ?></span>
                    <span class="btn-icon">📧</span>
                </button>
            </form>
        </div>
    </div>
</div>

<style>
/* Interactive ROI Calculator Styles */
.interactive-roi-calculator-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.roi-calculator-header {
    text-align: center;
    margin-bottom: 3rem;
}

.roi-calculator-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.roi-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
}

/* Calculator Widget */
.roi-calculator-widget {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 3rem;
}

.calculator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 600px;
}

/* Input Section */
.calculator-inputs {
    padding: 3rem;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.calculator-inputs h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 2rem;
}

.input-group {
    margin-bottom: 2rem;
}

.input-group label {
    display: block;
    font-weight: 600;
    color: var(--color-african-sky);
    margin-bottom: 0.5rem;
}

.calculator-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.calculator-input:focus {
    outline: none;
    border-color: var(--color-african-sky);
}

/* Slider Inputs */
.input-with-currency,
.input-with-unit {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

.currency-symbol {
    font-weight: 700;
    color: var(--color-african-gold);
    font-size: 1.1rem;
}

.calculator-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    -webkit-appearance: none;
}

.calculator-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--color-african-sky);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.calculator-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--color-african-sky);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.slider-value {
    font-weight: 700;
    color: var(--color-african-sky);
    min-width: 60px;
    text-align: right;
}

.unit-label {
    font-weight: 600;
    color: #6c757d;
}

.input-help {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
    line-height: 1.4;
}

/* Results Section */
.calculator-results {
    padding: 3rem;
    background: white;
}

.calculator-results h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 2rem;
}

.calculator-results h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

/* Cost Breakdown */
.cost-breakdown,
.chatgabi-costs {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.cost-item:last-child {
    border-bottom: none;
}

.cost-item.total {
    font-weight: 700;
    font-size: 1.1rem;
    border-top: 2px solid var(--color-african-sky);
    margin-top: 0.5rem;
    padding-top: 1rem;
}

.cost-label {
    color: #2c3e50;
}

.cost-value {
    font-weight: 700;
    color: var(--color-african-nature);
}

/* Savings Summary */
.savings-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    border-radius: 12px;
    color: #2c3e50;
}

.savings-item {
    text-align: center;
}

.savings-amount {
    font-size: 2rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.savings-label {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.8;
}

/* Time Savings */
.time-savings {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.time-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.time-amount {
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--color-african-nature);
    min-width: 60px;
}

.time-label {
    color: #2c3e50;
    font-weight: 600;
}

/* Industry Insights */
.industry-insights {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.insight-item:last-child {
    margin-bottom: 0;
}

.insight-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.insight-text {
    color: #2c3e50;
    line-height: 1.5;
}

/* CTA Section */
.calculator-cta {
    padding: 3rem;
    background: linear-gradient(135deg, var(--color-african-sky), #2c3e50);
    color: white;
    text-align: center;
}

.cta-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.cta-primary,
.cta-secondary {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    min-width: 200px;
}

.cta-primary {
    background: var(--color-african-gold);
    color: #2c3e50;
}

.cta-secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.cta-primary:hover,
.cta-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.cta-text {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.cta-subtext {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Social Proof */
.calculator-social-proof {
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-top: 2rem;
}

.proof-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.proof-stat {
    text-align: center;
}

.proof-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--color-african-gold);
    margin-bottom: 0.5rem;
}

.proof-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ROI Email Modal */
.roi-email-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.roi-email-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.roi-email-modal {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.roi-email-modal-overlay.show .roi-email-modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--color-african-sky);
}

.modal-content {
    padding: 2rem;
}

.modal-content p {
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--color-african-sky);
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--color-african-sky);
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    color: #2c3e50;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        grid-template-columns: 1fr;
    }

    .calculator-inputs {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }

    .savings-summary {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .proof-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .input-with-currency,
    .input-with-unit {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .slider-value {
        text-align: center;
    }
}
</style>
