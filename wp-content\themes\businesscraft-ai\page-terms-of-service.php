<?php
/**
 * Template Name: Terms of Service
 * 
 * Terms of Service page for ChatGABI
 * Compliant with African legal frameworks
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="chatgabi-legal-page terms-of-service-page">
    <div class="container">
        <!-- Page Header -->
        <div class="legal-page-header glassmorphism-card">
            <div class="header-content">
                <h1 class="page-title">
                    <span class="page-icon">📋</span>
                    <?php _e('Terms of Service', 'chatgabi'); ?>
                </h1>
                <p class="page-subtitle">
                    <?php _e('These terms govern your use of ChatGABI, Swiftmind\'s AI-powered business intelligence platform for African entrepreneurs. Please read carefully before using our services.', 'chatgabi'); ?>
                </p>
                <div class="last-updated">
                    <strong><?php _e('Last Updated:', 'chatgabi'); ?></strong> <?php echo date('F j, Y'); ?>
                </div>
            </div>
        </div>

        <!-- Terms of Service Content -->
        <div class="legal-content">
            <!-- Section 1: Acceptance of Terms -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('1. Acceptance of Terms', 'chatgabi'); ?></h2>
                <p><?php _e('By accessing or using ChatGABI, you agree to be bound by these Terms of Service ("Terms"). If you disagree with any part of these terms, you may not access or use our service.', 'chatgabi'); ?></p>
                <p><?php _e('These Terms constitute a legally binding agreement between you and Swiftmind, a company based in Accra, Ghana, operating ChatGABI for users across Ghana, Kenya, Nigeria, and South Africa.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 2: Description of Service -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('2. Description of Service', 'chatgabi'); ?></h2>
                <p><?php _e('ChatGABI is an AI-powered business intelligence platform that provides:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('AI-driven business insights and recommendations', 'chatgabi'); ?></li>
                    <li><?php _e('Business document templates and wizards', 'chatgabi'); ?></li>
                    <li><?php _e('Market analysis and opportunity identification', 'chatgabi'); ?></li>
                    <li><?php _e('Sector-specific intelligence for African markets', 'chatgabi'); ?></li>
                    <li><?php _e('Multi-language support for local African languages', 'chatgabi'); ?></li>
                </ul>
                <p><?php _e('Our service operates on a credit-based system where users purchase credits to access AI-powered features.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 3: User Accounts and Registration -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('3. User Accounts and Registration', 'chatgabi'); ?></h2>
                
                <h3><?php _e('3.1 Account Creation', 'chatgabi'); ?></h3>
                <p><?php _e('To use ChatGABI, you must create an account by providing accurate and complete information. You are responsible for maintaining the confidentiality of your account credentials.', 'chatgabi'); ?></p>

                <h3><?php _e('3.2 Eligibility', 'chatgabi'); ?></h3>
                <p><?php _e('You must be at least 18 years old and have the legal capacity to enter into contracts in your jurisdiction to use ChatGABI.', 'chatgabi'); ?></p>

                <h3><?php _e('3.3 Account Security', 'chatgabi'); ?></h3>
                <p><?php _e('You are responsible for all activities that occur under your account. Notify us immediately of any unauthorized use of your account.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 4: Credit System and Payments -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('4. Credit System and Payments', 'chatgabi'); ?></h2>
                
                <h3><?php _e('4.1 Credit Purchase', 'chatgabi'); ?></h3>
                <p><?php _e('Credits are purchased through our secure payment system powered by Paystack. Prices are displayed in local currencies (GHS, KES, NGN, ZAR) and USD.', 'chatgabi'); ?></p>

                <h3><?php _e('4.2 Credit Usage', 'chatgabi'); ?></h3>
                <p><?php _e('Credits are consumed when you use AI-powered features. Different features may consume different amounts of credits based on computational requirements.', 'chatgabi'); ?></p>

                <h3><?php _e('4.3 Free Credits', 'chatgabi'); ?></h3>
                <p><?php _e('New users receive free credits to test our system. These credits are provided at our discretion and may be subject to limitations.', 'chatgabi'); ?></p>

                <h3><?php _e('4.4 Payment Processing', 'chatgabi'); ?></h3>
                <p><?php _e('All payments are processed securely through Paystack. We do not store your payment card information on our servers.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 5: Acceptable Use Policy -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('5. Acceptable Use Policy', 'chatgabi'); ?></h2>
                <p><?php _e('You agree not to use ChatGABI for any of the following prohibited activities:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Illegal activities or violation of applicable laws', 'chatgabi'); ?></li>
                    <li><?php _e('Generating harmful, offensive, or discriminatory content', 'chatgabi'); ?></li>
                    <li><?php _e('Attempting to reverse engineer or hack our systems', 'chatgabi'); ?></li>
                    <li><?php _e('Sharing account credentials with unauthorized parties', 'chatgabi'); ?></li>
                    <li><?php _e('Using the service to compete with or harm our business', 'chatgabi'); ?></li>
                    <li><?php _e('Violating intellectual property rights of others', 'chatgabi'); ?></li>
                    <li><?php _e('Spamming or sending unsolicited communications', 'chatgabi'); ?></li>
                    <li><?php _e('Attempting to overwhelm our systems with excessive requests', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 6: Intellectual Property Rights -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('6. Intellectual Property Rights', 'chatgabi'); ?></h2>
                
                <h3><?php _e('6.1 Our Intellectual Property', 'chatgabi'); ?></h3>
                <p><?php _e('ChatGABI, including its software, design, content, and trademarks, is owned by Swiftmind and protected by intellectual property laws. You may not copy, modify, or distribute our intellectual property without permission.', 'chatgabi'); ?></p>

                <h3><?php _e('6.2 Your Content', 'chatgabi'); ?></h3>
                <p><?php _e('You retain ownership of content you input into ChatGABI. By using our service, you grant us a limited license to process your content to provide our services.', 'chatgabi'); ?></p>

                <h3><?php _e('6.3 AI-Generated Content', 'chatgabi'); ?></h3>
                <p><?php _e('Content generated by our AI system based on your prompts is provided for your use. However, you are responsible for ensuring such content complies with applicable laws and does not infringe third-party rights.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 7: Privacy and Data Protection -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('7. Privacy and Data Protection', 'chatgabi'); ?></h2>
                <p><?php _e('Your privacy is important to us. Our collection, use, and protection of your personal information is governed by our Privacy Policy, which is incorporated into these Terms by reference.', 'chatgabi'); ?></p>
                <p><?php _e('We comply with applicable data protection laws in Ghana, Kenya, Nigeria, and South Africa, including POPIA, NDPR, and other relevant regulations.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 8: Service Availability and Modifications -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('8. Service Availability and Modifications', 'chatgabi'); ?></h2>
                
                <h3><?php _e('8.1 Service Availability', 'chatgabi'); ?></h3>
                <p><?php _e('We strive to maintain high service availability but cannot guarantee uninterrupted access. Scheduled maintenance and unexpected downtime may occur.', 'chatgabi'); ?></p>

                <h3><?php _e('8.2 Service Modifications', 'chatgabi'); ?></h3>
                <p><?php _e('We reserve the right to modify, suspend, or discontinue any aspect of ChatGABI at any time. We will provide reasonable notice of significant changes when possible.', 'chatgabi'); ?></p>

                <h3><?php _e('8.3 Updates to Terms', 'chatgabi'); ?></h3>
                <p><?php _e('We may update these Terms from time to time. Continued use of ChatGABI after changes constitutes acceptance of the updated Terms.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 9: Limitation of Liability -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('9. Limitation of Liability', 'chatgabi'); ?></h2>
                <p><?php _e('To the maximum extent permitted by law:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('ChatGABI is provided "as is" without warranties of any kind', 'chatgabi'); ?></li>
                    <li><?php _e('We are not liable for indirect, incidental, or consequential damages', 'chatgabi'); ?></li>
                    <li><?php _e('Our total liability is limited to the amount you paid for credits in the past 12 months', 'chatgabi'); ?></li>
                    <li><?php _e('We are not responsible for decisions made based on AI-generated content', 'chatgabi'); ?></li>
                    <li><?php _e('You use ChatGABI at your own risk and discretion', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 10: Indemnification -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('10. Indemnification', 'chatgabi'); ?></h2>
                <p><?php _e('You agree to indemnify and hold harmless Swiftmind, its officers, directors, employees, and agents from any claims, damages, or expenses arising from:', 'chatgabi'); ?></p>
                <ul>
                    <li><?php _e('Your use of ChatGABI', 'chatgabi'); ?></li>
                    <li><?php _e('Your violation of these Terms', 'chatgabi'); ?></li>
                    <li><?php _e('Your violation of applicable laws', 'chatgabi'); ?></li>
                    <li><?php _e('Your infringement of third-party rights', 'chatgabi'); ?></li>
                </ul>
            </section>

            <!-- Section 11: Termination -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('11. Termination', 'chatgabi'); ?></h2>
                
                <h3><?php _e('11.1 Termination by You', 'chatgabi'); ?></h3>
                <p><?php _e('You may terminate your account at any time by contacting us. Unused credits are non-refundable upon termination.', 'chatgabi'); ?></p>

                <h3><?php _e('11.2 Termination by Us', 'chatgabi'); ?></h3>
                <p><?php _e('We may suspend or terminate your account for violation of these Terms, illegal activity, or other reasons at our discretion.', 'chatgabi'); ?></p>

                <h3><?php _e('11.3 Effect of Termination', 'chatgabi'); ?></h3>
                <p><?php _e('Upon termination, your access to ChatGABI will cease, and we may delete your account data according to our data retention policy.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 12: Governing Law and Dispute Resolution -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('12. Governing Law and Dispute Resolution', 'chatgabi'); ?></h2>
                
                <h3><?php _e('12.1 Governing Law', 'chatgabi'); ?></h3>
                <p><?php _e('These Terms are governed by the laws of Ghana, where Swiftmind is based, without regard to conflict of law principles.', 'chatgabi'); ?></p>

                <h3><?php _e('12.2 Dispute Resolution', 'chatgabi'); ?></h3>
                <p><?php _e('We encourage resolving disputes through direct communication. If necessary, disputes will be resolved through arbitration in Accra, Ghana, or the courts of Ghana.', 'chatgabi'); ?></p>

                <h3><?php _e('12.3 African Market Considerations', 'chatgabi'); ?></h3>
                <p><?php _e('For users in Kenya, Nigeria, and South Africa, local consumer protection laws may provide additional rights that cannot be waived by these Terms.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 13: Miscellaneous -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('13. Miscellaneous', 'chatgabi'); ?></h2>
                
                <h3><?php _e('13.1 Entire Agreement', 'chatgabi'); ?></h3>
                <p><?php _e('These Terms, together with our Privacy Policy and Refund Policy, constitute the entire agreement between you and Swiftmind regarding ChatGABI.', 'chatgabi'); ?></p>

                <h3><?php _e('13.2 Severability', 'chatgabi'); ?></h3>
                <p><?php _e('If any provision of these Terms is found to be unenforceable, the remaining provisions will remain in full force and effect.', 'chatgabi'); ?></p>

                <h3><?php _e('13.3 Assignment', 'chatgabi'); ?></h3>
                <p><?php _e('You may not assign your rights under these Terms. We may assign our rights and obligations to any party without restriction.', 'chatgabi'); ?></p>

                <h3><?php _e('13.4 Force Majeure', 'chatgabi'); ?></h3>
                <p><?php _e('We are not liable for delays or failures in performance due to circumstances beyond our reasonable control, including natural disasters, government actions, or technical failures.', 'chatgabi'); ?></p>
            </section>

            <!-- Section 14: Contact Information -->
            <section class="legal-section glassmorphism-card">
                <h2><?php _e('14. Contact Information', 'chatgabi'); ?></h2>
                <p><?php _e('If you have questions about these Terms of Service, please contact us:', 'chatgabi'); ?></p>
                <div class="contact-info">
                    <p><strong><?php _e('Swiftmind', 'chatgabi'); ?></strong></p>
                    <p><?php _e('Email: <EMAIL>', 'chatgabi'); ?></p>
                    <p><?php _e('Address: Accra, Ghana', 'chatgabi'); ?></p>
                    <p><?php _e('Product: ChatGABI - AI Business Intelligence Platform', 'chatgabi'); ?></p>
                </div>
            </section>
        </div>
    </div>
</div>

<style>
/* Terms of Service Specific Styles */
.terms-of-service-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.legal-page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-icon {
    font-size: 2.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.last-updated {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
    padding: 10px 20px;
    background: rgba(var(--color-secondary-accent), 0.1);
    border-radius: 8px;
    display: inline-block;
}

.legal-content {
    max-width: 900px;
    margin: 0 auto;
}

.legal-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 25px;
}

.legal-section h2 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--color-secondary-accent);
    padding-bottom: 10px;
}

.legal-section h3 {
    color: var(--color-tertiary-accent);
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
}

.legal-section p {
    line-height: 1.7;
    margin-bottom: 15px;
    color: var(--color-text-primary);
}

.legal-section ul {
    margin: 15px 0;
    padding-left: 25px;
}

.legal-section li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: var(--color-text-primary);
}

.contact-info {
    background: rgba(var(--color-nature-green), 0.1);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.contact-info p {
    margin-bottom: 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .terms-of-service-page {
        padding: 20px 0;
    }
    
    .legal-page-header {
        padding: 25px 20px;
    }
    
    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .legal-section {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .legal-section h2 {
        font-size: 1.5rem;
    }
    
    .legal-section h3 {
        font-size: 1.2rem;
    }
}

/* Dark Theme Support */
body.theme-dark .legal-page-header,
body.theme-dark .legal-section {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .page-title {
    color: var(--color-secondary-accent);
}

body.theme-dark .legal-section h2 {
    color: var(--color-secondary-accent);
    border-bottom-color: var(--color-primary-accent);
}

body.theme-dark .legal-section h3 {
    color: var(--color-tertiary-accent);
}

body.theme-dark .contact-info {
    background: rgba(39, 174, 96, 0.2);
}
</style>

<?php
// Add SEO meta tags
function chatgabi_terms_of_service_meta() {
    $title = __('Terms of Service - ChatGABI | Swiftmind', 'chatgabi');
    $description = __('Read the Terms of Service for ChatGABI, Swiftmind\'s AI-powered business intelligence platform. Understand your rights and responsibilities when using our services across Ghana, Kenya, Nigeria, and South Africa.', 'chatgabi');
    
    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
}
add_action('wp_head', 'chatgabi_terms_of_service_meta');

get_footer();
?>
