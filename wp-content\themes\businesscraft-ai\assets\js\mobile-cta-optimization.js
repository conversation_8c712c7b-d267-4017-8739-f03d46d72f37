/**
 * ChatGABI Mobile CTA Optimization
 * 
 * Handles mobile-specific CTA optimizations, sticky CTAs,
 * and thumb-friendly button interactions for African mobile-first users.
 */

(function($) {
    'use strict';

    const MobileCTAOptimizer = {
        
        // Configuration
        config: {
            stickyThreshold: 20, // Show sticky CTA after 20% scroll
            hideThreshold: 10,   // Hide sticky CTA before 10% scroll
            buttonMinSize: 48,   // Minimum touch target size (48px)
            animationDuration: 300
        },
        
        // Initialize mobile CTA optimizations
        init: function() {
            this.optimizeButtonSizes();
            this.initStickyCTA();
            this.addTouchFeedback();
            this.trackMobileInteractions();
            this.optimizeForThumbNavigation();
        },
        
        // Optimize button sizes for mobile
        optimizeButtonSizes: function() {
            if (window.innerWidth <= 768) {
                // Ensure all CTAs meet minimum touch target size
                $('.cta-primary, .cta-secondary, .hero-cta, .button').each(function() {
                    const $button = $(this);
                    const currentHeight = $button.outerHeight();
                    const currentWidth = $button.outerWidth();
                    
                    if (currentHeight < this.config.buttonMinSize) {
                        $button.css('min-height', this.config.buttonMinSize + 'px');
                    }
                    
                    if (currentWidth < this.config.buttonMinSize) {
                        $button.css('min-width', this.config.buttonMinSize + 'px');
                    }
                    
                    // Add mobile-optimized padding
                    $button.addClass('mobile-optimized-cta');
                }.bind(this));
            }
        },
        
        // Initialize sticky CTA for mobile
        initStickyCTA: function() {
            if (window.innerWidth <= 768) {
                const $stickyCTA = $('#mobile-sticky-cta');
                let isVisible = false;
                
                $(window).on('scroll', () => {
                    const scrollPercent = ($(window).scrollTop() / ($(document).height() - $(window).height())) * 100;
                    
                    if (scrollPercent > this.config.stickyThreshold && !isVisible) {
                        this.showStickyCTA($stickyCTA);
                        isVisible = true;
                    } else if (scrollPercent < this.config.hideThreshold && isVisible) {
                        this.hideStickyCTA($stickyCTA);
                        isVisible = false;
                    }
                });
                
                // Track sticky CTA interactions
                $stickyCTA.find('.sticky-cta-button').on('click', function() {
                    if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                        window.ChatGABI.ConversionTracker.trackCTAClick('sticky_mobile', 'bottom');
                    }
                });
            }
        },
        
        // Show sticky CTA with animation
        showStickyCTA: function($element) {
            $element.addClass('show').css({
                'transform': 'translateY(0)',
                'opacity': '1'
            });
            
            // Add entrance animation
            $element.find('.sticky-cta-content').css({
                'animation': 'slideUpBounce 0.5s ease-out'
            });
        },
        
        // Hide sticky CTA with animation
        hideStickyCTA: function($element) {
            $element.removeClass('show').css({
                'transform': 'translateY(100%)',
                'opacity': '0'
            });
        },
        
        // Add touch feedback for better mobile UX
        addTouchFeedback: function() {
            $('.cta-primary, .cta-secondary, .button, .trust-indicator').on('touchstart', function() {
                $(this).addClass('touch-active');
            }).on('touchend touchcancel', function() {
                $(this).removeClass('touch-active');
            });
        },
        
        // Track mobile-specific interactions
        trackMobileInteractions: function() {
            // Track mobile CTA clicks
            $('.cta-primary, .cta-secondary').on('click', function() {
                const ctaType = $(this).hasClass('cta-primary') ? 'primary' : 'secondary';
                const position = $(this).closest('.hero-cta-section').length ? 'hero' : 'other';
                
                if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                    window.ChatGABI.ConversionTracker.trackConversion('mobile_cta_click', {
                        cta_type: ctaType,
                        position: position,
                        device: 'mobile',
                        screen_width: window.innerWidth
                    });
                }
            });
            
            // Track mobile scroll behavior
            let maxScrollDepth = 0;
            $(window).on('scroll', function() {
                const scrollPercent = Math.round(($(window).scrollTop() / ($(document).height() - $(window).height())) * 100);
                
                if (scrollPercent > maxScrollDepth) {
                    maxScrollDepth = scrollPercent;
                    
                    // Track significant scroll milestones on mobile
                    if ([25, 50, 75, 90].includes(scrollPercent)) {
                        if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                            window.ChatGABI.ConversionTracker.trackConversion('mobile_scroll_depth', {
                                depth: scrollPercent,
                                device: 'mobile',
                                screen_width: window.innerWidth
                            });
                        }
                    }
                }
            });
        },
        
        // Optimize for thumb navigation
        optimizeForThumbNavigation: function() {
            if (window.innerWidth <= 768) {
                // Add thumb-friendly spacing
                $('.hero-benefits-list .benefit-item').css({
                    'margin-bottom': '1rem',
                    'padding': '1rem'
                });
                
                // Optimize trust indicators for mobile
                $('.trust-indicators-grid').css({
                    'grid-template-columns': 'repeat(2, 1fr)',
                    'gap': '1rem'
                });
                
                // Add mobile-specific CTA positioning
                this.addMobileSpecificCTAs();
            }
        },
        
        // Add mobile-specific CTAs
        addMobileSpecificCTAs: function() {
            // Add floating action button for key actions
            const $fab = $(`
                <div class="mobile-fab" id="mobile-fab">
                    <button class="fab-button" aria-label="Quick Start">
                        <span class="fab-icon">🚀</span>
                    </button>
                    <div class="fab-menu">
                        <a href="#chat-demo" class="fab-item">
                            <span class="fab-item-icon">💬</span>
                            <span class="fab-item-text">Try Demo</span>
                        </a>
                        <a href="<?php echo wp_registration_url(); ?>" class="fab-item">
                            <span class="fab-item-icon">✨</span>
                            <span class="fab-item-text">Sign Up</span>
                        </a>
                        <a href="#pricing" class="fab-item">
                            <span class="fab-item-icon">💰</span>
                            <span class="fab-item-text">Pricing</span>
                        </a>
                    </div>
                </div>
            `);
            
            $('body').append($fab);
            
            // FAB interactions
            $('.fab-button').on('click', function() {
                $('.mobile-fab').toggleClass('fab-open');
                
                if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                    window.ChatGABI.ConversionTracker.trackConversion('mobile_fab_click', {
                        action: $('.mobile-fab').hasClass('fab-open') ? 'open' : 'close'
                    });
                }
            });
            
            // Track FAB item clicks
            $('.fab-item').on('click', function() {
                const action = $(this).find('.fab-item-text').text().toLowerCase();
                
                if (window.ChatGABI && window.ChatGABI.ConversionTracker) {
                    window.ChatGABI.ConversionTracker.trackConversion('mobile_fab_item_click', {
                        action: action,
                        device: 'mobile'
                    });
                }
            });
        },
        
        // Add haptic feedback simulation
        addHapticFeedback: function() {
            $('.cta-primary, .cta-secondary').on('click', function() {
                // Simulate haptic feedback with visual feedback
                $(this).addClass('haptic-feedback');
                setTimeout(() => {
                    $(this).removeClass('haptic-feedback');
                }, 150);
            });
        },
        
        // Optimize for different mobile orientations
        handleOrientationChange: function() {
            $(window).on('orientationchange', () => {
                setTimeout(() => {
                    this.optimizeButtonSizes();
                    this.adjustLayoutForOrientation();
                }, 100);
            });
        },
        
        // Adjust layout for orientation
        adjustLayoutForOrientation: function() {
            const isLandscape = window.innerWidth > window.innerHeight;
            
            if (isLandscape && window.innerWidth <= 768) {
                // Landscape mobile optimizations
                $('.hero-benefits-list').css('grid-template-columns', 'repeat(2, 1fr)');
                $('.trust-indicators-grid').css('grid-template-columns', 'repeat(4, 1fr)');
            } else {
                // Portrait mobile optimizations
                $('.hero-benefits-list').css('grid-template-columns', '1fr');
                $('.trust-indicators-grid').css('grid-template-columns', 'repeat(2, 1fr)');
            }
        }
    };
    
    // Mobile-specific CSS styles
    const mobileStyles = `
        <style>
        /* Mobile CTA Optimizations */
        @media (max-width: 768px) {
            .mobile-optimized-cta {
                min-height: 48px !important;
                min-width: 48px !important;
                padding: 12px 20px !important;
                font-size: 16px !important; /* Prevent zoom on iOS */
                border-radius: 8px !important;
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
            }
            
            .touch-active {
                transform: scale(0.98);
                opacity: 0.8;
                transition: all 0.1s ease;
            }
            
            .haptic-feedback {
                animation: hapticPulse 0.15s ease;
            }
            
            @keyframes hapticPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            @keyframes slideUpBounce {
                0% { transform: translateY(100%); opacity: 0; }
                60% { transform: translateY(-10px); opacity: 1; }
                100% { transform: translateY(0); opacity: 1; }
            }
            
            /* Mobile FAB Styles */
            .mobile-fab {
                position: fixed;
                bottom: 80px;
                right: 20px;
                z-index: 1001;
                transition: all 0.3s ease;
            }
            
            .fab-button {
                width: 56px;
                height: 56px;
                border-radius: 50%;
                background: linear-gradient(45deg, #3D4E81, #5753C9);
                border: none;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                color: white;
                font-size: 24px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .fab-button:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 16px rgba(0,0,0,0.4);
            }
            
            .fab-menu {
                position: absolute;
                bottom: 70px;
                right: 0;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                padding: 8px;
                min-width: 150px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(20px);
                transition: all 0.3s ease;
            }
            
            .fab-open .fab-menu {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            
            .fab-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                border-radius: 8px;
                text-decoration: none;
                color: #2c3e50;
                transition: background-color 0.2s ease;
                min-height: 48px;
            }
            
            .fab-item:hover {
                background-color: #f8f9fa;
            }
            
            .fab-item-icon {
                font-size: 20px;
                width: 24px;
                text-align: center;
            }
            
            .fab-item-text {
                font-weight: 600;
                font-size: 14px;
            }
            
            /* Enhanced Mobile Sticky CTA */
            .mobile-sticky-cta {
                box-shadow: 0 -4px 20px rgba(0,0,0,0.2);
                backdrop-filter: blur(10px);
            }
            
            .sticky-cta-button {
                min-height: 48px;
                font-size: 16px;
                padding: 12px 24px;
                border-radius: 24px;
                transition: all 0.2s ease;
            }
            
            .sticky-cta-button:active {
                transform: scale(0.98);
            }
            
            /* Thumb-friendly spacing */
            .hero-benefits-list .benefit-item {
                min-height: 60px;
                padding: 16px !important;
                margin-bottom: 12px !important;
            }
            
            .trust-indicator {
                min-height: 80px;
                padding: 16px !important;
            }
            
            /* Prevent accidental zooms */
            input, select, textarea, button {
                font-size: 16px !important;
            }
        }
        
        /* Landscape mobile optimizations */
        @media (max-width: 768px) and (orientation: landscape) {
            .enhanced-hero-section {
                padding: 2rem 0 3rem 0;
                min-height: 60vh;
            }
            
            .hero-content-wrapper {
                gap: 2rem;
            }
            
            .mobile-fab {
                bottom: 20px;
                right: 20px;
            }
        }
        </style>
    `;
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Add mobile styles
        $('head').append(mobileStyles);
        
        // Initialize mobile CTA optimizer
        MobileCTAOptimizer.init();
        MobileCTAOptimizer.addHapticFeedback();
        MobileCTAOptimizer.handleOrientationChange();
        
        // Initial orientation adjustment
        MobileCTAOptimizer.adjustLayoutForOrientation();
    });
    
    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.MobileCTAOptimizer = MobileCTAOptimizer;

})(jQuery);
