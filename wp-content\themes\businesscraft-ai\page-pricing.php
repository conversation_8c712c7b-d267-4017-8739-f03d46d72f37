<?php
/**
 * Template Name: Pricing
 * 
 * Pricing page for ChatGABI
 * Multi-currency credit packages with Paystack integration
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get user data for personalization
$current_user = wp_get_current_user();
$user_credits = 0;
$user_country = 'Ghana';

if ($current_user->ID) {
    $user_credits = get_user_meta($current_user->ID, 'businesscraft_credits', true) ?: 0;
    $user_country = get_user_meta($current_user->ID, 'country', true) ?: 'Ghana';
}

// Determine currency based on user country
$currency_map = array(
    'Ghana' => 'GHS',
    'Kenya' => 'KES', 
    'Nigeria' => 'NGN',
    'South Africa' => 'ZAR'
);

$user_currency = $currency_map[$user_country] ?? 'USD';

// Multi-currency pricing
function get_pricing_for_currency($currency) {
    $base_prices = array(
        'starter' => array('usd' => 10, 'credits' => 100),
        'business' => array('usd' => 45, 'credits' => 500),
        'professional' => array('usd' => 80, 'credits' => 1000),
        'enterprise' => array('usd' => 350, 'credits' => 5000)
    );
    
    $exchange_rates = array(
        'USD' => 1.0,
        'GHS' => 12.0,
        'KES' => 150.0,
        'NGN' => 800.0,
        'ZAR' => 18.0
    );
    
    $currency_symbols = array(
        'USD' => '$',
        'GHS' => '₵',
        'KES' => 'KSh',
        'NGN' => '₦',
        'ZAR' => 'R'
    );
    
    $rate = $exchange_rates[$currency] ?? 1.0;
    $symbol = $currency_symbols[$currency] ?? '$';
    
    $pricing = array();
    foreach ($base_prices as $package => $data) {
        $local_price = $data['usd'] * $rate;
        $pricing[$package] = array(
            'credits' => $data['credits'],
            'usd_price' => $data['usd'],
            'local_price' => $local_price,
            'currency' => $currency,
            'symbol' => $symbol,
            'formatted' => $symbol . number_format($local_price, 0),
            'per_credit' => round($local_price / $data['credits'], 2)
        );
    }
    
    return $pricing;
}

$pricing = get_pricing_for_currency($user_currency);
?>

<div class="chatgabi-pricing-page">
    <div class="container">
        <!-- Pricing Hero Section -->
        <div class="pricing-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">💰</span>
                    <?php _e('ChatGABI Pricing', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Transparent, flexible pricing designed for African entrepreneurs. Pay only for what you use with our credit-based system.', 'chatgabi'); ?>
                </p>
                
                <?php if ($current_user->ID): ?>
                    <div class="user-credits-display">
                        <div class="credits-info">
                            <span class="credits-icon">💳</span>
                            <span class="credits-text">
                                <?php printf(__('You have %s credits available', 'chatgabi'), '<strong>' . number_format($user_credits) . '</strong>'); ?>
                            </span>
                        </div>
                        <div class="currency-info">
                            <span class="currency-icon">🌍</span>
                            <span class="currency-text">
                                <?php printf(__('Prices shown in %s for %s', 'chatgabi'), $user_currency, $user_country); ?>
                            </span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="pricing-benefits">
                    <div class="benefit-item">
                        <span class="benefit-icon">✅</span>
                        <span><?php _e('No monthly subscriptions', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">✅</span>
                        <span><?php _e('Credits never expire', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">✅</span>
                        <span><?php _e('Secure African payments', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">✅</span>
                        <span><?php _e('Multi-currency support', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency Selector -->
        <div class="currency-selector glassmorphism-card">
            <div class="selector-header">
                <h3><?php _e('Choose Your Currency', 'chatgabi'); ?></h3>
                <p><?php _e('Select your preferred currency for local pricing', 'chatgabi'); ?></p>
            </div>
            <div class="currency-options">
                <button class="currency-btn <?php echo $user_currency === 'GHS' ? 'active' : ''; ?>" data-currency="GHS" data-country="Ghana">
                    <span class="flag">🇬🇭</span>
                    <span class="currency-info">
                        <strong>Ghana Cedi</strong>
                        <small>GHS (₵)</small>
                    </span>
                </button>
                <button class="currency-btn <?php echo $user_currency === 'KES' ? 'active' : ''; ?>" data-currency="KES" data-country="Kenya">
                    <span class="flag">🇰🇪</span>
                    <span class="currency-info">
                        <strong>Kenyan Shilling</strong>
                        <small>KES (KSh)</small>
                    </span>
                </button>
                <button class="currency-btn <?php echo $user_currency === 'NGN' ? 'active' : ''; ?>" data-currency="NGN" data-country="Nigeria">
                    <span class="flag">🇳🇬</span>
                    <span class="currency-info">
                        <strong>Nigerian Naira</strong>
                        <small>NGN (₦)</small>
                    </span>
                </button>
                <button class="currency-btn <?php echo $user_currency === 'ZAR' ? 'active' : ''; ?>" data-currency="ZAR" data-country="South Africa">
                    <span class="flag">🇿🇦</span>
                    <span class="currency-info">
                        <strong>South African Rand</strong>
                        <small>ZAR (R)</small>
                    </span>
                </button>
                <button class="currency-btn <?php echo $user_currency === 'USD' ? 'active' : ''; ?>" data-currency="USD" data-country="International">
                    <span class="flag">🌍</span>
                    <span class="currency-info">
                        <strong>US Dollar</strong>
                        <small>USD ($)</small>
                    </span>
                </button>
            </div>
        </div>

        <!-- Pricing Cards -->
        <div class="pricing-section">
            <div class="section-header">
                <h2><?php _e('Credit Packages', 'chatgabi'); ?></h2>
                <p><?php _e('Choose the package that fits your business needs. All packages include full access to ChatGABI features.', 'chatgabi'); ?></p>
            </div>
            
            <div class="pricing-grid">
                <!-- Starter Package -->
                <div class="pricing-card glassmorphism-card" data-package="starter">
                    <div class="package-header">
                        <div class="package-icon">🚀</div>
                        <h3><?php _e('Starter Pack', 'chatgabi'); ?></h3>
                        <p class="package-description"><?php _e('Perfect for trying ChatGABI features', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="package-pricing">
                        <div class="price-display">
                            <span class="currency-symbol"><?php echo $pricing['starter']['symbol']; ?></span>
                            <span class="price-amount" data-price="<?php echo $pricing['starter']['local_price']; ?>">
                                <?php echo number_format($pricing['starter']['local_price'], 0); ?>
                            </span>
                        </div>
                        <div class="credits-amount">
                            <span class="credits-number"><?php echo number_format($pricing['starter']['credits']); ?></span>
                            <span class="credits-label"><?php _e('Credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="per-credit-cost">
                            <?php printf(__('%s per credit', 'chatgabi'), $pricing['starter']['symbol'] . number_format($pricing['starter']['per_credit'], 2)); ?>
                        </div>
                    </div>
                    
                    <div class="package-features">
                        <ul>
                            <li><?php _e('100 AI chat queries', 'chatgabi'); ?></li>
                            <li><?php _e('50 template enhancements', 'chatgabi'); ?></li>
                            <li><?php _e('33 market analysis reports', 'chatgabi'); ?></li>
                            <li><?php _e('20 document wizards', 'chatgabi'); ?></li>
                            <li><?php _e('Multi-language support', 'chatgabi'); ?></li>
                            <li><?php _e('African context intelligence', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="package-action">
                        <?php if ($current_user->ID): ?>
                            <button class="btn btn-primary purchase-btn" 
                                    data-package="starter" 
                                    data-credits="<?php echo $pricing['starter']['credits']; ?>"
                                    data-amount="<?php echo $pricing['starter']['local_price']; ?>"
                                    data-currency="<?php echo $user_currency; ?>">
                                <span class="btn-icon">💳</span>
                                <?php _e('Purchase Now', 'chatgabi'); ?>
                            </button>
                        <?php else: ?>
                            <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary">
                                <span class="btn-icon">🚀</span>
                                <?php _e('Sign Up to Purchase', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Business Package -->
                <div class="pricing-card glassmorphism-card popular" data-package="business">
                    <div class="popular-badge"><?php _e('Most Popular', 'chatgabi'); ?></div>
                    <div class="package-header">
                        <div class="package-icon">💼</div>
                        <h3><?php _e('Business Pack', 'chatgabi'); ?></h3>
                        <p class="package-description"><?php _e('Ideal for growing businesses', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="package-pricing">
                        <div class="price-display">
                            <span class="currency-symbol"><?php echo $pricing['business']['symbol']; ?></span>
                            <span class="price-amount" data-price="<?php echo $pricing['business']['local_price']; ?>">
                                <?php echo number_format($pricing['business']['local_price'], 0); ?>
                            </span>
                        </div>
                        <div class="credits-amount">
                            <span class="credits-number"><?php echo number_format($pricing['business']['credits']); ?></span>
                            <span class="credits-label"><?php _e('Credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="per-credit-cost">
                            <?php printf(__('%s per credit', 'chatgabi'), $pricing['business']['symbol'] . number_format($pricing['business']['per_credit'], 2)); ?>
                        </div>
                        <div class="savings-badge">
                            <?php _e('Save 10%', 'chatgabi'); ?>
                        </div>
                    </div>
                    
                    <div class="package-features">
                        <ul>
                            <li><?php _e('500 AI chat queries', 'chatgabi'); ?></li>
                            <li><?php _e('250 template enhancements', 'chatgabi'); ?></li>
                            <li><?php _e('166 market analysis reports', 'chatgabi'); ?></li>
                            <li><?php _e('100 document wizards', 'chatgabi'); ?></li>
                            <li><?php _e('Priority support', 'chatgabi'); ?></li>
                            <li><?php _e('Advanced analytics', 'chatgabi'); ?></li>
                            <li><?php _e('Export to PDF/DOCX', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="package-action">
                        <?php if ($current_user->ID): ?>
                            <button class="btn btn-primary purchase-btn" 
                                    data-package="business" 
                                    data-credits="<?php echo $pricing['business']['credits']; ?>"
                                    data-amount="<?php echo $pricing['business']['local_price']; ?>"
                                    data-currency="<?php echo $user_currency; ?>">
                                <span class="btn-icon">💳</span>
                                <?php _e('Purchase Now', 'chatgabi'); ?>
                            </button>
                        <?php else: ?>
                            <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary">
                                <span class="btn-icon">🚀</span>
                                <?php _e('Sign Up to Purchase', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Professional Package -->
                <div class="pricing-card glassmorphism-card" data-package="professional">
                    <div class="package-header">
                        <div class="package-icon">⭐</div>
                        <h3><?php _e('Professional Pack', 'chatgabi'); ?></h3>
                        <p class="package-description"><?php _e('For serious entrepreneurs', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="package-pricing">
                        <div class="price-display">
                            <span class="currency-symbol"><?php echo $pricing['professional']['symbol']; ?></span>
                            <span class="price-amount" data-price="<?php echo $pricing['professional']['local_price']; ?>">
                                <?php echo number_format($pricing['professional']['local_price'], 0); ?>
                            </span>
                        </div>
                        <div class="credits-amount">
                            <span class="credits-number"><?php echo number_format($pricing['professional']['credits']); ?></span>
                            <span class="credits-label"><?php _e('Credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="per-credit-cost">
                            <?php printf(__('%s per credit', 'chatgabi'), $pricing['professional']['symbol'] . number_format($pricing['professional']['per_credit'], 2)); ?>
                        </div>
                        <div class="savings-badge">
                            <?php _e('Save 20%', 'chatgabi'); ?>
                        </div>
                    </div>
                    
                    <div class="package-features">
                        <ul>
                            <li><?php _e('1,000 AI chat queries', 'chatgabi'); ?></li>
                            <li><?php _e('500 template enhancements', 'chatgabi'); ?></li>
                            <li><?php _e('333 market analysis reports', 'chatgabi'); ?></li>
                            <li><?php _e('200 document wizards', 'chatgabi'); ?></li>
                            <li><?php _e('Premium support', 'chatgabi'); ?></li>
                            <li><?php _e('Custom templates', 'chatgabi'); ?></li>
                            <li><?php _e('API access', 'chatgabi'); ?></li>
                            <li><?php _e('White-label options', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="package-action">
                        <?php if ($current_user->ID): ?>
                            <button class="btn btn-primary purchase-btn" 
                                    data-package="professional" 
                                    data-credits="<?php echo $pricing['professional']['credits']; ?>"
                                    data-amount="<?php echo $pricing['professional']['local_price']; ?>"
                                    data-currency="<?php echo $user_currency; ?>">
                                <span class="btn-icon">💳</span>
                                <?php _e('Purchase Now', 'chatgabi'); ?>
                            </button>
                        <?php else: ?>
                            <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary">
                                <span class="btn-icon">🚀</span>
                                <?php _e('Sign Up to Purchase', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Enterprise Package -->
                <div class="pricing-card glassmorphism-card enterprise" data-package="enterprise">
                    <div class="package-header">
                        <div class="package-icon">🏢</div>
                        <h3><?php _e('Enterprise Pack', 'chatgabi'); ?></h3>
                        <p class="package-description"><?php _e('For large organizations', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="package-pricing">
                        <div class="price-display">
                            <span class="currency-symbol"><?php echo $pricing['enterprise']['symbol']; ?></span>
                            <span class="price-amount" data-price="<?php echo $pricing['enterprise']['local_price']; ?>">
                                <?php echo number_format($pricing['enterprise']['local_price'], 0); ?>
                            </span>
                        </div>
                        <div class="credits-amount">
                            <span class="credits-number"><?php echo number_format($pricing['enterprise']['credits']); ?></span>
                            <span class="credits-label"><?php _e('Credits', 'chatgabi'); ?></span>
                        </div>
                        <div class="per-credit-cost">
                            <?php printf(__('%s per credit', 'chatgabi'), $pricing['enterprise']['symbol'] . number_format($pricing['enterprise']['per_credit'], 2)); ?>
                        </div>
                        <div class="savings-badge">
                            <?php _e('Save 30%', 'chatgabi'); ?>
                        </div>
                    </div>
                    
                    <div class="package-features">
                        <ul>
                            <li><?php _e('5,000 AI chat queries', 'chatgabi'); ?></li>
                            <li><?php _e('2,500 template enhancements', 'chatgabi'); ?></li>
                            <li><?php _e('1,666 market analysis reports', 'chatgabi'); ?></li>
                            <li><?php _e('1,000 document wizards', 'chatgabi'); ?></li>
                            <li><?php _e('Dedicated account manager', 'chatgabi'); ?></li>
                            <li><?php _e('Custom integrations', 'chatgabi'); ?></li>
                            <li><?php _e('Team collaboration tools', 'chatgabi'); ?></li>
                            <li><?php _e('SLA guarantee', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="package-action">
                        <?php if ($current_user->ID): ?>
                            <button class="btn btn-primary purchase-btn" 
                                    data-package="enterprise" 
                                    data-credits="<?php echo $pricing['enterprise']['credits']; ?>"
                                    data-amount="<?php echo $pricing['enterprise']['local_price']; ?>"
                                    data-currency="<?php echo $user_currency; ?>">
                                <span class="btn-icon">💳</span>
                                <?php _e('Purchase Now', 'chatgabi'); ?>
                            </button>
                        <?php else: ?>
                            <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary">
                                <span class="btn-icon">🚀</span>
                                <?php _e('Sign Up to Purchase', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Free Trial Section -->
        <div class="free-trial-section glassmorphism-card">
            <div class="trial-content">
                <h2><?php _e('Start with 25 Free Credits', 'chatgabi'); ?></h2>
                <p><?php _e('New users receive 25 free credits to explore all ChatGABI features. No credit card required.', 'chatgabi'); ?></p>

                <div class="trial-features">
                    <div class="trial-feature">
                        <span class="feature-icon">🤖</span>
                        <span><?php _e('25 AI chat queries', 'chatgabi'); ?></span>
                    </div>
                    <div class="trial-feature">
                        <span class="feature-icon">📝</span>
                        <span><?php _e('12 template enhancements', 'chatgabi'); ?></span>
                    </div>
                    <div class="trial-feature">
                        <span class="feature-icon">📊</span>
                        <span><?php _e('8 market analysis reports', 'chatgabi'); ?></span>
                    </div>
                    <div class="trial-feature">
                        <span class="feature-icon">🧙‍♂️</span>
                        <span><?php _e('5 document wizards', 'chatgabi'); ?></span>
                    </div>
                </div>

                <?php if (!$current_user->ID): ?>
                    <div class="trial-action">
                        <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">🎁</span>
                            <?php _e('Claim Your Free Credits', 'chatgabi'); ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="trial-action">
                        <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">📊</span>
                            <?php _e('Go to Dashboard', 'chatgabi'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- ROI Calculator -->
        <div class="roi-calculator-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('ROI Calculator', 'chatgabi'); ?></h2>
                <p><?php _e('Calculate the potential return on investment for your ChatGABI usage', 'chatgabi'); ?></p>
            </div>

            <div class="calculator-content">
                <div class="calculator-inputs">
                    <div class="input-group">
                        <label for="business-type"><?php _e('Business Type', 'chatgabi'); ?></label>
                        <select id="business-type">
                            <option value="startup"><?php _e('Startup', 'chatgabi'); ?></option>
                            <option value="small-business"><?php _e('Small Business', 'chatgabi'); ?></option>
                            <option value="medium-enterprise"><?php _e('Medium Enterprise', 'chatgabi'); ?></option>
                            <option value="large-corporation"><?php _e('Large Corporation', 'chatgabi'); ?></option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label for="monthly-queries"><?php _e('Monthly AI Queries', 'chatgabi'); ?></label>
                        <input type="range" id="monthly-queries" min="10" max="1000" value="100" step="10">
                        <span class="range-value" id="queries-value">100</span>
                    </div>

                    <div class="input-group">
                        <label for="consultant-cost"><?php _e('Current Consultant Cost (Monthly)', 'chatgabi'); ?></label>
                        <input type="range" id="consultant-cost" min="0" max="5000" value="500" step="50">
                        <span class="range-value" id="cost-value">$500</span>
                    </div>
                </div>

                <div class="calculator-results">
                    <div class="result-card">
                        <h4><?php _e('ChatGABI Monthly Cost', 'chatgabi'); ?></h4>
                        <div class="result-value" id="chatgabi-cost">$10</div>
                    </div>

                    <div class="result-card">
                        <h4><?php _e('Monthly Savings', 'chatgabi'); ?></h4>
                        <div class="result-value savings" id="monthly-savings">$490</div>
                    </div>

                    <div class="result-card">
                        <h4><?php _e('Annual ROI', 'chatgabi'); ?></h4>
                        <div class="result-value roi" id="annual-roi">5,880%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="payment-methods-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Secure Payment Methods', 'chatgabi'); ?></h2>
                <p><?php _e('We support multiple payment methods popular across Africa', 'chatgabi'); ?></p>
            </div>

            <div class="payment-grid">
                <div class="payment-category">
                    <h4><?php _e('💳 Card Payments', 'chatgabi'); ?></h4>
                    <div class="payment-options">
                        <div class="payment-option">
                            <span class="payment-icon">💳</span>
                            <span>Visa</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">💳</span>
                            <span>Mastercard</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">💳</span>
                            <span>Verve</span>
                        </div>
                    </div>
                </div>

                <div class="payment-category">
                    <h4><?php _e('📱 Mobile Money', 'chatgabi'); ?></h4>
                    <div class="payment-options">
                        <div class="payment-option">
                            <span class="payment-icon">📱</span>
                            <span>MTN Mobile Money</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">📱</span>
                            <span>Vodafone Cash</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">📱</span>
                            <span>AirtelTigo Money</span>
                        </div>
                    </div>
                </div>

                <div class="payment-category">
                    <h4><?php _e('🏦 Bank Transfer', 'chatgabi'); ?></h4>
                    <div class="payment-options">
                        <div class="payment-option">
                            <span class="payment-icon">🏦</span>
                            <span>Direct Bank Transfer</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">🏦</span>
                            <span>USSD Banking</span>
                        </div>
                    </div>
                </div>

                <div class="payment-category">
                    <h4><?php _e('💰 Digital Wallets', 'chatgabi'); ?></h4>
                    <div class="payment-options">
                        <div class="payment-option">
                            <span class="payment-icon">💰</span>
                            <span>Paystack Wallet</span>
                        </div>
                        <div class="payment-option">
                            <span class="payment-icon">💰</span>
                            <span>Flutterwave</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="payment-security">
                <div class="security-badges">
                    <div class="security-badge">
                        <span class="badge-icon">🔒</span>
                        <span><?php _e('256-bit SSL Encryption', 'chatgabi'); ?></span>
                    </div>
                    <div class="security-badge">
                        <span class="badge-icon">🛡️</span>
                        <span><?php _e('PCI DSS Compliant', 'chatgabi'); ?></span>
                    </div>
                    <div class="security-badge">
                        <span class="badge-icon">✅</span>
                        <span><?php _e('Paystack Secured', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="pricing-faq-section glassmorphism-card">
            <div class="section-header">
                <h2><?php _e('Pricing FAQ', 'chatgabi'); ?></h2>
                <p><?php _e('Common questions about ChatGABI pricing and credits', 'chatgabi'); ?></p>
            </div>

            <div class="faq-items">
                <div class="faq-item">
                    <h4><?php _e('How does the credit system work?', 'chatgabi'); ?></h4>
                    <p><?php _e('Credits are consumed when you use AI-powered features. Different features require different amounts of credits: AI chat (1 credit), template enhancement (2 credits), market analysis (3 credits), document wizards (5 credits).', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Do credits expire?', 'chatgabi'); ?></h4>
                    <p><?php _e('No, purchased credits never expire. You can use them at your own pace without any time pressure.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Can I get a refund?', 'chatgabi'); ?></h4>
                    <p><?php _e('All credit purchases are final and non-refundable. However, we provide 25 free credits for new users to test our system before making any purchases.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Which payment methods do you accept?', 'chatgabi'); ?></h4>
                    <p><?php _e('We accept all major credit cards (Visa, Mastercard, Verve), mobile money (MTN, Vodafone, AirtelTigo), bank transfers, and digital wallets through our secure Paystack integration.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Is there a subscription fee?', 'chatgabi'); ?></h4>
                    <p><?php _e('No, ChatGABI operates on a pay-per-use model. You only pay for the credits you purchase and use. There are no monthly or annual subscription fees.', 'chatgabi'); ?></p>
                </div>

                <div class="faq-item">
                    <h4><?php _e('Can I purchase credits for my team?', 'chatgabi'); ?></h4>
                    <p><?php _e('Yes, you can purchase credits and share them with your team members. Enterprise packages include team collaboration features and bulk credit management.', 'chatgabi'); ?></p>
                </div>
            </div>
        </div>

        <!-- Final CTA -->
        <div class="pricing-cta-section glassmorphism-card">
            <div class="cta-content">
                <h2><?php _e('Ready to Get Started?', 'chatgabi'); ?></h2>
                <p><?php _e('Join thousands of African entrepreneurs who are already using ChatGABI to build successful businesses.', 'chatgabi'); ?></p>

                <div class="cta-actions">
                    <?php if (!$current_user->ID): ?>
                        <a href="<?php echo esc_url(home_url('/register')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">🎁</span>
                            <?php _e('Start with 25 Free Credits', 'chatgabi'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/features')); ?>" class="btn btn-secondary">
                            <span class="btn-icon">🧠</span>
                            <?php _e('Explore Features', 'chatgabi'); ?>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo esc_url(home_url('/dashboard')); ?>" class="btn btn-primary btn-large">
                            <span class="btn-icon">📊</span>
                            <?php _e('Go to Dashboard', 'chatgabi'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/contact')); ?>" class="btn btn-secondary">
                            <span class="btn-icon">💬</span>
                            <?php _e('Contact Support', 'chatgabi'); ?>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="cta-guarantee">
                    <p><small><?php _e('✅ Secure payments • ✅ No hidden fees • ✅ 24/7 support • ✅ African-focused', 'chatgabi'); ?></small></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Pricing Page Specific Styles */
.chatgabi-pricing-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.pricing-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.user-credits-display {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.credits-info,
.currency-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: rgba(var(--color-primary-accent), 0.1);
    border-radius: 25px;
    font-weight: 600;
    color: var(--color-text-primary);
}

.credits-icon,
.currency-icon {
    font-size: 1.3rem;
}

.pricing-benefits {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.benefit-icon {
    font-size: 1.2rem;
    color: var(--color-nature-green);
}

/* Currency Selector */
.currency-selector {
    padding: 30px;
}

.selector-header {
    text-align: center;
    margin-bottom: 30px;
}

.selector-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.selector-header p {
    color: var(--color-text-secondary);
    font-size: 1.1rem;
}

.currency-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.currency-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--color-text-primary);
    font-weight: 600;
}

.currency-btn:hover {
    background: rgba(var(--color-primary-accent), 0.1);
    border-color: var(--color-primary-accent);
    transform: translateY(-2px);
}

.currency-btn.active {
    background: rgba(var(--color-secondary-accent), 0.2);
    border-color: var(--color-secondary-accent);
    color: var(--color-primary-accent);
}

.currency-btn .flag {
    font-size: 1.5rem;
}

.currency-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.currency-info strong {
    font-size: 1rem;
    margin-bottom: 2px;
}

.currency-info small {
    font-size: 0.8rem;
    color: var(--color-text-secondary);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.pricing-card {
    padding: 40px 30px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.pricing-card.popular {
    border-color: var(--color-secondary-accent);
    background: rgba(var(--color-secondary-accent), 0.05);
}

.pricing-card.enterprise {
    border-color: var(--color-tertiary-accent);
    background: rgba(var(--color-tertiary-accent), 0.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-secondary-accent);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.package-header {
    margin-bottom: 30px;
}

.package-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.package-header h3 {
    font-size: 1.8rem;
    color: var(--color-primary-accent);
    margin-bottom: 10px;
}

.package-description {
    color: var(--color-text-secondary);
    font-size: 1.1rem;
}

.package-pricing {
    margin-bottom: 30px;
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
}

.price-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 15px;
}

.currency-symbol {
    font-size: 1.5rem;
    color: var(--color-secondary-accent);
    font-weight: bold;
}

.price-amount {
    font-size: 3rem;
    font-weight: bold;
    color: var(--color-primary-accent);
    margin-left: 5px;
}

.credits-amount {
    margin-bottom: 10px;
}

.credits-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--color-secondary-accent);
}

.credits-label {
    color: var(--color-text-secondary);
    font-size: 1.1rem;
    margin-left: 5px;
}

.per-credit-cost {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.savings-badge {
    background: var(--color-nature-green);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
}

.package-features {
    margin-bottom: 30px;
}

.package-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.package-features li {
    padding: 8px 0;
    color: var(--color-text-primary);
    position: relative;
    padding-left: 25px;
}

.package-features li:before {
    content: "✓";
    color: var(--color-nature-green);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.package-action {
    margin-top: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    width: 100%;
    justify-content: center;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: rgba(var(--color-tertiary-accent), 0.1);
    color: var(--color-tertiary-accent);
    border: 2px solid var(--color-tertiary-accent);
}

.btn-secondary:hover {
    background: var(--color-tertiary-accent);
    color: white;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Free Trial Section */
.free-trial-section {
    text-align: center;
    background: rgba(var(--color-nature-green), 0.1);
    border: 2px solid var(--color-nature-green);
}

.trial-content h2 {
    color: var(--color-primary-accent);
    font-size: 2.2rem;
    margin-bottom: 20px;
}

.trial-content p {
    color: var(--color-text-secondary);
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.trial-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.trial-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.trial-feature .feature-icon {
    font-size: 1.5rem;
}

.trial-action {
    margin-top: 30px;
}

/* ROI Calculator */
.calculator-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

.calculator-inputs {
    display: grid;
    gap: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group label {
    font-weight: 600;
    color: var(--color-text-primary);
}

.input-group select,
.input-group input[type="range"] {
    padding: 12px;
    border: 2px solid var(--color-borders);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
}

.range-value {
    font-weight: bold;
    color: var(--color-primary-accent);
    font-size: 1.1rem;
}

.calculator-results {
    display: grid;
    gap: 20px;
}

.result-card {
    background: rgba(var(--color-primary-accent), 0.1);
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    border-left: 4px solid var(--color-primary-accent);
}

.result-card h4 {
    color: var(--color-text-secondary);
    margin-bottom: 10px;
    font-size: 1rem;
}

.result-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--color-primary-accent);
}

.result-value.savings {
    color: var(--color-nature-green);
}

.result-value.roi {
    color: var(--color-secondary-accent);
}

/* Payment Methods */
.payment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.payment-category h4 {
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.payment-options {
    display: grid;
    gap: 10px;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 8px;
    color: var(--color-text-primary);
}

.payment-icon {
    font-size: 1.2rem;
}

.payment-security {
    text-align: center;
    margin-top: 30px;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.badge-icon {
    font-size: 1.2rem;
    color: var(--color-nature-green);
}

/* FAQ */
.faq-items {
    display: grid;
    gap: 20px;
}

.faq-item {
    padding: 25px;
    background: rgba(var(--color-primary-accent), 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.faq-item h4 {
    color: var(--color-primary-accent);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.faq-item p {
    color: var(--color-text-secondary);
    line-height: 1.6;
}

/* Final CTA */
.pricing-cta-section {
    text-align: center;
    background: rgba(var(--color-secondary-accent), 0.1);
    border: 2px solid var(--color-secondary-accent);
}

.pricing-cta-section h2 {
    color: var(--color-primary-accent);
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.pricing-cta-section p {
    color: var(--color-text-secondary);
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.cta-guarantee {
    margin-top: 20px;
}

.cta-guarantee small {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-pricing-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .pricing-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .user-credits-display,
    .pricing-benefits,
    .trial-features {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .currency-options {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .currency-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .calculator-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .payment-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .security-badges {
        flex-direction: column;
        gap: 15px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .section-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .pricing-card:hover {
    background: rgba(26, 36, 58, 0.9);
}

body.theme-dark .currency-btn {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
}

body.theme-dark .currency-btn:hover,
body.theme-dark .currency-btn.active {
    background: rgba(110, 127, 243, 0.2);
    border-color: var(--color-primary-accent);
}

body.theme-dark .package-pricing {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .free-trial-section {
    background: rgba(39, 174, 96, 0.2);
}

body.theme-dark .result-card {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .payment-option {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .faq-item {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .pricing-cta-section {
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--color-secondary-accent);
}
</style>

<script>
// Pricing Page JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // Currency Selector Functionality
    const currencyButtons = document.querySelectorAll('.currency-btn');
    const pricingCards = document.querySelectorAll('.pricing-card');

    // Exchange rates and symbols
    const exchangeRates = {
        'USD': 1.0,
        'GHS': 12.0,
        'KES': 150.0,
        'NGN': 800.0,
        'ZAR': 18.0
    };

    const currencySymbols = {
        'USD': '$',
        'GHS': '₵',
        'KES': 'KSh',
        'NGN': '₦',
        'ZAR': 'R'
    };

    const basePrices = {
        'starter': 10,
        'business': 45,
        'professional': 80,
        'enterprise': 350
    };

    // Currency button click handler
    currencyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currency = this.dataset.currency;
            const country = this.dataset.country;

            // Update active state
            currencyButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update pricing display
            updatePricing(currency);

            // Update user context if logged in
            updateUserContext(currency, country);
        });
    });

    function updatePricing(currency) {
        const rate = exchangeRates[currency];
        const symbol = currencySymbols[currency];

        pricingCards.forEach(card => {
            const package = card.dataset.package;
            if (package && basePrices[package]) {
                const basePrice = basePrices[package];
                const localPrice = Math.round(basePrice * rate);

                // Update currency symbol
                const symbolElement = card.querySelector('.currency-symbol');
                if (symbolElement) {
                    symbolElement.textContent = symbol;
                }

                // Update price amount
                const priceElement = card.querySelector('.price-amount');
                if (priceElement) {
                    priceElement.textContent = localPrice.toLocaleString();
                    priceElement.dataset.price = localPrice;
                }

                // Update purchase button data
                const purchaseBtn = card.querySelector('.purchase-btn');
                if (purchaseBtn) {
                    purchaseBtn.dataset.amount = localPrice;
                    purchaseBtn.dataset.currency = currency;
                }

                // Update per-credit cost
                const credits = parseInt(card.querySelector('.credits-number').textContent.replace(',', ''));
                const perCreditCost = (localPrice / credits).toFixed(2);
                const perCreditElement = card.querySelector('.per-credit-cost');
                if (perCreditElement) {
                    perCreditElement.textContent = `${symbol}${perCreditCost} per credit`;
                }
            }
        });

        // Update ROI calculator
        updateROICalculator(currency, symbol);
    }

    function updateUserContext(currency, country) {
        const currencyInfo = document.querySelector('.currency-text');
        if (currencyInfo) {
            currencyInfo.innerHTML = `Prices shown in ${currency} for ${country}`;
        }
    }

    // ROI Calculator Functionality
    const businessTypeSelect = document.getElementById('business-type');
    const monthlyQueriesSlider = document.getElementById('monthly-queries');
    const consultantCostSlider = document.getElementById('consultant-cost');
    const queriesValue = document.getElementById('queries-value');
    const costValue = document.getElementById('cost-value');
    const chatgabiCostElement = document.getElementById('chatgabi-cost');
    const monthlySavingsElement = document.getElementById('monthly-savings');
    const annualROIElement = document.getElementById('annual-roi');

    function updateROICalculator(currency = 'USD', symbol = '$') {
        if (!monthlyQueriesSlider || !consultantCostSlider) return;

        const queries = parseInt(monthlyQueriesSlider.value);
        const consultantCost = parseInt(consultantCostSlider.value);
        const rate = exchangeRates[currency];

        // Calculate ChatGABI cost (assuming $0.10 per credit/query)
        const chatgabiCostUSD = queries * 0.10;
        const chatgabiCostLocal = Math.round(chatgabiCostUSD * rate);

        // Calculate savings
        const consultantCostLocal = Math.round(consultantCost * rate);
        const monthlySavings = consultantCostLocal - chatgabiCostLocal;
        const annualSavings = monthlySavings * 12;
        const annualROI = chatgabiCostLocal > 0 ? ((annualSavings / (chatgabiCostLocal * 12)) * 100) : 0;

        // Update display
        if (queriesValue) queriesValue.textContent = queries;
        if (costValue) costValue.textContent = symbol + consultantCost;
        if (chatgabiCostElement) chatgabiCostElement.textContent = symbol + chatgabiCostLocal.toLocaleString();
        if (monthlySavingsElement) monthlySavingsElement.textContent = symbol + monthlySavings.toLocaleString();
        if (annualROIElement) annualROIElement.textContent = Math.round(annualROI).toLocaleString() + '%';
    }

    // ROI Calculator event listeners
    if (monthlyQueriesSlider) {
        monthlyQueriesSlider.addEventListener('input', () => updateROICalculator());
    }

    if (consultantCostSlider) {
        consultantCostSlider.addEventListener('input', () => updateROICalculator());
    }

    if (businessTypeSelect) {
        businessTypeSelect.addEventListener('change', () => updateROICalculator());
    }

    // Initialize ROI calculator
    updateROICalculator();

    // Purchase Button Functionality
    const purchaseButtons = document.querySelectorAll('.purchase-btn');

    purchaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const package = this.dataset.package;
            const credits = this.dataset.credits;
            const amount = this.dataset.amount;
            const currency = this.dataset.currency;

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="btn-icon">⏳</span> Processing...';
            this.disabled = true;

            // Simulate payment processing (replace with actual Paystack integration)
            setTimeout(() => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;

                // Show success message (replace with actual payment flow)
                alert(`Initiating payment for ${package} package: ${credits} credits for ${currency} ${amount}`);

                // In production, this would redirect to Paystack payment page
                // window.location.href = '/payment/paystack?package=' + package + '&amount=' + amount + '&currency=' + currency;
            }, 2000);
        });
    });

    // Smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Pricing card hover effects
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // FAQ accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('h4');
        const answer = item.querySelector('p');

        if (question && answer) {
            // Initially hide answers
            answer.style.maxHeight = '0';
            answer.style.overflow = 'hidden';
            answer.style.transition = 'max-height 0.3s ease';

            question.style.cursor = 'pointer';
            question.style.position = 'relative';

            // Add expand/collapse indicator
            const indicator = document.createElement('span');
            indicator.innerHTML = ' ▼';
            indicator.style.fontSize = '0.8rem';
            indicator.style.transition = 'transform 0.3s ease';
            question.appendChild(indicator);

            question.addEventListener('click', function() {
                const isExpanded = answer.style.maxHeight !== '0px';

                if (isExpanded) {
                    answer.style.maxHeight = '0';
                    indicator.style.transform = 'rotate(0deg)';
                } else {
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                    indicator.style.transform = 'rotate(180deg)';
                }
            });
        }
    });

    // Initialize with first FAQ expanded
    if (faqItems.length > 0) {
        const firstQuestion = faqItems[0].querySelector('h4');
        if (firstQuestion) {
            firstQuestion.click();
        }
    }

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all glassmorphism cards for animation
    document.querySelectorAll('.glassmorphism-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Performance optimization: Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate any responsive elements if needed
            updateROICalculator();
        }, 250);
    });
});
</script>

<?php
// Add SEO meta tags and structured data
function chatgabi_pricing_meta() {
    $title = __('ChatGABI Pricing - Flexible Credit Packages for African Entrepreneurs', 'chatgabi');
    $description = __('Transparent pricing for ChatGABI AI business intelligence. Credit packages from $10 with multi-currency support for Ghana, Kenya, Nigeria, and South Africa. Start with 25 free credits.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary_large_image">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';

    // Schema.org structured data for pricing
    echo '<script type="application/ld+json">';
    echo json_encode(array(
        '@context' => 'https://schema.org',
        '@type' => 'Product',
        'name' => 'ChatGABI AI Business Intelligence',
        'description' => $description,
        'brand' => array(
            '@type' => 'Brand',
            'name' => 'Swiftmind'
        ),
        'offers' => array(
            array(
                '@type' => 'Offer',
                'name' => 'Starter Pack',
                'price' => '10',
                'priceCurrency' => 'USD',
                'description' => '100 credits for AI business intelligence'
            ),
            array(
                '@type' => 'Offer',
                'name' => 'Business Pack',
                'price' => '45',
                'priceCurrency' => 'USD',
                'description' => '500 credits for growing businesses'
            ),
            array(
                '@type' => 'Offer',
                'name' => 'Professional Pack',
                'price' => '80',
                'priceCurrency' => 'USD',
                'description' => '1,000 credits for serious entrepreneurs'
            ),
            array(
                '@type' => 'Offer',
                'name' => 'Enterprise Pack',
                'price' => '350',
                'priceCurrency' => 'USD',
                'description' => '5,000 credits for large organizations'
            )
        ),
        'aggregateRating' => array(
            '@type' => 'AggregateRating',
            'ratingValue' => '4.8',
            'reviewCount' => '150'
        )
    ));
    echo '</script>';
}
add_action('wp_head', 'chatgabi_pricing_meta');

get_footer();
?>
