<?php
/**
 * PHP Environment Test for ChatGABI
 */

header('Content-Type: text/plain');

echo "🚀 ChatGABI PHP Environment Test\n";
echo "================================\n\n";

// PHP Version
echo "✅ PHP Version: " . PHP_VERSION . "\n";
echo "📅 PHP Release: " . date('Y-m-d H:i:s') . "\n\n";

// Server Information
echo "🖥️ Server Information:\n";
echo "   - Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
echo "   - Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "   - Server Name: " . $_SERVER['SERVER_NAME'] . "\n";
echo "   - Server Port: " . $_SERVER['SERVER_PORT'] . "\n\n";

// PHP Extensions Check
echo "🔧 Required Extensions:\n";
$required_extensions = [
    'pdo',
    'pdo_mysql',
    'mysqli',
    'json',
    'curl',
    'mbstring',
    'openssl',
    'zip',
    'gd'
];

foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "   $status $ext\n";
}

echo "\n📊 PHP Configuration:\n";
echo "   - Memory Limit: " . ini_get('memory_limit') . "\n";
echo "   - Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "   - Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "   - Post Max Size: " . ini_get('post_max_size') . "\n";
echo "   - Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "\n";

echo "\n🌐 WordPress Compatibility:\n";
$wp_compatible = version_compare(PHP_VERSION, '7.4.0', '>=');
echo "   " . ($wp_compatible ? '✅' : '❌') . " WordPress Compatible (PHP 7.4+)\n";

echo "\n🔒 Security Features:\n";
echo "   - HTTPS: " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'Yes' : 'No') . "\n";
echo "   - Session Support: " . (function_exists('session_start') ? 'Yes' : 'No') . "\n";

echo "\n📁 File System:\n";
$writable_dirs = [
    $_SERVER['DOCUMENT_ROOT'],
    sys_get_temp_dir()
];

foreach ($writable_dirs as $dir) {
    $writable = is_writable($dir);
    $status = $writable ? '✅' : '❌';
    echo "   $status " . basename($dir) . " (" . $dir . ")\n";
}

echo "\n🎯 ChatGABI Readiness:\n";
$all_good = true;

// Check critical requirements
$checks = [
    'PHP 7.4+' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'JSON' => extension_loaded('json'),
    'cURL' => extension_loaded('curl'),
    'Document Root Writable' => is_writable($_SERVER['DOCUMENT_ROOT'])
];

foreach ($checks as $check => $result) {
    $status = $result ? '✅' : '❌';
    echo "   $status $check\n";
    if (!$result) $all_good = false;
}

echo "\n" . ($all_good ? '🎉 All systems ready for ChatGABI!' : '⚠️ Some requirements need attention') . "\n";

echo "\n📋 Next Steps:\n";
echo "   1. Import chatgabi-test-setup.sql in phpMyAdmin\n";
echo "   2. Install WordPress in this directory\n";
echo "   3. Upload ChatGABI theme files\n";
echo "   4. Run comprehensive tests\n";
?>
