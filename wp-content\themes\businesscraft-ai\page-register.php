<?php
/**
 * Template Name: Register
 * 
 * User registration page for ChatGABI
 * Custom registration with business information and free credits
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url('/dashboard'));
    exit;
}

// Handle registration form submission
$registration_message = '';
$registration_status = '';
$form_data = array();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['chatgabi_register_submit'])) {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['chatgabi_register_nonce'], 'chatgabi_register_form')) {
        $registration_message = __('Security check failed. Please try again.', 'chatgabi');
        $registration_status = 'error';
    } else {
        // Sanitize and validate form data
        $form_data = array(
            'username' => sanitize_user($_POST['register_username']),
            'email' => sanitize_email($_POST['register_email']),
            'password' => $_POST['register_password'],
            'confirm_password' => $_POST['register_confirm_password'],
            'first_name' => sanitize_text_field($_POST['register_first_name']),
            'last_name' => sanitize_text_field($_POST['register_last_name']),
            'country' => sanitize_text_field($_POST['register_country']),
            'business_sector' => sanitize_text_field($_POST['register_business_sector']),
            'business_stage' => sanitize_text_field($_POST['register_business_stage']),
            'company_name' => sanitize_text_field($_POST['register_company_name']),
            'phone' => sanitize_text_field($_POST['register_phone']),
            'terms_accepted' => isset($_POST['register_terms'])
        );
        
        // Validation
        $errors = array();
        
        if (empty($form_data['username'])) {
            $errors[] = __('Username is required.', 'chatgabi');
        } elseif (username_exists($form_data['username'])) {
            $errors[] = __('Username already exists.', 'chatgabi');
        }
        
        if (empty($form_data['email']) || !is_email($form_data['email'])) {
            $errors[] = __('Valid email address is required.', 'chatgabi');
        } elseif (email_exists($form_data['email'])) {
            $errors[] = __('Email address already registered.', 'chatgabi');
        }
        
        if (empty($form_data['password']) || strlen($form_data['password']) < 8) {
            $errors[] = __('Password must be at least 8 characters long.', 'chatgabi');
        }
        
        if ($form_data['password'] !== $form_data['confirm_password']) {
            $errors[] = __('Passwords do not match.', 'chatgabi');
        }
        
        if (empty($form_data['first_name']) || empty($form_data['last_name'])) {
            $errors[] = __('First name and last name are required.', 'chatgabi');
        }
        
        if (empty($form_data['country'])) {
            $errors[] = __('Country selection is required.', 'chatgabi');
        }
        
        if (!$form_data['terms_accepted']) {
            $errors[] = __('You must accept the Terms of Service and Privacy Policy.', 'chatgabi');
        }
        
        if (empty($errors)) {
            // Create user account
            $user_id = wp_create_user($form_data['username'], $form_data['password'], $form_data['email']);
            
            if (!is_wp_error($user_id)) {
                // Update user meta with additional information
                update_user_meta($user_id, 'first_name', $form_data['first_name']);
                update_user_meta($user_id, 'last_name', $form_data['last_name']);
                update_user_meta($user_id, 'country', $form_data['country']);
                update_user_meta($user_id, 'business_sector', $form_data['business_sector']);
                update_user_meta($user_id, 'business_stage', $form_data['business_stage']);
                update_user_meta($user_id, 'company_name', $form_data['company_name']);
                update_user_meta($user_id, 'phone', $form_data['phone']);
                update_user_meta($user_id, 'registration_date', current_time('mysql'));
                
                // Award free credits
                update_user_meta($user_id, 'businesscraft_credits', 25);
                
                // Log credit transaction
                global $wpdb;
                $wpdb->insert(
                    $wpdb->prefix . 'businesscraft_credit_transactions',
                    array(
                        'user_id' => $user_id,
                        'amount' => 25,
                        'transaction_type' => 'registration_bonus',
                        'description' => 'Free credits for new user registration',
                        'created_at' => current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s')
                );
                
                // Send welcome email
                $subject = __('Welcome to ChatGABI - Your 25 Free Credits Await!', 'chatgabi');
                $message = sprintf(
                    __('Welcome to ChatGABI, %s!

Thank you for joining our community of African entrepreneurs. Your account has been created successfully and you have received 25 free credits to explore all our AI-powered business intelligence features.

Your Account Details:
- Username: %s
- Email: %s
- Country: %s
- Free Credits: 25

Get Started:
1. Login to your dashboard: %s
2. Explore our AI chat features
3. Generate business templates
4. Analyze market opportunities

Need help? Contact <NAME_EMAIL>

Best regards,
The ChatGABI Team
Swiftmind - AI for Africa', 'chatgabi'),
                    $form_data['first_name'],
                    $form_data['username'],
                    $form_data['email'],
                    $form_data['country'],
                    home_url('/dashboard')
                );
                
                wp_mail($form_data['email'], $subject, $message);
                
                $registration_message = __('Registration successful! You have received 25 free credits. Please check your email for login details.', 'chatgabi');
                $registration_status = 'success';
                
                // Clear form data on success
                $form_data = array();
                
                // Auto-login user
                wp_set_current_user($user_id);
                wp_set_auth_cookie($user_id);
                
                // Redirect to dashboard after 3 seconds
                echo '<script>setTimeout(function(){ window.location.href = "' . home_url('/dashboard') . '"; }, 3000);</script>';
                
            } else {
                $registration_message = __('Registration failed: ', 'chatgabi') . $user_id->get_error_message();
                $registration_status = 'error';
            }
        } else {
            $registration_message = implode('<br>', $errors);
            $registration_status = 'error';
        }
    }
}

get_header();
?>

<div class="chatgabi-register-page">
    <div class="container">
        <!-- Registration Hero Section -->
        <div class="register-hero glassmorphism-card">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-icon">🚀</span>
                    <?php _e('Join ChatGABI', 'chatgabi'); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php _e('Create your account and receive 25 free credits to explore AI-powered business intelligence designed for African entrepreneurs.', 'chatgabi'); ?>
                </p>
                
                <div class="registration-benefits">
                    <div class="benefit-item">
                        <span class="benefit-icon">🎁</span>
                        <span><?php _e('25 Free Credits', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🤖</span>
                        <span><?php _e('AI Business Intelligence', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🌍</span>
                        <span><?php _e('African Market Focus', 'chatgabi'); ?></span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🔒</span>
                        <span><?php _e('Secure & Private', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="registration-content">
            <!-- Registration Form -->
            <div class="registration-form-section glassmorphism-card">
                <div class="form-header">
                    <h2><?php _e('Create Your Account', 'chatgabi'); ?></h2>
                    <p><?php _e('Fill in your details to get started with ChatGABI', 'chatgabi'); ?></p>
                </div>
                
                <?php if ($registration_message): ?>
                    <div class="registration-message <?php echo esc_attr($registration_status); ?>">
                        <p><?php echo wp_kses_post($registration_message); ?></p>
                        <?php if ($registration_status === 'success'): ?>
                            <p><small><?php _e('Redirecting to dashboard in 3 seconds...', 'chatgabi'); ?></small></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <form method="post" class="registration-form" novalidate>
                    <?php wp_nonce_field('chatgabi_register_form', 'chatgabi_register_nonce'); ?>
                    
                    <!-- Personal Information -->
                    <div class="form-section">
                        <h3><?php _e('Personal Information', 'chatgabi'); ?></h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="register_first_name"><?php _e('First Name', 'chatgabi'); ?> <span class="required">*</span></label>
                                <input type="text" 
                                       id="register_first_name" 
                                       name="register_first_name" 
                                       value="<?php echo esc_attr($form_data['first_name'] ?? ''); ?>"
                                       required 
                                       placeholder="<?php esc_attr_e('Enter your first name', 'chatgabi'); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="register_last_name"><?php _e('Last Name', 'chatgabi'); ?> <span class="required">*</span></label>
                                <input type="text" 
                                       id="register_last_name" 
                                       name="register_last_name" 
                                       value="<?php echo esc_attr($form_data['last_name'] ?? ''); ?>"
                                       required 
                                       placeholder="<?php esc_attr_e('Enter your last name', 'chatgabi'); ?>">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="register_email"><?php _e('Email Address', 'chatgabi'); ?> <span class="required">*</span></label>
                                <input type="email" 
                                       id="register_email" 
                                       name="register_email" 
                                       value="<?php echo esc_attr($form_data['email'] ?? ''); ?>"
                                       required 
                                       placeholder="<?php esc_attr_e('Enter your email address', 'chatgabi'); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="register_phone"><?php _e('Phone Number', 'chatgabi'); ?></label>
                                <input type="tel" 
                                       id="register_phone" 
                                       name="register_phone" 
                                       value="<?php echo esc_attr($form_data['phone'] ?? ''); ?>"
                                       placeholder="<?php esc_attr_e('Enter your phone number', 'chatgabi'); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="form-section">
                        <h3><?php _e('Account Information', 'chatgabi'); ?></h3>
                        
                        <div class="form-group">
                            <label for="register_username"><?php _e('Username', 'chatgabi'); ?> <span class="required">*</span></label>
                            <input type="text" 
                                   id="register_username" 
                                   name="register_username" 
                                   value="<?php echo esc_attr($form_data['username'] ?? ''); ?>"
                                   required 
                                   placeholder="<?php esc_attr_e('Choose a unique username', 'chatgabi'); ?>">
                            <small class="form-help"><?php _e('Username must be unique and contain only letters, numbers, and underscores', 'chatgabi'); ?></small>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="register_password"><?php _e('Password', 'chatgabi'); ?> <span class="required">*</span></label>
                                <input type="password" 
                                       id="register_password" 
                                       name="register_password" 
                                       required 
                                       placeholder="<?php esc_attr_e('Create a strong password', 'chatgabi'); ?>">
                                <small class="form-help"><?php _e('Minimum 8 characters with letters and numbers', 'chatgabi'); ?></small>
                            </div>
                            
                            <div class="form-group">
                                <label for="register_confirm_password"><?php _e('Confirm Password', 'chatgabi'); ?> <span class="required">*</span></label>
                                <input type="password" 
                                       id="register_confirm_password" 
                                       name="register_confirm_password" 
                                       required 
                                       placeholder="<?php esc_attr_e('Confirm your password', 'chatgabi'); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Business Information -->
                    <div class="form-section">
                        <h3><?php _e('Business Information', 'chatgabi'); ?></h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="register_country"><?php _e('Country', 'chatgabi'); ?> <span class="required">*</span></label>
                                <select id="register_country" name="register_country" required>
                                    <option value=""><?php _e('Select your country', 'chatgabi'); ?></option>
                                    <option value="Ghana" <?php selected($form_data['country'] ?? '', 'Ghana'); ?>><?php _e('🇬🇭 Ghana', 'chatgabi'); ?></option>
                                    <option value="Kenya" <?php selected($form_data['country'] ?? '', 'Kenya'); ?>><?php _e('🇰🇪 Kenya', 'chatgabi'); ?></option>
                                    <option value="Nigeria" <?php selected($form_data['country'] ?? '', 'Nigeria'); ?>><?php _e('🇳🇬 Nigeria', 'chatgabi'); ?></option>
                                    <option value="South Africa" <?php selected($form_data['country'] ?? '', 'South Africa'); ?>><?php _e('🇿🇦 South Africa', 'chatgabi'); ?></option>
                                    <option value="Other" <?php selected($form_data['country'] ?? '', 'Other'); ?>><?php _e('🌍 Other African Country', 'chatgabi'); ?></option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="register_business_stage"><?php _e('Business Stage', 'chatgabi'); ?></label>
                                <select id="register_business_stage" name="register_business_stage">
                                    <option value=""><?php _e('Select business stage', 'chatgabi'); ?></option>
                                    <option value="Idea Stage" <?php selected($form_data['business_stage'] ?? '', 'Idea Stage'); ?>><?php _e('💡 Idea Stage', 'chatgabi'); ?></option>
                                    <option value="Startup" <?php selected($form_data['business_stage'] ?? '', 'Startup'); ?>><?php _e('🚀 Startup (0-2 years)', 'chatgabi'); ?></option>
                                    <option value="Growth" <?php selected($form_data['business_stage'] ?? '', 'Growth'); ?>><?php _e('📈 Growth Stage (2-5 years)', 'chatgabi'); ?></option>
                                    <option value="Established" <?php selected($form_data['business_stage'] ?? '', 'Established'); ?>><?php _e('🏢 Established (5+ years)', 'chatgabi'); ?></option>
                                    <option value="Consultant" <?php selected($form_data['business_stage'] ?? '', 'Consultant'); ?>><?php _e('👨‍💼 Consultant/Freelancer', 'chatgabi'); ?></option>
                                    <option value="Student" <?php selected($form_data['business_stage'] ?? '', 'Student'); ?>><?php _e('🎓 Student/Researcher', 'chatgabi'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="register_business_sector"><?php _e('Business Sector', 'chatgabi'); ?></label>
                                <select id="register_business_sector" name="register_business_sector">
                                    <option value=""><?php _e('Select your business sector', 'chatgabi'); ?></option>
                                    <optgroup label="<?php esc_attr_e('Technology', 'chatgabi'); ?>">
                                        <option value="Software Development" <?php selected($form_data['business_sector'] ?? '', 'Software Development'); ?>><?php _e('Software Development', 'chatgabi'); ?></option>
                                        <option value="Mobile Apps" <?php selected($form_data['business_sector'] ?? '', 'Mobile Apps'); ?>><?php _e('Mobile Apps', 'chatgabi'); ?></option>
                                        <option value="Fintech" <?php selected($form_data['business_sector'] ?? '', 'Fintech'); ?>><?php _e('Fintech', 'chatgabi'); ?></option>
                                        <option value="E-commerce" <?php selected($form_data['business_sector'] ?? '', 'E-commerce'); ?>><?php _e('E-commerce', 'chatgabi'); ?></option>
                                    </optgroup>
                                    <optgroup label="<?php esc_attr_e('Agriculture', 'chatgabi'); ?>">
                                        <option value="Crop Production" <?php selected($form_data['business_sector'] ?? '', 'Crop Production'); ?>><?php _e('Crop Production', 'chatgabi'); ?></option>
                                        <option value="Livestock" <?php selected($form_data['business_sector'] ?? '', 'Livestock'); ?>><?php _e('Livestock', 'chatgabi'); ?></option>
                                        <option value="Agritech" <?php selected($form_data['business_sector'] ?? '', 'Agritech'); ?>><?php _e('Agritech', 'chatgabi'); ?></option>
                                    </optgroup>
                                    <optgroup label="<?php esc_attr_e('Manufacturing', 'chatgabi'); ?>">
                                        <option value="Food Processing" <?php selected($form_data['business_sector'] ?? '', 'Food Processing'); ?>><?php _e('Food Processing', 'chatgabi'); ?></option>
                                        <option value="Textiles" <?php selected($form_data['business_sector'] ?? '', 'Textiles'); ?>><?php _e('Textiles', 'chatgabi'); ?></option>
                                        <option value="Construction Materials" <?php selected($form_data['business_sector'] ?? '', 'Construction Materials'); ?>><?php _e('Construction Materials', 'chatgabi'); ?></option>
                                    </optgroup>
                                    <optgroup label="<?php esc_attr_e('Services', 'chatgabi'); ?>">
                                        <option value="Consulting" <?php selected($form_data['business_sector'] ?? '', 'Consulting'); ?>><?php _e('Consulting', 'chatgabi'); ?></option>
                                        <option value="Education" <?php selected($form_data['business_sector'] ?? '', 'Education'); ?>><?php _e('Education', 'chatgabi'); ?></option>
                                        <option value="Healthcare" <?php selected($form_data['business_sector'] ?? '', 'Healthcare'); ?>><?php _e('Healthcare', 'chatgabi'); ?></option>
                                        <option value="Tourism" <?php selected($form_data['business_sector'] ?? '', 'Tourism'); ?>><?php _e('Tourism', 'chatgabi'); ?></option>
                                    </optgroup>
                                    <optgroup label="<?php esc_attr_e('Energy & Environment', 'chatgabi'); ?>">
                                        <option value="Renewable Energy" <?php selected($form_data['business_sector'] ?? '', 'Renewable Energy'); ?>><?php _e('Renewable Energy', 'chatgabi'); ?></option>
                                        <option value="Waste Management" <?php selected($form_data['business_sector'] ?? '', 'Waste Management'); ?>><?php _e('Waste Management', 'chatgabi'); ?></option>
                                        <option value="Water Solutions" <?php selected($form_data['business_sector'] ?? '', 'Water Solutions'); ?>><?php _e('Water Solutions', 'chatgabi'); ?></option>
                                    </optgroup>
                                    <option value="Other" <?php selected($form_data['business_sector'] ?? '', 'Other'); ?>><?php _e('Other', 'chatgabi'); ?></option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="register_company_name"><?php _e('Company/Business Name', 'chatgabi'); ?></label>
                                <input type="text"
                                       id="register_company_name"
                                       name="register_company_name"
                                       value="<?php echo esc_attr($form_data['company_name'] ?? ''); ?>"
                                       placeholder="<?php esc_attr_e('Enter your company or business name', 'chatgabi'); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-section">
                        <h3><?php _e('Terms and Conditions', 'chatgabi'); ?></h3>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       id="register_terms"
                                       name="register_terms"
                                       required
                                       <?php checked($form_data['terms_accepted'] ?? false, true); ?>>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <?php printf(
                                        __('I agree to the %s and %s', 'chatgabi'),
                                        '<a href="' . esc_url(home_url('/terms-of-service')) . '" target="_blank">' . __('Terms of Service', 'chatgabi') . '</a>',
                                        '<a href="' . esc_url(home_url('/privacy-policy')) . '" target="_blank">' . __('Privacy Policy', 'chatgabi') . '</a>'
                                    ); ?>
                                    <span class="required">*</span>
                                </span>
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       id="register_marketing"
                                       name="register_marketing"
                                       <?php checked($form_data['marketing_consent'] ?? false, true); ?>>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <?php _e('I would like to receive updates about new features, business insights, and special offers from ChatGABI', 'chatgabi'); ?>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-submit">
                        <button type="submit" name="chatgabi_register_submit" class="btn btn-primary btn-large">
                            <span class="btn-icon">🚀</span>
                            <?php _e('Create Account & Get 25 Free Credits', 'chatgabi'); ?>
                        </button>
                    </div>
                </form>

                <div class="form-footer">
                    <p><?php _e('Already have an account?', 'chatgabi'); ?>
                       <a href="<?php echo esc_url(home_url('/login')); ?>"><?php _e('Login here', 'chatgabi'); ?></a>
                    </p>
                </div>
            </div>

            <!-- Registration Benefits Sidebar -->
            <div class="registration-benefits-section glassmorphism-card">
                <div class="benefits-header">
                    <h3><?php _e('What You Get', 'chatgabi'); ?></h3>
                    <p><?php _e('Join thousands of African entrepreneurs already using ChatGABI', 'chatgabi'); ?></p>
                </div>

                <div class="benefits-list">
                    <div class="benefit-card">
                        <div class="benefit-icon">🎁</div>
                        <div class="benefit-content">
                            <h4><?php _e('25 Free Credits', 'chatgabi'); ?></h4>
                            <p><?php _e('Start exploring immediately with 25 free credits - no payment required', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🤖</div>
                        <div class="benefit-content">
                            <h4><?php _e('AI Business Intelligence', 'chatgabi'); ?></h4>
                            <p><?php _e('Access to advanced AI tools for market analysis, business planning, and document generation', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🌍</div>
                        <div class="benefit-content">
                            <h4><?php _e('African Market Focus', 'chatgabi'); ?></h4>
                            <p><?php _e('AI trained specifically on African business contexts, regulations, and opportunities', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🗣️</div>
                        <div class="benefit-content">
                            <h4><?php _e('Multi-Language Support', 'chatgabi'); ?></h4>
                            <p><?php _e('Communicate in English, Twi, Swahili, Yoruba, or Zulu with cultural context', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">📊</div>
                        <div class="benefit-content">
                            <h4><?php _e('Real-Time Analytics', 'chatgabi'); ?></h4>
                            <p><?php _e('Track your business intelligence usage and ROI with detailed analytics', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🔒</div>
                        <div class="benefit-content">
                            <h4><?php _e('Secure & Compliant', 'chatgabi'); ?></h4>
                            <p><?php _e('Enterprise-grade security with full compliance to African data protection laws', 'chatgabi'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-preview">
                    <div class="testimonial-content">
                        <p>"<?php _e('ChatGABI helped me create a comprehensive business plan that secured funding for my startup in just 2 weeks!', 'chatgabi'); ?>"</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Kwame Asante</strong>
                        <span><?php _e('Tech Entrepreneur, Accra', 'chatgabi'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Registration Page Specific Styles */
.chatgabi-register-page {
    padding: 40px 0;
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface) 100%);
    min-height: 100vh;
}

.glassmorphism-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-bottom: 40px;
}

/* Hero Section */
.register-hero {
    text-align: center;
    padding: 60px 40px;
}

.hero-title {
    font-size: 3.5rem;
    color: var(--color-primary-accent);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.hero-icon {
    font-size: 3.5rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.registration-benefits {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--color-text-primary);
    font-weight: 600;
}

.benefit-icon {
    font-size: 1.2rem;
    color: var(--color-nature-green);
}

/* Registration Content Layout */
.registration-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Form Styles */
.registration-form-section {
    padding: 40px;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header h2 {
    font-size: 2.2rem;
    color: var(--color-primary-accent);
    margin-bottom: 15px;
}

.form-header p {
    font-size: 1.1rem;
    color: var(--color-text-secondary);
}

.registration-message {
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    font-weight: 600;
}

.registration-message.success {
    background: rgba(39, 174, 96, 0.1);
    border: 2px solid #27ae60;
    color: #27ae60;
}

.registration-message.error {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid #e74c3c;
    color: #e74c3c;
}

.registration-form {
    max-width: 100%;
}

.form-section {
    margin-bottom: 40px;
    padding: 30px;
    background: rgba(var(--color-primary-accent), 0.03);
    border-radius: 12px;
    border-left: 4px solid var(--color-primary-accent);
}

.form-section h3 {
    color: var(--color-primary-accent);
    font-size: 1.5rem;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-text-primary);
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--color-borders);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-text-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--color-primary-accent);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.1);
}

.form-help {
    font-size: 0.85rem;
    color: var(--color-text-secondary);
    margin-top: 5px;
    display: block;
}

/* Checkbox Styles */
.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-borders);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    position: relative;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--color-primary-accent);
    border-color: var(--color-primary-accent);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark:after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.checkbox-text {
    color: var(--color-text-primary);
}

.checkbox-text a {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
}

.checkbox-text a:hover {
    text-decoration: underline;
}

/* Submit Button */
.form-submit {
    text-align: center;
    margin-top: 40px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-large {
    padding: 18px 40px;
    font-size: 1.2rem;
    width: 100%;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-secondary-accent) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1.2rem;
}

.form-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(var(--color-borders), 0.3);
}

.form-footer p {
    color: var(--color-text-secondary);
}

.form-footer a {
    color: var(--color-primary-accent);
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Benefits Sidebar */
.registration-benefits-section {
    padding: 30px;
}

.benefits-header {
    text-align: center;
    margin-bottom: 30px;
}

.benefits-header h3 {
    color: var(--color-primary-accent);
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.benefits-header p {
    color: var(--color-text-secondary);
    font-size: 1rem;
}

.benefits-list {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.benefit-card {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(var(--color-nature-green), 0.1);
    border-radius: 12px;
    border-left: 4px solid var(--color-nature-green);
}

.benefit-card .benefit-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.benefit-content h4 {
    color: var(--color-primary-accent);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.benefit-content p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.testimonial-preview {
    background: rgba(var(--color-secondary-accent), 0.1);
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid var(--color-secondary-accent);
}

.testimonial-content p {
    font-style: italic;
    color: var(--color-text-primary);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.testimonial-author strong {
    color: var(--color-primary-accent);
    display: block;
    margin-bottom: 5px;
}

.testimonial-author span {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chatgabi-register-page {
        padding: 20px 0;
    }

    .glassmorphism-card {
        padding: 25px 20px;
        margin-bottom: 25px;
    }

    .register-hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .registration-benefits {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .registration-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .registration-form-section {
        padding: 25px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-section {
        padding: 20px;
        margin-bottom: 25px;
    }

    .benefits-list {
        gap: 15px;
    }

    .benefit-card {
        padding: 15px;
    }
}

/* Dark Theme Support */
body.theme-dark .glassmorphism-card {
    background: rgba(26, 36, 58, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

body.theme-dark .hero-title,
body.theme-dark .form-header h2 {
    color: var(--color-secondary-accent);
}

body.theme-dark .form-section {
    background: rgba(110, 127, 243, 0.1);
}

body.theme-dark .form-group input,
body.theme-dark .form-group select {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--color-text-primary);
}

body.theme-dark .checkmark {
    background: rgba(26, 36, 58, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
}

body.theme-dark .benefit-card {
    background: rgba(39, 174, 96, 0.2);
}

body.theme-dark .testimonial-preview {
    background: rgba(255, 215, 0, 0.1);
}
</style>

<script>
// Registration Page JavaScript Functionality
document.addEventListener('DOMContentLoaded', function() {

    // Form validation
    const registrationForm = document.querySelector('.registration-form');
    const passwordField = document.getElementById('register_password');
    const confirmPasswordField = document.getElementById('register_confirm_password');
    const usernameField = document.getElementById('register_username');
    const emailField = document.getElementById('register_email');
    const termsCheckbox = document.getElementById('register_terms');
    const submitButton = document.querySelector('button[name="chatgabi_register_submit"]');

    // Password strength indicator
    function checkPasswordStrength(password) {
        let strength = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        Object.values(checks).forEach(check => {
            if (check) strength++;
        });

        return { strength, checks };
    }

    function updatePasswordStrength() {
        const password = passwordField.value;
        const result = checkPasswordStrength(password);

        // Remove existing strength indicator
        const existingIndicator = passwordField.parentNode.querySelector('.password-strength');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        if (password.length > 0) {
            const strengthIndicator = document.createElement('div');
            strengthIndicator.className = 'password-strength';

            let strengthText = '';
            let strengthClass = '';

            if (result.strength < 2) {
                strengthText = 'Weak';
                strengthClass = 'weak';
            } else if (result.strength < 4) {
                strengthText = 'Medium';
                strengthClass = 'medium';
            } else {
                strengthText = 'Strong';
                strengthClass = 'strong';
            }

            strengthIndicator.innerHTML = `
                <div class="strength-bar ${strengthClass}">
                    <div class="strength-fill" style="width: ${(result.strength / 5) * 100}%"></div>
                </div>
                <small class="strength-text ${strengthClass}">Password strength: ${strengthText}</small>
            `;

            passwordField.parentNode.appendChild(strengthIndicator);
        }
    }

    // Password confirmation validation
    function validatePasswordConfirmation() {
        const password = passwordField.value;
        const confirmPassword = confirmPasswordField.value;

        // Remove existing validation message
        const existingMessage = confirmPasswordField.parentNode.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (confirmPassword.length > 0 && password !== confirmPassword) {
            const validationMessage = document.createElement('small');
            validationMessage.className = 'validation-message error';
            validationMessage.textContent = 'Passwords do not match';
            confirmPasswordField.parentNode.appendChild(validationMessage);
            return false;
        }

        return true;
    }

    // Username availability check (simulated)
    function checkUsernameAvailability() {
        const username = usernameField.value;

        // Remove existing validation message
        const existingMessage = usernameField.parentNode.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (username.length >= 3) {
            // Simulate username check (in production, this would be an AJAX call)
            const validationMessage = document.createElement('small');
            validationMessage.className = 'validation-message checking';
            validationMessage.innerHTML = '⏳ Checking availability...';
            usernameField.parentNode.appendChild(validationMessage);

            setTimeout(() => {
                const isAvailable = !['admin', 'test', 'user', 'chatgabi'].includes(username.toLowerCase());
                validationMessage.className = `validation-message ${isAvailable ? 'success' : 'error'}`;
                validationMessage.innerHTML = isAvailable ?
                    '✅ Username available' :
                    '❌ Username not available';
            }, 1000);
        }
    }

    // Email validation
    function validateEmail() {
        const email = emailField.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        // Remove existing validation message
        const existingMessage = emailField.parentNode.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (email.length > 0 && !emailRegex.test(email)) {
            const validationMessage = document.createElement('small');
            validationMessage.className = 'validation-message error';
            validationMessage.textContent = 'Please enter a valid email address';
            emailField.parentNode.appendChild(validationMessage);
            return false;
        }

        return true;
    }

    // Event listeners
    if (passwordField) {
        passwordField.addEventListener('input', updatePasswordStrength);
    }

    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordConfirmation);
    }

    if (usernameField) {
        usernameField.addEventListener('blur', checkUsernameAvailability);
    }

    if (emailField) {
        emailField.addEventListener('blur', validateEmail);
    }

    // Form submission handling
    if (registrationForm) {
        registrationForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Validate all fields
            if (!validateEmail()) isValid = false;
            if (!validatePasswordConfirmation()) isValid = false;

            // Check password strength
            const passwordStrength = checkPasswordStrength(passwordField.value);
            if (passwordStrength.strength < 2) {
                alert('Please choose a stronger password with at least 8 characters including letters and numbers.');
                isValid = false;
            }

            // Check terms acceptance
            if (!termsCheckbox.checked) {
                alert('Please accept the Terms of Service and Privacy Policy to continue.');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="btn-icon">⏳</span> Creating Account...';
                submitButton.disabled = true;

                // Re-enable button after 10 seconds as fallback
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 10000);
            }
        });
    }

    // Auto-fill country based on user's location (if available)
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            // This would typically use a geolocation API to determine country
            // For now, we'll just focus on the form functionality
        });
    }

    // Dynamic business sector suggestions based on country
    const countrySelect = document.getElementById('register_country');
    const sectorSelect = document.getElementById('register_business_sector');

    if (countrySelect && sectorSelect) {
        countrySelect.addEventListener('change', function() {
            const country = this.value;

            // Add country-specific sector suggestions
            const countrySectors = {
                'Ghana': ['Cocoa Processing', 'Gold Mining', 'Oil & Gas', 'Mobile Money'],
                'Kenya': ['Tea & Coffee', 'Horticulture', 'Tourism', 'Mobile Banking'],
                'Nigeria': ['Oil & Gas', 'Nollywood', 'Banking', 'Telecommunications'],
                'South Africa': ['Mining', 'Wine', 'Automotive', 'Financial Services']
            };

            if (countrySectors[country]) {
                // Add country-specific options to the sector select
                const countryGroup = document.createElement('optgroup');
                countryGroup.label = `Popular in ${country}`;

                countrySectors[country].forEach(sector => {
                    const option = document.createElement('option');
                    option.value = sector;
                    option.textContent = sector;
                    countryGroup.appendChild(option);
                });

                // Remove existing country-specific group if it exists
                const existingGroup = sectorSelect.querySelector('optgroup[label*="Popular in"]');
                if (existingGroup) {
                    existingGroup.remove();
                }

                // Insert at the beginning
                sectorSelect.insertBefore(countryGroup, sectorSelect.firstChild);
            }
        });
    }

    // Smooth scrolling for form sections
    const formSections = document.querySelectorAll('.form-section');

    formSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // Benefits cards animation
    const benefitCards = document.querySelectorAll('.benefit-card');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, observerOptions);

    benefitCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateX(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 100}ms`;
        observer.observe(card);
    });

    // Add custom CSS for validation messages
    const style = document.createElement('style');
    style.textContent = `
        .password-strength {
            margin-top: 8px;
        }

        .strength-bar {
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .strength-bar.weak .strength-fill {
            background: #e74c3c;
        }

        .strength-bar.medium .strength-fill {
            background: #f39c12;
        }

        .strength-bar.strong .strength-fill {
            background: #27ae60;
        }

        .strength-text {
            font-size: 0.8rem;
            font-weight: 600;
        }

        .strength-text.weak {
            color: #e74c3c;
        }

        .strength-text.medium {
            color: #f39c12;
        }

        .strength-text.strong {
            color: #27ae60;
        }

        .validation-message {
            display: block;
            margin-top: 5px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .validation-message.error {
            color: #e74c3c;
        }

        .validation-message.success {
            color: #27ae60;
        }

        .validation-message.checking {
            color: #3498db;
        }
    `;
    document.head.appendChild(style);
});
</script>

<?php
// Add SEO meta tags
function chatgabi_register_meta() {
    $title = __('Register for ChatGABI - Get 25 Free Credits | AI Business Intelligence', 'chatgabi');
    $description = __('Create your ChatGABI account and receive 25 free credits to explore AI-powered business intelligence for African entrepreneurs. Join thousands of successful business owners.', 'chatgabi');

    echo '<meta name="description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:title" content="' . esc_attr($title) . '">';
    echo '<meta property="og:description" content="' . esc_attr($description) . '">';
    echo '<meta property="og:type" content="website">';
    echo '<meta name="twitter:card" content="summary">';
    echo '<meta name="twitter:title" content="' . esc_attr($title) . '">';
    echo '<meta name="twitter:description" content="' . esc_attr($description) . '">';
    echo '<meta name="robots" content="index, follow">';
}
add_action('wp_head', 'chatgabi_register_meta');

get_footer();
?>
