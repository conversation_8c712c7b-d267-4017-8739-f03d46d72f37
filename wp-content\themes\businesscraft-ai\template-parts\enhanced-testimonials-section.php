<?php
/**
 * Enhanced Testimonials Section Template Part
 * 
 * Displays expanded testimonials with business verification,
 * case studies, and interactive elements for conversion optimization.
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get testimonials data
$testimonials = chatgabi_get_testimonials_data();
$featured_testimonials = array_slice($testimonials, 0, 3); // Show first 3 initially
$user_country = chatgabi_get_user_country();
?>

<section class="enhanced-testimonials-section">
    <div class="container">
        
        <!-- Section Header -->
        <div class="testimonials-header">
            <h2><?php _e('Trusted by 10,000+ African Entrepreneurs', 'chatgabi'); ?></h2>
            <p class="testimonials-subtitle">
                <?php _e('Real businesses, real results. See how ChatGABI is transforming entrepreneurship across Africa.', 'chatgabi'); ?>
            </p>
            
            <!-- Success Stats -->
            <div class="success-stats">
                <div class="stat-item">
                    <span class="stat-number">$2.5M+</span>
                    <span class="stat-label"><?php _e('Funding Secured', 'chatgabi'); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label"><?php _e('Businesses Launched', 'chatgabi'); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4.9/5</span>
                    <span class="stat-label"><?php _e('Average Rating', 'chatgabi'); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">12</span>
                    <span class="stat-label"><?php _e('Countries Served', 'chatgabi'); ?></span>
                </div>
            </div>
        </div>
        
        <!-- Testimonials Filter -->
        <div class="testimonials-filter">
            <button class="filter-btn active" data-filter="all">
                <?php _e('All Stories', 'chatgabi'); ?>
            </button>
            <button class="filter-btn" data-filter="GH">
                🇬🇭 <?php _e('Ghana', 'chatgabi'); ?>
            </button>
            <button class="filter-btn" data-filter="KE">
                🇰🇪 <?php _e('Kenya', 'chatgabi'); ?>
            </button>
            <button class="filter-btn" data-filter="NG">
                🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?>
            </button>
            <button class="filter-btn" data-filter="ZA">
                🇿🇦 <?php _e('South Africa', 'chatgabi'); ?>
            </button>
        </div>
        
        <!-- Main Testimonials Grid -->
        <div class="testimonials-grid" id="testimonials-grid">
            <?php foreach ($featured_testimonials as $testimonial): ?>
                <div class="testimonial-card enhanced" data-country="<?php echo esc_attr($testimonial['country_code']); ?>" data-business="<?php echo esc_attr($testimonial['business_type']); ?>">
                    
                    <!-- Testimonial Header -->
                    <div class="testimonial-header">
                        <div class="testimonial-avatar-section">
                            <img src="<?php echo esc_url($testimonial['avatar']); ?>" 
                                 alt="<?php echo esc_attr($testimonial['name']); ?>" 
                                 class="testimonial-avatar"
                                 loading="lazy">
                            <?php if ($testimonial['verified']): ?>
                                <div class="verified-badge" title="<?php _e('Verified Business', 'chatgabi'); ?>">
                                    <span class="verified-icon">✓</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="testimonial-info">
                            <h4 class="testimonial-name"><?php echo esc_html($testimonial['name']); ?></h4>
                            <p class="testimonial-title"><?php echo esc_html($testimonial['title']); ?></p>
                            <p class="testimonial-location">
                                <span class="country-flag"><?php echo chatgabi_get_country_flag($testimonial['country_code']); ?></span>
                                <?php echo esc_html($testimonial['country']); ?>
                            </p>
                            <div class="business-type-tag">
                                <?php echo esc_html($testimonial['business_type']); ?>
                            </div>
                        </div>
                        
                        <?php if (!empty($testimonial['business_logo'])): ?>
                            <div class="business-logo">
                                <img src="<?php echo esc_url($testimonial['business_logo']); ?>" 
                                     alt="<?php echo esc_attr($testimonial['name']); ?> Business Logo" 
                                     loading="lazy">
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Rating Stars -->
                    <div class="testimonial-rating">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo $i <= $testimonial['rating'] ? 'filled' : ''; ?>">★</span>
                        <?php endfor; ?>
                    </div>
                    
                    <!-- Testimonial Quote -->
                    <blockquote class="testimonial-quote">
                        "<?php echo esc_html($testimonial['quote']); ?>"
                    </blockquote>
                    
                    <!-- Results Metrics -->
                    <?php if (!empty($testimonial['results'])): ?>
                        <div class="testimonial-results">
                            <h5><?php _e('Results Achieved:', 'chatgabi'); ?></h5>
                            <div class="results-grid">
                                <?php foreach ($testimonial['results'] as $metric => $value): ?>
                                    <div class="result-item">
                                        <span class="result-value"><?php echo esc_html($value); ?></span>
                                        <span class="result-label"><?php echo esc_html(str_replace('_', ' ', ucwords($metric, '_'))); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Action Buttons -->
                    <div class="testimonial-actions">
                        <?php if (!empty($testimonial['case_study_url'])): ?>
                            <a href="<?php echo esc_url($testimonial['case_study_url']); ?>" 
                               class="case-study-btn">
                                <span class="btn-icon">📊</span>
                                <?php _e('View Case Study', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($testimonial['business_url'])): ?>
                            <a href="<?php echo esc_url($testimonial['business_url']); ?>" 
                               target="_blank" 
                               rel="noopener noreferrer" 
                               class="business-link">
                                <span class="btn-icon">🔗</span>
                                <?php _e('Visit Business', 'chatgabi'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Load More Button -->
        <div class="testimonials-load-more">
            <button class="load-more-btn" id="load-more-testimonials">
                <span class="btn-text"><?php _e('Load More Success Stories', 'chatgabi'); ?></span>
                <span class="btn-icon">↓</span>
            </button>
            <p class="load-more-note">
                <?php _e('Showing 3 of 12+ verified testimonials', 'chatgabi'); ?>
            </p>
        </div>
        
        <!-- Video Testimonials Section -->
        <div class="video-testimonials-section">
            <h3><?php _e('Watch Success Stories', 'chatgabi'); ?></h3>
            <div class="video-testimonials-grid">
                <div class="video-testimonial">
                    <div class="video-thumbnail">
                        <img src="<?php echo CHATGABI_THEME_URL; ?>/assets/images/video-thumbnails/akosua-video.jpg" 
                             alt="Akosua Mensah Video Testimonial">
                        <div class="play-button">
                            <span class="play-icon">▶</span>
                        </div>
                    </div>
                    <div class="video-info">
                        <h4><?php _e('Akosua\'s Journey: From Idea to $15K Funding', 'chatgabi'); ?></h4>
                        <p><?php _e('Watch how ChatGABI helped Akosua secure funding for her textile business in Ghana.', 'chatgabi'); ?></p>
                        <span class="video-duration">3:45</span>
                    </div>
                </div>
                
                <div class="video-testimonial">
                    <div class="video-thumbnail">
                        <img src="<?php echo CHATGABI_THEME_URL; ?>/assets/images/video-thumbnails/james-video.jpg" 
                             alt="James Ochieng Video Testimonial">
                        <div class="play-button">
                            <span class="play-icon">▶</span>
                        </div>
                    </div>
                    <div class="video-info">
                        <h4><?php _e('James\' Tech Startup: $50K Series A Success', 'chatgabi'); ?></h4>
                        <p><?php _e('Discover how financial forecasting helped James close his Series A round in Kenya.', 'chatgabi'); ?></p>
                        <span class="video-duration">4:12</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Social Proof Indicators -->
        <div class="social-proof-indicators">
            <div class="proof-item">
                <span class="proof-icon">🏆</span>
                <span class="proof-text"><?php _e('Featured in TechCrunch Africa', 'chatgabi'); ?></span>
            </div>
            <div class="proof-item">
                <span class="proof-icon">🎖️</span>
                <span class="proof-text"><?php _e('Winner: Best AI Tool 2024', 'chatgabi'); ?></span>
            </div>
            <div class="proof-item">
                <span class="proof-icon">📈</span>
                <span class="proof-text"><?php _e('98% Customer Satisfaction', 'chatgabi'); ?></span>
            </div>
            <div class="proof-item">
                <span class="proof-icon">🌍</span>
                <span class="proof-text"><?php _e('Trusted Across 12 African Countries', 'chatgabi'); ?></span>
            </div>
        </div>
        
    </div>
</section>

<style>
/* Enhanced Testimonials Section Styles */
.enhanced-testimonials-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 3rem;
}

.testimonials-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--color-african-sky);
    margin-bottom: 1rem;
}

.testimonials-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Success Stats */
.success-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 900;
    color: var(--color-african-gold);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
}

/* Testimonials Filter */
.testimonials-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--color-african-sky);
    color: white;
    border-color: var(--color-african-sky);
}

/* Enhanced Testimonial Cards */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.testimonial-card.enhanced {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card.enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.testimonial-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.testimonial-avatar-section {
    position: relative;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--color-african-gold);
}

.verified-badge {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 24px;
    height: 24px;
    background: var(--color-african-nature);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

.verified-icon {
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.testimonial-info {
    flex: 1;
}

.testimonial-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 0.25rem;
}

.testimonial-title {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.testimonial-location {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.business-type-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--color-african-gold);
    color: #2c3e50;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.business-logo {
    width: 40px;
    height: 40px;
}

.business-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

/* Rating Stars */
.testimonial-rating {
    margin-bottom: 1rem;
}

.star {
    color: #ddd;
    font-size: 1.2rem;
    margin-right: 0.25rem;
}

.star.filled {
    color: var(--color-african-gold);
}

/* Quote */
.testimonial-quote {
    font-size: 1rem;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-style: italic;
    position: relative;
}

/* Results Metrics */
.testimonial-results {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.testimonial-results h5 {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 0.75rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
}

.result-item {
    text-align: center;
}

.result-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-african-nature);
    margin-bottom: 0.25rem;
}

.result-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: capitalize;
}

/* Action Buttons */
.testimonial-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.case-study-btn,
.business-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.case-study-btn {
    background: var(--color-african-sky);
    color: white;
}

.business-link {
    background: #f8f9fa;
    color: var(--color-african-sky);
    border: 1px solid #dee2e6;
}

.case-study-btn:hover,
.business-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Load More Section */
.testimonials-load-more {
    text-align: center;
    margin-bottom: 3rem;
}

.load-more-btn {
    background: linear-gradient(135deg, var(--color-african-gold), var(--color-african-sunset));
    color: #2c3e50;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.load-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.load-more-note {
    margin-top: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Video Testimonials */
.video-testimonials-section {
    margin-bottom: 3rem;
}

.video-testimonials-section h3 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 2rem;
}

.video-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-testimonial {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.video-testimonial:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.video-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(0,0,0,0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: var(--color-african-sky);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
    color: white;
    font-size: 1.5rem;
    margin-left: 3px;
}

.video-info {
    padding: 1.5rem;
}

.video-info h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-african-sky);
    margin-bottom: 0.5rem;
}

.video-info p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.video-duration {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--color-african-gold);
    color: #2c3e50;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Social Proof Indicators */
.social-proof-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.proof-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
}

.proof-icon {
    font-size: 1.5rem;
}

.proof-text {
    font-weight: 600;
    color: var(--color-african-sky);
}

/* Responsive Design */
@media (max-width: 768px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonial-header {
        flex-direction: column;
        text-align: center;
    }
    
    .testimonial-actions {
        justify-content: center;
    }
    
    .success-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .video-testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .social-proof-indicators {
        grid-template-columns: 1fr;
    }
}
</style>

<?php
/**
 * Helper function to get country flag emoji
 */
function chatgabi_get_country_flag($country_code) {
    $flags = array(
        'GH' => '🇬🇭',
        'KE' => '🇰🇪',
        'NG' => '🇳🇬',
        'ZA' => '🇿🇦',
        'CI' => '🇨🇮'
    );
    
    return $flags[$country_code] ?? '🌍';
}
?>
