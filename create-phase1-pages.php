<?php
/**
 * Create Phase 1 Essential Pages
 * 
 * Script to create all Phase 1 pages in WordPress
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>ChatGABI Phase 1 - Page Creation</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; font-weight: bold; }";
echo ".error { color: #e74c3c; font-weight: bold; }";
echo ".info { color: #3498db; font-weight: bold; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo ".page-item { margin: 15px 0; padding: 15px; border-left: 4px solid #3498db; background: #f8f9fa; }";
echo "</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🚀 ChatGABI Phase 1: Page Creation</h1>";
echo "<p><strong>Creation Time:</strong> " . current_time('F j, Y g:i A') . "</p>";

// Define Phase 1 pages
$phase1_pages = array(
    'privacy-policy' => array(
        'title' => 'Privacy Policy',
        'template' => 'page-privacy-policy.php',
        'content' => 'Privacy Policy content will be displayed using the custom template.'
    ),
    'terms-of-service' => array(
        'title' => 'Terms of Service',
        'template' => 'page-terms-of-service.php',
        'content' => 'Terms of Service content will be displayed using the custom template.'
    ),
    'refund-policy' => array(
        'title' => 'Refund Policy',
        'template' => 'page-refund-policy.php',
        'content' => 'Refund Policy content will be displayed using the custom template.'
    ),
    'cookie-policy' => array(
        'title' => 'Cookie Policy',
        'template' => 'page-cookie-policy.php',
        'content' => 'Cookie Policy content will be displayed using the custom template.'
    ),
    'about' => array(
        'title' => 'About Us',
        'template' => 'page-about.php',
        'content' => 'About Us content will be displayed using the custom template.'
    ),
    'contact' => array(
        'title' => 'Contact',
        'template' => 'page-contact.php',
        'content' => 'Contact page content will be displayed using the custom template.'
    )
);

$created_count = 0;
$updated_count = 0;
$error_count = 0;

foreach ($phase1_pages as $slug => $page_data) {
    echo "<div class='page-item'>";
    echo "<h3>📄 {$page_data['title']} ({$slug})</h3>";
    
    // Check if page already exists
    $existing_page = get_page_by_path($slug);
    
    if ($existing_page) {
        echo "<p class='info'>ℹ️ Page already exists (ID: {$existing_page->ID})</p>";
        
        // Update template assignment
        $current_template = get_page_template_slug($existing_page->ID);
        if ($current_template !== $page_data['template']) {
            update_post_meta($existing_page->ID, '_wp_page_template', $page_data['template']);
            echo "<p class='success'>✅ Template updated to: {$page_data['template']}</p>";
            $updated_count++;
        } else {
            echo "<p class='success'>✅ Template already correct: {$page_data['template']}</p>";
        }
        
        // Ensure page is published
        if ($existing_page->post_status !== 'publish') {
            wp_update_post(array(
                'ID' => $existing_page->ID,
                'post_status' => 'publish'
            ));
            echo "<p class='success'>✅ Page status updated to published</p>";
        }
        
        echo "<p class='info'>🔗 <a href='" . get_permalink($existing_page->ID) . "' target='_blank'>View Page</a></p>";
        
    } else {
        // Create new page
        $page_args = array(
            'post_title' => $page_data['title'],
            'post_content' => $page_data['content'],
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => $slug,
            'post_author' => 1
        );
        
        $page_id = wp_insert_post($page_args);
        
        if ($page_id && !is_wp_error($page_id)) {
            echo "<p class='success'>✅ Page created successfully (ID: {$page_id})</p>";
            
            // Set page template
            update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            echo "<p class='success'>✅ Template assigned: {$page_data['template']}</p>";
            
            echo "<p class='info'>🔗 <a href='" . get_permalink($page_id) . "' target='_blank'>View Page</a></p>";
            
            $created_count++;
        } else {
            echo "<p class='error'>❌ Failed to create page</p>";
            if (is_wp_error($page_id)) {
                echo "<p class='error'>Error: " . $page_id->get_error_message() . "</p>";
            }
            $error_count++;
        }
    }
    
    echo "</div>";
}

// Summary
echo "<div style='margin-top: 30px; padding: 20px; background: #ecf0f1; border-radius: 8px;'>";
echo "<h2>📊 Creation Summary</h2>";
echo "<p><strong>Pages Created:</strong> {$created_count}</p>";
echo "<p><strong>Pages Updated:</strong> {$updated_count}</p>";
echo "<p><strong>Errors:</strong> {$error_count}</p>";

if ($error_count === 0) {
    echo "<p class='success'>🎉 All Phase 1 pages are ready!</p>";
} else {
    echo "<p class='error'>⚠️ Some issues occurred during page creation.</p>";
}

echo "</div>";

// Flush rewrite rules to ensure clean URLs
flush_rewrite_rules();
echo "<p class='info'>🔄 Rewrite rules flushed for clean URLs</p>";

echo "</div>";
echo "</body></html>";
?>
